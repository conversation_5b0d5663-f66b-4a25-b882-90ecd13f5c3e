<div class="breadcrumb">
  <span class="breadcrumb-item">Dashboard</span> >
  <span class="breadcrumb-item">List of Schemes</span> > Application
  <span class="required-info"
    >All <span class="required-mark">*</span> marked fields are mandatory</span
  >
</div>

<hr class="separator" />

<form
  [formGroup]="form"
  [ngClass]="{ 'disabled-form': isViewMode }"
  (ngSubmit)="onSubmit()"
>
  <div class="form-header">
    <span class="form-title"
      >Application For Scheme For Funeral Assistance In Case Of Death Of
      Registered Construction Worker</span
    >
  </div>

  <div class="form-grid ">
    <!-- Dynamic Field Generation using DRY principle -->
    <div class="form-field" *ngFor="let config of fieldConfigs">
      <!-- Text Input Fields -->
      <app-ilogi-input
        *ngIf="config.type === 'text'"
        [type]="config.type"
        [submitted]="submitted"
        [maxlength]="config.maxlength ?? null"
        [mandatory]="isConditionallyMandatory(config)"
        [fieldLabel]="config.label"
        [placeholder]="config.placeholder"
        [fieldId]="config.key"
        [errorMessages]="config.errorMessages"
        [formControlName]="config.key"
        [readonly]="isViewMode"
        [errors]="getFieldErrors(config.key)"
      >
      </app-ilogi-input>

      <!-- Textarea Fields -->
      <app-ilogi-input
        *ngIf="config.type === 'textarea'"
        [type]="config.type"
        [submitted]="submitted"
        [mandatory]="isConditionallyMandatory(config)"
        [fieldLabel]="config.label"
        [placeholder]="config.placeholder"
        [fieldId]="config.key"
        [errorMessages]="config.errorMessages"
        [formControlName]="config.key"
        [readonly]="isViewMode"
        [errors]="getFieldErrors(config.key)"
        [rows]="config.rows !== undefined ? config.rows.toString() : undefined"
      >
      </app-ilogi-input>

      <!-- Select Fields -->
      <app-ilogi-select
        *ngIf="config.type === 'select'"
        [submitted]="submitted"
        [mandatory]="isConditionallyMandatory(config)"
        [fieldLabel]="config.label"
        [placeholder]="config.placeholder"
        [fieldId]="config.key"
        [errorMessages]="config.errorMessages"
        [formControlName]="config.key"
        [readonly]="isViewMode"
        [errors]="getFieldErrors(config.key)"
      >
      </app-ilogi-select>

      <!-- Date Fields -->
      <app-ilogi-input-date
        *ngIf="config.type === 'date'"
        [submitted]="submitted"
        [mandatory]="isConditionallyMandatory(config)"
        [fieldLabel]="config.label"
        [placeholder]="config.placeholder"
        [fieldId]="config.key"
        [errorMessages]="config.errorMessages"
        [formControlName]="config.key"
        [errors]="getFieldErrors(config.key)"
        [monthsRange]="config.dateConfig?.monthsRange || 6"
        [futureDateErrorMessage]="
          config.dateConfig?.futureDateErrorMessage ||
          'Date cannot be in the future.'
        "
        [pastDateErrorMessage]="
          config.dateConfig?.pastDateErrorMessage ||
          'Date must be within the specified range.'
        "
        [readonly]="isViewMode"
      >
      </app-ilogi-input-date>
    </div>
  </div>

  <div class="form-actions">
    <button *ngIf="!isViewMode" type="submit" class="btn btn-primary">
      Submit
    </button>
    <button
      *ngIf="isViewMode"
      type="button"
      (click)="backClicked()"
      class="btn btn-secondary"
    >
      Back
    </button>
  </div>
</form>

<style>
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }

  .main-container {
    background: #ffffff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .breadcrumb {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    font-size: 14px;
  }

  .breadcrumb-item {
    font-weight: bold;
    color: #007bff;
    margin-right: 5px;
  }

  .required-info {
    margin-left: auto;
    font-size: 12px;
    color: #666;
  }

  .required-mark {
    color: #dc3545;
    font-weight: bold;
  }

  .separator {
    border: none;
    height: 1px;
    background-color: #e9ecef;
    margin: 20px 0;
  }

  .form-header {
    margin: 20px 0;
  }

  .form-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
  }

  .form-field {
    display: flex;
    flex-direction: column;
  }

  .form-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin: 40px 0;
  }

  .btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .btn-primary {
    background-color: #007bff;
    color: white;
  }

  .btn-primary:hover {
    background-color: #0056b3;
  }

  .btn-secondary {
    background-color: #6c757d;
    color: white;
  }

  .btn-secondary:hover {
    background-color: #545b62;
  }

  .disabled-form {
    pointer-events: none;
    opacity: 0.7;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .form-grid {
      grid-template-columns: 1fr;
    }

    .breadcrumb {
      flex-direction: column;
      align-items: flex-start;
    }

    .required-info {
      margin-left: 0;
      margin-top: 10px;
    }
  }

  @media (max-width: 480px) {
    .container {
      padding: 10px;
    }

    .main-container {
      padding: 15px;
    }

    .form-actions {
      flex-direction: column;
    }

    .btn {
      width: 100%;
    }
  }
</style>
