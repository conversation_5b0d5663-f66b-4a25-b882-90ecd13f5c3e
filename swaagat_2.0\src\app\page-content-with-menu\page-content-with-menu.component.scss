$sidebar-width-expanded: 320px;
$sidebar-width-collapsed: 70px;
$top-navbar-height: 40px; 
$transition-speed: 0.3s;
$mobile-breakpoint: 1024px;

.page-layout {
  position: relative;
  transition: padding-left $transition-speed ease-in-out;
  
  padding-top: $top-navbar-height;

  
  padding-left: $sidebar-width-expanded;

  &.sidebar-collapsed {
    padding-left: $sidebar-width-collapsed;
  }
}

.main-content {
  width: 100%;
  min-height: calc(100vh - #{$top-navbar-height});
  background-color: var(--color-primary, #f8f9fa);
  
  .content-wrapper {
    padding: 1.5rem;
  }
}


@media (max-width: $mobile-breakpoint) {
  .page-layout {
    padding-left: 0;
    
    &.sidebar-collapsed {
      padding-left: 0;
    }
    
    .content-wrapper {
      padding: 1rem;
    }
  }
}

.sidebar-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999; 
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}


body.sidebar-open {
  @media (max-width: $mobile-breakpoint) {
    overflow: hidden;
  }
}