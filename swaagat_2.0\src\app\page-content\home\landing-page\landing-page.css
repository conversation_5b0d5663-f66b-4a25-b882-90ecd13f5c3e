/* Modern Font Import */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

.hero-section {
  position: relative;
  width: 100%;
  height: 90vh;
  overflow: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Image Slider Background */
.slider-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0;
  transition: opacity 1s ease-in-out;
}

.slide.active {
  opacity: 1;
}

/* Enhanced Gradient Overlay for better contrast */
/* .gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
    rgba(0, 0, 0, 0.75) 0%,
    rgba(0, 0, 0, 0.45) 50%,
    rgba(0, 0, 0, 0.7) 100%);
  z-index: 2;
} */

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
    rgba(175, 114, 0, 0.884) 0%,
    rgba(126, 126, 126, 0.45) 50%,
    rgba(54, 121, 0, 0.7) 100%);
  z-index: 2;
  backdrop-filter: blur(6px);
}

/* Content Container */
.content-container {
  position: relative;
  z-index: 3;
  display: flex;
  height: 100%;
  align-items: center;
  padding: 0 2%;
  gap: 2rem;
}

/* Left Section - Enhanced with larger images */
.left-section {
  width: 30%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
}

.person-info {
  display: flex;
  flex-direction: column;
  gap: 4rem;
  text-align: center;
  color: white;
  width: 100%;
}

.person-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.3s ease;
}

.person-card:hover {
  transform: translateY(-8px);
}

/* MUCH LARGER Images - Increased from 100px to 180px */
.logo-circle {
  width: 8vw;
  height: 8vw;
  background: rgba(255, 255, 255, 0.1);
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28px;
  font-weight: bold;
  backdrop-filter: blur(20px);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow:
    0 15px 35px rgba(0, 0, 0, 0.3),
    0 5px 15px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  margin-bottom: 1.5rem;
  overflow: hidden;
  position: relative;
}

.logo-circle::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg,
    rgba(255, 255, 255, 0.2) 0%,
    transparent 50%,
    rgba(255, 255, 255, 0.1) 100%);
  border-radius: 50%;
  z-index: -1;
}

.person-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  transition: transform 0.4s ease;
}

.logo-circle:hover {
  transform: translateY(-10px) scale(1.05);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.4),
    0 10px 25px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.logo-circle:hover .person-image {
  transform: scale(1.1);
}

.person-details {
  text-align: center;
  transition: transform 0.3s ease;
}

.person-card:hover .person-details {
  transform: translateY(-5px);
}

.person-name {
  font-size: 1.6rem;
  font-weight: 700;
  margin-bottom: 0.8rem;
  color: white;
  font-family: 'Inter', sans-serif;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.person-designation {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.85);
  margin-bottom: 1rem;
  font-style: italic;
  font-weight: 500;
  font-family: 'Inter', sans-serif;
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
}

.person-message {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  line-height: 1.5;
  font-family: 'Inter', sans-serif;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Right Section - Adjusted for larger left section */
.right-section {
  width: 70%;
  padding-left: 2%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.content-wrapper {
  max-width: 900px;
  position: relative;
  height: auto;
  overflow: hidden;
  text-align: center;
}

.title {
  font-size: 4rem;
  font-weight: 900;
  margin-bottom: 2rem;
  line-height: 1.1;
  color: #ffffff;
  font-family: 'Inter', sans-serif;
  letter-spacing: -0.02em;
  opacity: 1;
  transition: opacity 0.5s ease-in-out;
  text-align: center;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
}

.paragraph {
  font-size: 1.4rem;
  line-height: 1.7;
  margin-bottom: 3rem;
  color: rgba(255, 255, 255, 0.95);
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  opacity: 1;
  transition: opacity 0.5s ease-in-out;
  text-align: center;
  max-width: 100%;
  text-shadow: 0 3px 10px rgba(0, 0, 0, 3);
}

/* Fade Animation */
.content-wrapper:not(.animate) .title,
.content-wrapper:not(.animate) .paragraph {
  opacity: 0;
}

.content-wrapper.animate .title,
.content-wrapper.animate .paragraph {
  opacity: 1;
}

/* Enhanced Action Button */
.action-button {
  background: linear-gradient(45deg, #ff6b6b, #ee5a52);
  color: white;
  border: none;
  padding: 20px 50px;
  font-size: 1.3rem;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  font-family: 'Inter', sans-serif;
  letter-spacing: 1px;
  text-transform: uppercase;
  position: relative;
  overflow: hidden;
  opacity: 1;
  transform: translateY(0);
  box-shadow: 0 12px 30px rgba(255, 107, 107, 0.4);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s;
}

.action-button:hover::before {
  left: 100%;
}

.action-button:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 20px 40px rgba(255, 107, 107, 0.5);
  background: linear-gradient(45deg, #ee5a52, #ff6b6b);
}

.action-button:active {
  transform: translateY(-2px) scale(0.98);
}

/* Enhanced Slide Indicators */
.slide-indicators {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 4;
  display: flex;
  gap: 15px;
}

.indicator {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
}

.indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: white;
  border-radius: 50%;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: translate(-50%, -50%);
}

.indicator.active {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(255, 255, 255, 0.6);
}

.indicator.active::before {
  width: 100%;
  height: 100%;
}

.indicator:hover {
  background: rgba(255, 255, 255, 0.7);
  transform: scale(1.4);
  border-color: rgba(255, 255, 255, 0.5);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .logo-circle {
    width: 140px;
    height: 140px;
  }

  .title {
    font-size: 3.5rem;
  }

  .paragraph {
    font-size: 1.3rem;
  }
}

@media (max-width: 1024px) {
  .content-container {
    padding: 0 3%;
  }

  .logo-circle {
    width: 130px;
    height: 130px;
  }

  .title {
    font-size: 3.2rem;
  }

  .paragraph {
    font-size: 1.25rem;
  }
}

/* MOBILE RESPONSIVE - Images go to top in row layout */
@media (max-width: 768px) {
  .hero-section {
    height: 85vh;
  }

  .content-container {
    flex-direction: column;
    padding: 3% 4%;
    text-align: center;
    justify-content: flex-start;
    gap: 2rem;
  }

  /* Images section moves to top and becomes horizontal */
  .left-section {
    width: 100%;
    margin-bottom: 0;
    padding: 2rem 1rem 1rem 1rem;
    order: 1;
  }

  .person-info {
    flex-direction: row;
    gap: 3rem;
    justify-content: center;
    align-items: center;
  }

  .person-card {
    flex: 1;
    max-width: 200px;
  }

  /* Larger images on mobile for better visibility */
  .logo-circle {
    width: 140px;
    height: 140px;
    margin-bottom: 1rem;
  }

  .person-name {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
  }

  .person-designation {
    font-size: 1rem;
    margin-bottom: 0.8rem;
  }

  .person-message {
    font-size: 0.9rem;
  }

  /* Content section */
  .right-section {
    width: 100%;
    padding-left: 0;
    order: 2;
  }

  .content-wrapper {
    max-width: 100%;
  }

  .title {
    font-size: 2.8rem;
    margin-bottom: 1.5rem;
  }

  .paragraph {
    font-size: 1.15rem;
    text-align: center;
    margin-bottom: 2.5rem;
    line-height: 1.6;
  }

  .action-button {
    padding: 16px 40px;
    font-size: 1.1rem;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .content-container {
    padding: 4% 5%;
    gap: 1.5rem;
  }

  .person-info {
    gap: 2rem;
  }

  .logo-circle {
    width: 120px;
    height: 120px;
  }

  .person-name {
    font-size: 1.1rem;
  }

  .person-designation {
    font-size: 0.9rem;
  }

  .person-message {
    font-size: 0.8rem;
  }

  .title {
    font-size: 2.2rem;
  }

  .paragraph {
    font-size: 1rem;
    line-height: 1.5;
  }

  .action-button {
    padding: 14px 35px;
    font-size: 1rem;
  }
}

/* Extra small devices */
@media (max-width: 360px) {
  .person-info {
    gap: 1.5rem;
  }

  .logo-circle {
    width: 100px;
    height: 100px;
  }

  .person-name {
    font-size: 1rem;
  }

  .person-designation {
    font-size: 0.85rem;
  }

  .person-message {
    font-size: 0.75rem;
  }

  .title {
    font-size: 2rem;
  }

  .paragraph {
    font-size: 0.95rem;
  }
}