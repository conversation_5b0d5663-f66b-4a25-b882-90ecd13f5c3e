.footer {
  background: linear-gradient(135deg, #0f1a2a, #1a2b40);
  color: #e2e8f0;
  padding: 4rem 0 1.5rem 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
  }

  .grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2.5rem;
    padding-bottom: 2.5rem;

    @media (max-width: 1024px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 2rem;
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      text-align: center;
      padding: 0 1rem;
    }
  }

  h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
    letter-spacing: -0.1px;

    &::after {
      content: '';
      position: absolute;
      bottom: -4px;
      left: 0;
      width: 40px;
      height: 2px;
      background: #00b5d9;
      border-radius: 2px;
    }
  }

  p {
    color: #a0aec0;
    font-size: 0.875rem;
    margin: 0.3rem 0;
    line-height: 1.5;
  }

  a {
    color: #cbd5e1;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.875rem;

    &:hover {
      color: #64ffda;
      transform: translateX(2px);
    }
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;

    li {
      position: relative;
      padding-left: 0.5rem;

      &::before {
        content: '→';
        opacity: 0;
        position: absolute;
        left: -18px;
        color: #64ffda;
        font-size: 0.75rem;
        transition: opacity 0.3s ease, transform 0.3s ease;
      }

      &:hover::before {
        opacity: 1;
        transform: translateX(-2px);
      }
    }
  }

  // Contact Info Link Styling
  .flex p a {
    color: #64ffda;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }

  // Bottom Bar (Copyright)
  .bottom-bar {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: #a0aec0;
    margin-top: 0;
    padding: 1.25rem;
    text-align: center;
    font-size: 0.875rem;
    font-weight: 500;
    letter-spacing: -0.1px;

    span {
      color: #64ffda;
      font-weight: 600;
    }
  }
}