// .form-group {
//   margin-bottom: 1.25rem;
//   position: relative;
// }

// label {
//   display: block;
//   margin-bottom: 0.5rem;
//   font-weight: 500;
//   color: var(--color-text-dark, #333);
// }

// .required-indicator {
//   color: var(--color-poppy-red, #dc3545);
//   margin-left: 0.25rem;
// }

// .readonly-display {
//   padding: 0.75rem 1rem;
//   background-color: var(--color-background-light, #f8f9fa);
//   border: 1px solid var(--color-border, #ced4da);
//   border-radius: 6px;
//   font-size: 1rem;
//   color: var(--color-text-dark, #333);
//   min-height: calc(1.5em + 0.75rem + 2px);
//   word-break: break-word;
// }

// .form-control {
//   border: 1px solid var(--color-border, #ced4da);
//   border-radius: 6px;
//   background-color: var(--color-background, #ffffff);
//   box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
//   color: var(--color-text-dark, #333333);
//   padding: 0.75rem 1rem;
//   font-size: 1rem;
//   transition: border-color 0.2s ease, box-shadow 0.2s ease;

//   &:focus {
//     outline: none;
//     border-color: var(--color-poppy-blue, #0a54c4);
//     box-shadow: 0 0 0 3px rgba(6, 41, 236, 0.2);
//   }

//   &.is-invalid {
//     border-color: var(--color-poppy-red, #dc3545);

//     &:focus {
//       box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2);
//     }
//   }

//   &.readonly-like {
//     background-color: var(--color-background-light, #f8f9fa);
//     cursor: not-allowed;
//   }
// }

// .invalid-input {
//   opacity: 0;
//   color: var(--color-poppy-red, #dc3545);
//   font-size: 0.875rem;
//   margin-top: 0.25rem;
//   transform: translateY(-4px);
//   transition: opacity 0.2s ease, transform 0.2s ease;

//   &.show-error {
//     opacity: 1;
//     transform: translateY(0);
//   }
// }

// .x-error-msg-text {
//   font-size: 0.875rem;
//   line-height: 1.5;
// }


:host ::ng-deep .p-floatlabel {
  font-size: 0.85rem; 
  // margin-top: 1.5rem; 
  background-color: transparent;

}

:host ::ng-deep .p-floatlabel input,
:host ::ng-deep .p-floatlabel  .p-textarea {
  // height: 32px;
  // width: 200px;
  padding: 10px 8px;
  font-size: 0.8rem; 
  background-color: #fff;
  color: #3b3b3b;
  border: 1px solid #b8b8b8
}

:host ::ng-deep .p-floatlabel label {
  color: #2C82AA; 
  font-size: 0.75rem; 
  background-color: #fff!important;
}

:host ::ng-deep .p-floatlabel input::placeholder {
  font-size: 0.75rem;
  color: #999;
  font-weight: 500;
}

:host ::ng-deep .p-floatlabel input:focus,
:host ::ng-deep .p-floatlabel textarea:focus{
  border: 1px solid #2C82AA
}
:host ::ng-deep .p-floatlabel input:focus ~ label,
:host ::ng-deep .p-floatlabel input.p-filled ~ label,:host ::ng-deep .p-floatlabel textarea:focus ~ label {
  background-color: white;
  color: #2C82AA;
  padding: 0 4px; 
}
.required-indicator {
  color: var(--color-poppy-red, #dc3545);
  margin-left: 0.25rem;
}

:host ::ng-deep .p-floatlabel {
  font-size: 0.85rem; 
  background-color: transparent;
}

:host ::ng-deep .p-floatlabel input,
:host ::ng-deep .p-floatlabel .p-textarea {
  padding: 10px 8px;
  font-size: 0.8rem; 
  background-color: #fff;
  color: #3b3b3b;
  border: 1px solid #b8b8b8;
  
  /* ✅ Style for readonly */
  &[readonly] {
    background-color: #f5f5f5; /* Light gray background */
    color: #666;              /* Darker text */
    cursor: not-allowed;      /* Show not-allowed cursor */
    opacity: 0.9;             /* Slightly faded */
    
    &:focus {
      border-color: #b8b8b8;  /* Keep border same as normal */
      outline: none;          /* No focus outline */
    }
  }
}

:host ::ng-deep .p-floatlabel label {
  color: #2C82AA; 
  font-size: 0.75rem; 
  background-color: #fff !important;
}

:host ::ng-deep .p-floatlabel input::placeholder {
  font-size: 0.75rem;
  color: #999;
  font-weight: 500;
}

:host ::ng-deep .p-floatlabel input:focus,
:host ::ng-deep .p-floatlabel textarea:focus {
  border: 1px solid #2C82AA;
}

:host ::ng-deep .p-floatlabel input:focus ~ label,
:host ::ng-deep .p-floatlabel input.p-filled ~ label,
:host ::ng-deep .p-floatlabel textarea:focus ~ label {
  background-color: white;
  color: #2C82AA;
  padding: 0 4px; 
}

.required-indicator {
  color: var(--color-poppy-red, #dc3545);
  margin-left: 0.25rem;
}