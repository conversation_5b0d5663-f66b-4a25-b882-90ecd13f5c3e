<div class="dialog-header">
  <span class="title" mat-dialog-title>{{ data ? 'Update Service' : 'Create Service' }}</span>
  <button mat-icon-button (click)="closeDialog()">
    <mat-icon>close</mat-icon>
  </button>
</div>

<mat-dialog-content class="dialog-content">
  <form [formGroup]="serviceForm" class="form-container">
    <div class="form-row">
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Service Title</mat-label>
        <input matInput formControlName="service_title_or_description" placeholder="e.g. Contract Labour">
        <mat-error *ngIf="serviceForm.get('service_title_or_description')?.hasError('required')">
          Service Title is required
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Department</mat-label>
        <mat-select formControlName="department_id">
          <mat-option *ngFor="let dept of departments" [value]="dept.id">{{ dept.name }}</mat-option>
        </mat-select>
        <mat-error *ngIf="serviceForm.get('department_id')?.hasError('required')">
          Department is required
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="form-field">
        <mat-label>NOC Type</mat-label>
        <mat-select formControlName="noc_type">
          <mat-option *ngFor="let type of nocTypes" [value]="type">{{ type }}</mat-option>
        </mat-select>
        <mat-error *ngIf="serviceForm.get('noc_type')?.hasError('required')">
          NOC Type is required
        </mat-error>
      </mat-form-field>
    </div>
    <div class="form-row">
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>NOC Name</mat-label>
        <input matInput formControlName="noc_name">
        <mat-error *ngIf="serviceForm.get('noc_name')?.hasError('required')">
          NOC Name is required
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Payment Type</mat-label>
        <mat-select formControlName="noc_payment_type">
          <mat-option value="Estimated">Estimated</mat-option>
          <mat-option value="Calculated">Calculated</mat-option>
          <mat-option value="Hardcoded">Hardcoded</mat-option>
        </mat-select>
        <mat-error *ngIf="serviceForm.get('noc_payment_type')?.hasError('required')">
          Payment Type is required
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Target Days</mat-label>
        <input matInput type="number" formControlName="target_days">
        <mat-error *ngIf="serviceForm.get('target_days')?.hasError('required')">
          Target Days is required
        </mat-error>
      </mat-form-field>
    </div>
    <div class="form-row">
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Generated ID Format</mat-label>
        <input matInput formControlName="generated_id_format" placeholder="e.g. CL/WR/SWS/{{ '{YEAR}' }}/{{ '{SEQ}' }}">
        <mat-hint>Use {{ '{YEAR}' }} for current year & {{ '{SEQ}' }} for sequence</mat-hint>
      </mat-form-field>

      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Depend on Service</mat-label>
        <input matInput formControlName="depends_on_services" placeholder="Select dependent service">
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>NOC Date</mat-label>
        <input matInput formControlName="label_noc_date" [matDatepicker]="nocDate">
        <mat-datepicker-toggle matSuffix [for]="nocDate"></mat-datepicker-toggle>
        <mat-datepicker #nocDate></mat-datepicker>
      </mat-form-field>

      <mat-form-field appearance="outline" class="form-field">
        <mat-label>NOC Document</mat-label>
        <input matInput formControlName="label_noc_doc">
      </mat-form-field>

      <mat-form-field appearance="outline" class="form-field">
        <mat-label>NOC Number</mat-label>
        <input matInput formControlName="label_noc_no">
      </mat-form-field>
    </div>
    <div class="form-row">
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>NOC Short Name</mat-label>
        <input matInput formControlName="noc_short_name">
        <mat-error *ngIf="serviceForm.get('noc_short_name')?.hasError('required')">
          NOC Short Name is required
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Valid Till Date</mat-label>
        <input matInput [matDatepicker]="picker" formControlName="label_valid_till">
        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
        <mat-datepicker #picker></mat-datepicker>
        <mat-error *ngIf="serviceForm.get('label_valid_till')?.hasError('required')">
          Valid Till Date is required
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="form-field">
        <mat-label>NOC Validity (days)</mat-label>
        <input matInput type="number" formControlName="noc_validity">
      </mat-form-field>

      <mat-form-field appearance="outline" class="form-field">
        <mat-label>NSW License ID</mat-label>
        <input matInput formControlName="nsw_license_id">
      </mat-form-field>
    </div>

    <div class="form-row checkboxes">
      <mat-checkbox formControlName="allow_repeat_application">Allow Repeat Application</mat-checkbox>
      <mat-checkbox formControlName="generate_id">Generate ID</mat-checkbox>
      <mat-checkbox formControlName="generate_pdf">Generate PDF</mat-checkbox>
      <mat-checkbox formControlName="show_valid_till">Show Valid Till</mat-checkbox>
      <mat-checkbox formControlName="auto_renewal">Auto Renewal</mat-checkbox>
      <mat-checkbox formControlName="show_letter_date">Show Letter Date</mat-checkbox>
      <mat-checkbox formControlName="external_data_share">External Data Share</mat-checkbox>
      <mat-checkbox formControlName="valid_for_upload">Valid For Upload</mat-checkbox>
    </div>

  </form>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button mat-stroked-button color="warn" (click)="closeDialog()">Cancel</button>
  <button mat-raised-button color="primary" [disabled]="!serviceForm.valid"
    (click)="data ? submit(true) : submit(false)">
    {{ data ? 'Update' : 'Create' }}
  </button>
</mat-dialog-actions>