@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&display=swap');
@import url('https://cdn.jsdelivr.net/npm/feather-icons@4.28.0/dist/feather.min.css');
.timeline-container {
  margin: 0 auto;
  padding: 2rem 1rem;
  
  @media (max-width: 768px) {
    padding: 1rem 0.5rem;
  }
}

.timeline-wrapper {
  position: relative;
}

.timeline-line {
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #e5e7eb 0%, #e5e7eb 100%);
  transform: translateX(-50%);
  z-index: 1;
  
  @media (max-width: 768px) {
    left: 2rem;
  }
}

.timeline-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 4rem;
  position: relative;
  width: 100%;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  @media (max-width: 768px) {
    margin-bottom: 2rem;
  }
  
  // Odd items (1st, 3rd, 5th...) - content on the left
  &:nth-child(odd) {
    @media (min-width: 769px) {
      justify-content: flex-start;
      
      .timeline-content {
        order: 1;
        margin-right: 2rem;
      }
      
      .timeline-node {
        order: 2;
      }
    }
  }
  
  // Even items (2nd, 4th, 6th...) - content on the right
  &:nth-child(even) {
    @media (min-width: 769px) {
      justify-content: flex-end;
      
      .timeline-content {
        order: 2;
        margin-left: 2rem;
      }
      
      .timeline-node {
        order: 1;
      }
    }
  }
}

.timeline-node {
  position: relative;
  flex-shrink: 0;
  z-index: 2;
  display: flex;
  justify-content: center;
  
  @media (min-width: 769px) {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 0;
  }
  
  @media (max-width: 768px) {
    margin-right: 1rem;
  }
}

.node-circle {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  background: hsl(0, 66%, 33%);
  border: 3px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  
  @media (max-width: 768px) {
    width: 3rem;
    height: 3rem;
  }
  
  .timeline-item.active & {
    border-color: #3b82f6;
    background: #3b82f6;
    color: white;
  }
  

}

.node-number {
  font-weight: 600;
  font-size: 0.875rem;
  position: absolute;
  top: -1.5rem;
  left: 50%;
  transform: translateX(-50%);
  color: #6b7280;
  
  @media (max-width: 768px) {
    font-size: 0.75rem;
    top: -1.25rem;
  }
  
  .timeline-item.active & {
    color: #3b82f6;
  }
  
  .timeline-item.completed & {
    color: #10b981;
  }
}

.node-icon {
  color: #fbfdff;
  
  .timeline-item.active & {
    color: white;
  }
  
  .timeline-item.completed & {
    color: white;
  }
  
  svg {
    width: 1.5rem;
    height: 1.5rem;
    
    @media (max-width: 768px) {
      width: 1.25rem;
      height: 1.25rem;
    }
  }
}

.timeline-connector {
  position: absolute;
  top: 4rem;
  left: 50%;
  width: 2px;
  height: 3rem;
  background: #e5e7eb;
  transform: translateX(-50%);
  
  @media (max-width: 768px) {
    top: 3rem;
    height: 2rem;
  }
}

.timeline-content {
  flex: 1;
  max-width: calc(50% - 3rem);
  
  @media (max-width: 768px) {
    max-width: 100%;
  }
}

.content-card {
  background: #fffbfb;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 8px 10px -1px rgba(0, 0, 0, 0.1), 0 8px 10px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  position: relative;
  transition: all 0.3s ease;
  
  @media (max-width: 768px) {
    padding: 1.25rem;
    border-radius: 0.75rem;
  }
  
  &:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
  }
  
  &.active-card {
    box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.1), 0 4px 6px -2px rgba(59, 130, 246, 0.05);
  }
  
  @media (min-width: 769px) {
    &::before {
      content: '';
      position: absolute;
      top: 2rem;
      width: 0;
      height: 0;
      border: 10px solid transparent;
    }
  }
  
  // Arrow for odd items (pointing right)
  .timeline-item:nth-child(odd) &::before {
    @media (min-width: 769px) {
      right: -20px;
      border-left-color: #ffffff;
      border-right: none;
    }
  }
  
  // Arrow for even items (pointing left)
  .timeline-item:nth-child(even) &::before {
    @media (min-width: 769px) {
      left: -20px;
      border-right-color: #ffffff;
      border-left: none;
    }
  }
  
  &.active-card::before {
    @media (min-width: 769px) {
      border-left-color: #ffffff !important;
      border-right-color: #ffffff !important;
    }
  }
}

.card-step-number {
  position: absolute;
  top: -0.5rem;
  right: 1rem;
  background: #f3f4f6;
  color: #6b7280;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
  
  .active-card & {
    background: #3b82f6;
    color: white;
  }
}

.card-header {
  margin-bottom: 1rem;
}

.card-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin: 0;
  line-height: 1.3;
  
  @media (max-width: 768px) {
    font-size: 1.125rem;
  }
}

.card-subtitle {
  font-size:1rem;
  color: #000000;
  margin: 0.25rem 0 0 0;
  font-weight: 500;
}

.card-description {
  margin-bottom: 1.25rem;
  
  p {
    color: #000000;
    line-height: 1.6;
    margin: 0;
    font-size: 0.9375rem;
  }
}

.card-points {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 0.5rem;
  }
}

.point-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
  
  @media (max-width: 768px) {
    flex: 1;
    min-width: auto;
  }
  
  &:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
  }
  
  .active-card & {
    background: rgba(59, 130, 246, 0.05);
    border-color: rgba(59, 130, 246, 0.2);
    
    &:hover {
      background: rgba(59, 130, 246, 0.1);
    }
  }
}

.point-indicator {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background: #3b82f6;
  flex-shrink: 0;
  
  .active-card & {
    background: #3b82f6;
  }
}

.point-text {
  font-size: 0.8125rem;
  color: #ffffff;
  font-weight: 500;
  line-height: 1.4;
  
  .active-card & {
    color: #ffffff;
  }
}

// Responsive adjustments for mobile
@media (max-width: 480px) {
  .timeline-container {
    padding: 0.75rem 0.25rem;
  }
  
  .content-card {
    padding: 1rem;
  }
  
  .card-title {
    font-size: 1rem;
  }
  
  .card-description p {
    font-size: 0.875rem;
  }
  
  .point-text {
    font-size: 0.75rem;
  }
}