<div class="timeline-container">
  <div class="timeline-wrapper">
    <div class="timeline-line"></div>
    
    <div class="timeline-item" 
         *ngFor="let e of event; let i = index">
      
      <!-- Timeline Node -->
      <div class="timeline-node">
        <div class="node-circle">
          <div class="node-number">{{ (i + 1).toString().padStart(2, '0') }}</div>
          <div class="node-icon">
            <svg *ngIf="i === 0" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.89 22 5.99 22H18C19.1 22 20 21.1 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M14 2V8H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M16 13H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M16 17H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M10 9H9H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <svg *ngIf="i === 1" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
              <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <svg *ngIf="i > 1" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
              <path d="M8 12H16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M12 8V16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </div>
        </div>
        <div class="timeline-connector" *ngIf="i < event.length - 1"></div>
      </div>

      <!-- Content Card -->
      <div class="timeline-content">
        <div class="content-card" >
          <div class="card-step-number">{{ (i + 1).toString().padStart(2, '0') }}</div>
          
          <div class="card-header">
           <span></span><h3 class="card-title"><img width="50" height="50" style="display: inline;" src="..//public/location-pin_9508897.png" alt="vs"> {{ e.title }}</h3>
            <p class="card-subtitle" *ngIf="e.subtitle">{{ e.subtitle }}</p>
          </div>

          <div class="card-description">
            <p>{{ e.description }}</p>
          </div>

          <div class="card-points" >
            <div class="point-item" *ngFor="let point of e.points" [ngStyle]="{'background-color': randomColor()}">
              <div class="point-indicator"></div>
              <span class="point-text">{{ point }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>