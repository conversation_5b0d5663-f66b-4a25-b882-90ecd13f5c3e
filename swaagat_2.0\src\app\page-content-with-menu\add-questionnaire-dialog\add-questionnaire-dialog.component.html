<div class="dialog-header" mat-dialog-title>
  <h2>{{ data.mode === 'add' ? 'Add Questionnaire' : 'Update Questionnaire' }}</h2>
  <button mat-icon-button (click)="close()">
    <mat-icon>close</mat-icon>
  </button>
</div>
<div class="service-info">
  <strong>Service Group:</strong> {{ data.service?.name || 'Unknown Service' }}
</div>

<mat-dialog-content>
  <form [formGroup]="questionnaireForm" class="form-container">

    <div class="form-row">
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Question Label</mat-label>
        <input matInput formControlName="question_label" placeholder="Enter question label" />
        <mat-error *ngIf="questionnaireForm.get('question_label')?.hasError('required')">
          Question label is required
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Question Type</mat-label>
        <mat-select formControlName="question_type">
          <mat-option value="text">Text</mat-option>
          <mat-option value="number">Number</mat-option>
          <mat-option value="select">Dropdown</mat-option>
          <mat-option value="radio">Radio</mat-option>
          <mat-option value="checkbox">Checkbox</mat-option>
          <mat-option value="date">Date</mat-option>
          <mat-option value="button">Button</mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Default Value</mat-label>
        <input matInput formControlName="default_value" placeholder="Enter default value" />
      </mat-form-field>
    </div>

    <div class="form-row" *ngIf="['select','radio','checkbox'].includes(questionnaireForm.get('question_type')?.value)">
      <mat-form-field appearance="outline" class="form-field full-width">
        <mat-label>Options (comma separated)</mat-label>
        <input matInput formControlName="options" placeholder="Eg: Option1, Option2, Option3" />
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Is Required?</mat-label>
        <mat-select formControlName="is_required">
          <mat-option value="yes">Yes</mat-option>
          <mat-option value="no">No</mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Source Table</mat-label>
        <mat-select formControlName="default_source_table" (selectionChange)="onTableChange($event.value)">
          <mat-option value="unit_details">Unit Details</mat-option>
          <mat-option value="enterprise_details">Enterprise Details</mat-option>
          <mat-option value="management_details">Management Details</mat-option>
          <mat-option value="line_of_activities">Line of Activity</mat-option>
          <mat-option value="general_attachments">General Attachments</mat-option>
          <mat-option value="clearances">Clearances</mat-option>
          <mat-option value="bank_details">Bank Details</mat-option>
          <mat-option value="activities">Activities</mat-option>
        </mat-select>
      </mat-form-field>
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Source Column</mat-label>
        <mat-select formControlName="default_source_column">
          <mat-option *ngFor="let col of availableColumns" [value]="col">
            {{ col }}
          </mat-option>
        </mat-select>
      </mat-form-field>

    </div>
    <div class="form-row">
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Display Order</mat-label>
        <input matInput type="number" formControlName="display_order" />
        <mat-error *ngIf="questionnaireForm.get('display_order')?.hasError('required')">
          Display order is required
        </mat-error>
        <mat-error *ngIf="questionnaireForm.get('display_order')?.hasError('min')">
          Display order must be greater than 0
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Group Label</mat-label>
        <input matInput formControlName="group_label" />
      </mat-form-field>

      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Display Width</mat-label>
        <mat-select formControlName="display_width">
          <mat-option value="25%">25%</mat-option>
          <mat-option value="50%">50%</mat-option>
          <mat-option value="100%">100%</mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <div formGroupName="validation_rule" class="validation-section">
      <h3>Validation Rule</h3>
      <div class="form-row validation-row">
        <mat-form-field appearance="outline" class="form-field minmax-field">
          <mat-label>Min Length</mat-label>
          <input matInput type="number" formControlName="minLength" />
          <mat-error *ngIf="questionnaireForm.get('validation_rule.minLength')?.hasError('required')">
            Min Length is required
          </mat-error>
          <mat-error *ngIf="questionnaireForm.get('validation_rule.minLength')?.hasError('min')">
            Min Length must be at least 1
          </mat-error>
          <mat-error *ngIf="questionnaireForm.get('validation_rule.minLength')?.hasError('max')">
            Min Length cannot be more than 100
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="form-field minmax-field">
          <mat-label>Max Length</mat-label>
          <input matInput type="number" formControlName="maxLength" />
          <mat-error *ngIf="questionnaireForm.get('validation_rule.maxLength')?.hasError('required')">
            Max Length is required
          </mat-error>
          <mat-error *ngIf="questionnaireForm.get('validation_rule.maxLength')?.hasError('min')">
            Max Length must be at least 1
          </mat-error>
          <mat-error *ngIf="questionnaireForm.get('validation_rule.maxLength')?.hasError('max')">
            Max Length cannot be more than 100
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="form-field pattern-field">
          <mat-label>Pattern</mat-label>
          <input matInput formControlName="pattern" placeholder="^[A-Za-z ]+$" />
        </mat-form-field>

        <mat-form-field appearance="outline" class="form-field error-field">
          <mat-label>Error Message</mat-label>
          <input matInput formControlName="errorMessage" placeholder="Only alphabets allowed" />
        </mat-form-field>
      </div>
    </div>


  </form>

  <div class="preview-section" *ngIf="apiQuestions.length">
    <h3>Questionnaires Preview</h3>

    <table mat-table [dataSource]="apiQuestions" class="mat-elevation-z2 preview-table full-width">

      <ng-container matColumnDef="question_label">
        <th mat-header-cell *matHeaderCellDef> Question </th>
        <td mat-cell *matCellDef="let q"> {{q.question_label}} </td>
      </ng-container>

      <ng-container matColumnDef="question_type">
        <th mat-header-cell *matHeaderCellDef> Type </th>
        <td mat-cell *matCellDef="let q"> {{q.question_type}} </td>
      </ng-container>

      <ng-container matColumnDef="options">
        <th mat-header-cell *matHeaderCellDef> Options </th>
        <td mat-cell *matCellDef="let q"> {{ getOptionsAsString(q) || '-' }} </td>
      </ng-container>

      <ng-container matColumnDef="default_value">
        <th mat-header-cell *matHeaderCellDef> Default </th>
        <td mat-cell *matCellDef="let q"> {{q.default_value || '-'}} </td>
      </ng-container>

      <ng-container matColumnDef="is_required">
        <th mat-header-cell *matHeaderCellDef> Required </th>
        <td mat-cell *matCellDef="let q"> {{q.is_required}} </td>
      </ng-container>

      <ng-container matColumnDef="display_order">
        <th mat-header-cell *matHeaderCellDef> Order </th>
        <td mat-cell *matCellDef="let q"> {{q.display_order}} </td>
      </ng-container>

      <ng-container matColumnDef="group_label">
        <th mat-header-cell *matHeaderCellDef> Group </th>
        <td mat-cell *matCellDef="let q"> {{q.group_label}} </td>
      </ng-container>

      <ng-container matColumnDef="display_width">
        <th mat-header-cell *matHeaderCellDef> Width </th>
        <td mat-cell *matCellDef="let q"> {{q.display_width}} </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

    </table>
  </div>

</mat-dialog-content>

<mat-dialog-actions align="end">
  <button mat-stroked-button color="warn" (click)="close()">Cancel</button>
  <button mat-flat-button color="accent" class="submit-btn" (click)="submit()">
    {{ data.mode === 'add' ? 'Save' : 'Update' }}
  </button>
</mat-dialog-actions>