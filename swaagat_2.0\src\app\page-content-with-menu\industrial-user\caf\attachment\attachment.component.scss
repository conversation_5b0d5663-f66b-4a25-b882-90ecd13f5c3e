:host {
  font-size: 0.85rem;
  line-height: 1.3;
}

.form-group label,
.heder-color,
.download-btn,
.button,
.btn,
table,
.filename {
  font-size: 0.85rem;
}

.btn-primary {
  background-color: #007bff;
  border: none;
  padding: 0.375rem 1rem;
  font-size: 0.85rem;
  border-radius: 6px;
  font-weight: 500;

  &:hover:not(:disabled) {
    background-color: #0069d9;
  }

  &:disabled {
    background-color: #6c757d;
    opacity: 0.6;
  }
}
.btn-success {
  border: none;
  padding: 0.375rem 1rem;
  font-size: 0.85rem;
  border-radius: 6px;
  font-weight: 500;



  &:disabled {
    background-color: #6c757d;
    opacity: 0.6;
  }
}

.form-group-margin {
  margin-bottom: 0.5rem;
}

.table {
  font-size: 0.85rem;
  td, th {
    padding: 0.25rem 0.5rem;
  }
}

.file-display {
  padding: 6px 8px;
  gap: 6px;
  font-size: 0.85rem;
}

.filename {
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

th{
  background-color: #cfe2ff;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}


.grid-container {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
 column-gap: 1.5rem;
row-gap: 1.5rem;

  @media (max-width: 1024px) {
    grid-template-columns: repeat(1, 1fr);
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}