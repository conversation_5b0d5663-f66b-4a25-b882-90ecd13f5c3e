import {
  BlockScrollStrategy,
  CdkConnectedOverlay,
  CdkOverlayOrigin,
  CloseScrollStrategy,
  ConnectedOverlayPositionChange,
  ConnectionPositionPair,
  FlexibleConnectedPositionStrategy,
  FullscreenOverlayContainer,
  GlobalPositionStrategy,
  NoopScrollStrategy,
  Overlay,
  OverlayConfig,
  OverlayContainer,
  OverlayKeyboardDispatcher,
  OverlayModule,
  OverlayOutsideClickDispatcher,
  OverlayPositionBuilder,
  OverlayRef,
  RepositionScrollStrategy,
  STANDARD_DROPDOWN_ADJACENT_POSITIONS,
  STANDARD_DROPDOWN_BELOW_POSITIONS,
  ScrollStrategyOptions,
  ScrollingVisibility,
  createBlockScrollStrategy,
  createCloseScrollStrategy,
  createFlexibleConnectedPositionStrategy,
  createGlobalPositionStrategy,
  createNoopScrollStrategy,
  createOverlayRef,
  createRepositionScrollStrategy,
  validateHorizontalPosition,
  validateVerticalPosition
} from "./chunk-23Q4DKFI.js";
import "./chunk-24WSRXXN.js";
import "./chunk-QCETVJKM.js";
import "./chunk-GWE4MCPP.js";
import "./chunk-C4MWPVUO.js";
import {
  CdkFixedSizeVirtualScroll,
  CdkScrollable,
  CdkScrollableModule,
  CdkVirtualForOf,
  CdkVirtualScrollViewport,
  CdkVirtualScrollableElement,
  CdkVirtualScrollableWindow,
  ScrollDispatcher,
  ViewportRuler
} from "./chunk-Q6RXRJF4.js";
import "./chunk-HOCBX3ZD.js";
import "./chunk-EOFW2REK.js";
import {
  Dir
} from "./chunk-YQ55SDGC.js";
import "./chunk-LHVGIKP3.js";
import "./chunk-PQF5LFR6.js";
import "./chunk-FVRNZMEJ.js";
import "./chunk-NDZIWK7R.js";
import "./chunk-4G4PEPLI.js";
import "./chunk-3KKC7HMJ.js";
import "./chunk-KBUIKKCC.js";
export {
  BlockScrollStrategy,
  CdkConnectedOverlay,
  CdkOverlayOrigin,
  CdkScrollable,
  CloseScrollStrategy,
  ConnectedOverlayPositionChange,
  ConnectionPositionPair,
  FlexibleConnectedPositionStrategy,
  FullscreenOverlayContainer,
  GlobalPositionStrategy,
  NoopScrollStrategy,
  Overlay,
  OverlayConfig,
  OverlayContainer,
  OverlayKeyboardDispatcher,
  OverlayModule,
  OverlayOutsideClickDispatcher,
  OverlayPositionBuilder,
  OverlayRef,
  RepositionScrollStrategy,
  STANDARD_DROPDOWN_ADJACENT_POSITIONS,
  STANDARD_DROPDOWN_BELOW_POSITIONS,
  ScrollDispatcher,
  ScrollStrategyOptions,
  ScrollingVisibility,
  ViewportRuler,
  createBlockScrollStrategy,
  createCloseScrollStrategy,
  createFlexibleConnectedPositionStrategy,
  createGlobalPositionStrategy,
  createNoopScrollStrategy,
  createOverlayRef,
  createRepositionScrollStrategy,
  validateHorizontalPosition,
  validateVerticalPosition,
  CdkFixedSizeVirtualScroll as ɵɵCdkFixedSizeVirtualScroll,
  CdkScrollableModule as ɵɵCdkScrollableModule,
  CdkVirtualForOf as ɵɵCdkVirtualForOf,
  CdkVirtualScrollViewport as ɵɵCdkVirtualScrollViewport,
  CdkVirtualScrollableElement as ɵɵCdkVirtualScrollableElement,
  CdkVirtualScrollableWindow as ɵɵCdkVirtualScrollableWindow,
  Dir as ɵɵDir
};
