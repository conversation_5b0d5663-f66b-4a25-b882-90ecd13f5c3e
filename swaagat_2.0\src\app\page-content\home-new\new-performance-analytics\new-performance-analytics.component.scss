body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
}

.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.bg-white {
  background-color: #ffffff;
}

.max-w-7xl {
  max-width: 80rem;
  margin-left: auto;
  margin-right: auto;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.sm\:px-6 {
  @media (min-width: 640px) {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

.lg\:px-8 {
  @media (min-width: 1024px) {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.mb-8 {
  margin-bottom: 2rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.font-semibold {
  font-weight: 600;
}

.text-gray-900 {
  color: #1a202c;
}

.mb-4 {
  margin-bottom: 1rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-slate-600 {
  color: #4a5568;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.md\:grid-cols-2 {
  @media (min-width: 768px) {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

.lg\:grid-cols-4 {
  @media (min-width: 1024px) {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

.gap-6 {
  gap: 1.5rem;
}

.p-6 {
  padding: 1.5rem;
}

.rounded-2xl {
  border-radius: 1rem;
}

.shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.border {
  border-width: 1px;
}

.border-gray-100 {
  border-color: #f7fafc;
}

.mb-12 {
  margin-bottom: 3rem;
}

.text-center {
  text-align: center;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.w-12 {
  width: 3rem;
}

.h-12 {
  height: 3rem;
}

.bg-blue-100 {
  background-color: #ebf8ff;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.text-blue-600 {
  color: #3182ce;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.space-x-1 > * + * {
  margin-left: 0.25rem;
}

.inline-flex {
  display: inline-flex;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.rounded-full {
  border-radius: 9999px;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-medium {
  font-weight: 500;
}

.bg-green-100 {
  background-color: #f0fff4;
}

.text-green-800 {
  color: #276749;
}

.mr-1 {
  margin-right: 0.25rem;
}

.space-x-8 > * + * {
  margin-left: 2rem;
}

.border-b {
  border-bottom-width: 1px;
}

.border-gray-200 {
  border-color: #e2e8f0;
}

.pb-4 {
  padding-bottom: 1rem;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
}

.duration-200 {
  transition-duration: 200ms;
}

.text-blue-600 {
  color: #3182ce;
}

.border-blue-600 {
  border-color: #3182ce;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.text-gray-500 {
  color: #a0aec0;
}

.hover\:text-gray-700:hover {
  color: #4a5568;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-x-auto {
  overflow-x: auto;
}

.w-full {
  width: 100%;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.border-t {
  border-top-width: 1px;
}

.hover\:bg-gray-50:hover {
  background-color: #f9fafb;
}

.bg-slate-50 {
  background-color: #f8fafc;
}

.text-gray-700 {
  color: #4a5568;
}

.space-x-3 > * + * {
  margin-left: 0.75rem;
}

.w-8 {
  width: 2rem;
}

.h-8 {
  height: 2rem;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.text-emerald-600 {
  color: #059669;
}

.mt-16 {
  margin-top: 4rem;
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-blue-900 {
  --tw-gradient-from: #1a4971;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(26, 73, 113, 0));
}

.to-green-800 {
  --tw-gradient-to: #065f46;
}

.p-8 {
  padding: 2rem;
}

.text-white {
  color: #ffffff;
}

.lg\:grid-cols-2 {
  @media (min-width: 1024px) {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

.gap-8 {
  gap: 2rem;
}

.font-bold {
  font-weight: 700;
}

.text-blue-100 {
  color: #ebf8ff;
}

.leading-relaxed {
  line-height: 1.625;
}

.flex-col {
  flex-direction: column;
}

.sm\:flex-row {
  @media (min-width: 640px) {
    flex-direction: row;
  }
}

.gap-4 {
  gap: 1rem;
}

.bg-orange-500 {
  background-color: #f6ad55;
}

.hover\:bg-orange-600:hover {
  background-color: #ed8936;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.bg-white\/10 {
  background-color: rgba(255, 255, 255, 0.1);
}

.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

.border-white\/20 {
  border-color: rgba(255, 255, 255, 0.2);
}

.hover\:bg-white\/20:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.transition-all {
  transition-property: all;
}

.duration-300 {
  transition-duration: 300ms;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.p-4 {
  padding: 1rem;
}

.text-orange-400 {
  color: #f6ad55;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.text-green-400 {
  color: #68d391;
}

.text-yellow-400 {
  color: #f6e05e;
}

.text-purple-400 {
  color: #b794f4;
}

.text-blue-200 {
  color: #bee3f8;
}