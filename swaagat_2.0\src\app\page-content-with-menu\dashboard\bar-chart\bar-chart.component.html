 <div class="chart-container">
      <div class="chart-header">
        <h4 class="chart-title">{{ title }}</h4>
        <div class="chart-legend">
          <div class="legend-item">
            <span class="legend-color legend-color--approved"></span>
            <span class="legend-label">Approved</span>
          </div>
          <div class="legend-item">
            <span class="legend-color legend-color--rejected"></span>
            <span class="legend-label">Rejected</span>
          </div>
        </div>
      </div>
      <div class="chart-content">
        <div class="chart-y-axis">
          <div class="y-axis-label" *ngFor="let tick of yAxisTicks">{{ tick }}</div>
        </div>
        <div class="chart-bars">
          <div class="bar-group" *ngFor="let item of data">
            <div class="bar-container">
              <div class="bar bar--approved" 
                   [style.height.%]="getBarHeight(item.approved)">
                <span class="bar-value" *ngIf="item.approved > 0">{{ item.approved }}</span>
              </div>
              <div class="bar bar--rejected" 
                   [style.height.%]="getBarHeight(item.rejected)">
                <span class="bar-value" *ngIf="item.rejected > 0">{{ item.rejected }}</span>
              </div>
            </div>
            <div class="bar-label">{{ item.label }}</div>
          </div>
        </div>
      </div>
    </div>