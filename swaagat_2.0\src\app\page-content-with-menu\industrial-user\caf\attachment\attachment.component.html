<form [formGroup]="form" novalidate>

  <div class="mt-2">
    <span class="heder-color">GENERAL</span>
  </div>

  <!-- Self Certification (Form III) -->
  <div class="row no-padding-first-last">
    <div class="col-md-4 mt-2">
      <div class="form-group form-group-margin">
        <label for="selfCertification">Self Certification Form (Form III) <span class="red-font">*</span></label>
        <app-ilogi-file-upload [formControlName]="'selfCertification'" accept=".png,.jpg,.jpeg,.pdf"
          label="Choose File"></app-ilogi-file-upload>
      </div>
    </div>
    <div class="col-md-4 mt-5">
      <a href="/assets/documents/Self Certification - Form III.docx" target="__blank" class="btn download-btn ml-1">
        Download Form III Sample
      </a>
    </div>
  </div>

  <!-- Do you have trees? -->
  <div class="row no-padding-first-last mt-2">
    <div class="col-md-4">
      <div class="form-group form-group-margin">
        <label>Do you have trees in the land for industry? <span class="red-font">*</span></label>
        <app-ilogi-radio [radioOptions]="yesNoOptions" [formControlName]="'haveTree'"
          [submitted]="submitted"></app-ilogi-radio>
      </div>
    </div>
  </div>

  <!-- Type of Tree -->
  <div class="row no-padding-first-last" *ngIf="form.get('haveTree')?.value === 'YES'">
    <div class="col-md-4">
      <div class="form-group form-group-margin">
        <label>Type of Tree <span class="red-font">*</span></label>
        <app-ilogi-radio [radioOptions]="[
            { value: 'EXEMPTED', name: 'Exempted' },
            { value: 'NON_EXEMPTED', name: 'Non-Exempted' }
          ]" [formControlName]="'typeOfTree'" [submitted]="submitted"></app-ilogi-radio>
      </div>
    </div>
  </div>

  <!-- Conditional File Uploads -->
  <div class="row no-padding-first-last mt-2" *ngIf="form.get('haveTree')?.value === 'YES'">
    <div class="col-md-4" *ngIf="form.get('typeOfTree')?.value === 'EXEMPTED'">
      <div class="form-group form-group-margin">
        <label>Self Certificate Format 3A <span class="red-font">*</span></label>
        <app-ilogi-file-upload [formControlName]="'self_certificate_format_3A'" accept=".png,.jpg,.jpeg,.pdf"
          label="Upload File" (onRemove)="removeFile('self_certificate_format_3A')">
        </app-ilogi-file-upload>
      </div>
    </div>

    <div class="col-md-4" *ngIf="form.get('typeOfTree')?.value === 'NON_EXEMPTED'">
      <div class="form-group form-group-margin">
        <label>Tree Registration Certificate <span class="red-font">*</span></label>
        <app-ilogi-file-upload [formControlName]="'tree_registration_certificate'" accept=".png,.jpg,.jpeg,.pdf"
          label="Upload File" (onRemove)="removeFile('tree_registration_certificate')">
        </app-ilogi-file-upload>
      </div>
    </div>
  </div>

  <hr>

  <div class="mt-2">
    <span class="heder-color">ENTERPRISE RELATED ATTACHMENTS</span>
  </div>

  <div class="row top-space no-padding-first-last mt-2">
    <!-- Owner PAN -->
    <div class="col-md-4">
      <div class="form-group form-group-margin">
        <label>Owner PAN <span class="red-font">*</span></label>
        <app-ilogi-file-upload [formControlName]="'owner_pan_pdf'" accept=".png,.jpg,.jpeg,.pdf"
          label="Choose File"></app-ilogi-file-upload>
      </div>
    </div>

    <!-- Owner Aadhar -->
    <div class="col-md-4">
      <div class="form-group form-group-margin">
        <label>Owner Aadhar</label>
        <app-ilogi-file-upload [formControlName]="'owner_aadhar_pdf'" accept=".png,.jpg,.jpeg,.pdf" label="Choose File"
          (onRemove)="removeFile('owner_aadhar_pdf')">
        </app-ilogi-file-upload>
      </div>
    </div>

    <!-- Udyog Aadhar -->
    <div class="col-md-4">
      <div class="form-group form-group-margin">
        <label>Udyog Aadhar</label>
        <app-ilogi-file-upload [formControlName]="'udyog_aadhar'" accept=".png,.jpg,.jpeg,.pdf" label="Choose File"
          (onRemove)="removeFile('udyog_aadhar')">
        </app-ilogi-file-upload>
      </div>
    </div>
  </div>

  <!-- Input Fields -->
  <div class="row top-space no-padding-first-last mt-1">
    <div class="col-md-4">
      <app-ilogi-input fieldLabel="PAN Number" [mandatory]="true" formControlName="owner_pan_number"
        placeholder="Enter Number" maxlength="10"></app-ilogi-input>
    </div>
    <div class="col-md-4">
      <app-ilogi-input fieldLabel="Aadhar Number" formControlName="owner_aadhar_number" placeholder="Enter Number"
        maxlength="12"></app-ilogi-input>
    </div>
    <div class="col-md-4">
      <app-ilogi-input fieldLabel="Udyog Aadhar Number" formControlName="udyog_aadhar_number" placeholder="Enter Number"
        maxlength="255"></app-ilogi-input>
    </div>
  </div>

  <div class="row top-space no-padding-first-last mt-4">
    <div class="col-md-4">
      <app-ilogi-file-upload [formControlName]="'gst_certificate_pdf'" label="GST Certificate"
        accept=".png,.jpg,.jpeg,.pdf" (onRemove)="removeFile('gst_certificate_pdf')">
      </app-ilogi-file-upload>
    </div>
    <div class="col-md-4">
      <app-ilogi-input-date fieldLabel="Udyog Aadhar Date" formControlName="udyog_aadhar_registration_date"
        placeholder="DD/MM/YYYY"></app-ilogi-input-date>
    </div>
  </div>
  <div class="row top-space no-padding-first-last">
    <div class="col-md-4">
      <app-ilogi-input fieldLabel="GST Number" formControlName="gst_number" placeholder="Enter Number"
        maxlength="255"></app-ilogi-input>
    </div>
  </div>

  <hr>

  <div class="mt-2">
    <span class="heder-color">UNIT RELATED ATTACHMENTS</span>
  </div>
  <div class="grid-container mb-4">

    <!-- Combined Building Plan -->
    <div class="row top-space no-padding-first-last mt-2 ">
      <div class="">
        <app-ilogi-file-upload [formControlName]="'combinedBuilding'" label="Combined Building Plan"
          accept=".png,.jpg,.jpeg,.pdf" (onRemove)="removeFile('combinedBuilding')">
        </app-ilogi-file-upload>
      </div>
    </div>

    <!-- Land Details -->
    <div class="row top-space no-padding-first-last mt-2">
      <div class="">
        <app-ilogi-file-upload [formControlName]="'landRegistrationDeed'" label="Land Details"
          accept=".png,.jpg,.jpeg,.pdf"></app-ilogi-file-upload>
      </div>
    </div>

    <!-- Registration Details -->
    <div class="row top-space no-padding-first-last mt-2">
      <div class="">
        <app-ilogi-file-upload [formControlName]="'partnershipDetails'" label="Registration Details"
          accept=".png,.jpg,.jpeg,.pdf"></app-ilogi-file-upload>
      </div>
    </div>

    <!-- Process Flow Chart -->
    <div class="row top-space no-padding-first-last mt-2">
      <div class="">
        <app-ilogi-file-upload [formControlName]="'processFlowChart'" label="Process Flow Chart (Diagram / write up)"
          accept=".png,.jpg,.jpeg,.pdf"></app-ilogi-file-upload>
      </div>
    </div>

    <!-- Detail Project Report -->
    <div class="row top-space no-padding-first-last mt-2">
      <div class="">
        <app-ilogi-file-upload [formControlName]="'detailProjectReport'" label="Detail Project Report (DPR)"
          accept=".png,.jpg,.jpeg,.pdf" (onRemove)="removeFile('detailProjectReport')">
        </app-ilogi-file-upload>
      </div>
    </div>

    <!-- Property Tax Clearance Certificate -->
    <div class="row top-space no-padding-first-last mt-2">
      <div class="">
        <app-ilogi-file-upload [formControlName]="'propertyTaxClearanceCertificate'"
          label="Property Tax Clearance Certificate" accept=".png,.jpg,.jpeg,.pdf"></app-ilogi-file-upload>
      </div>
    </div>
  </div>

 <!-- Upload Additional Document -->
<div class="row no-padding-first-last mt-2">
  <div class="col-md-4">
    <app-ilogi-file-upload
      [formControlName]="'otherSupportingDoc'"
      label="Upload Additional Document"
      accept=".png,.jpg,.jpeg,.pdf,.doc,.docx"
      (fileSelected)="onAdditionalDocSelected($event)">
    </app-ilogi-file-upload>
  </div>
</div>

<!-- Table: Show Single Additional Document -->
<div class="row no-padding-first-last mt-3" *ngIf="additionalDoc.file">
  <div class="col-md-8">
    <table class="table table-bordered">
      <thead>
        <tr>
          <th>Document</th>
          <th>File Name</th>
          <th>Action</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>{{ additionalDoc.name }}</td>
          <td>{{ additionalDoc.file.name }}</td>
          <td>
            <a class="c-link" (click)="removeAdditionalDoc()">Remove</a>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

  <hr>

  <!-- Submit Buttons -->
  <div class="form-actions">
    <button type="button" class="btn btn-primary" (click)="saveAsDraft()">Save As Draft</button>
    <button type="button" class="btn btn-success" (click)="onSubmit()">Submit</button>
  </div>

</form>