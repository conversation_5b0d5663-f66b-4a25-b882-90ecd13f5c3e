{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-menu.mjs"], "sourcesContent": ["import { trigger, transition, style as style$1, animate } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, PLATFORM_ID, Inject, Pipe, inject, EventEmitter, forwardRef, Output, Input, ViewEncapsulation, Component, input, computed, signal, numberAttribute, booleanAttribute, ContentChildren, ContentChild, ViewChild, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport * as i1 from '@angular/platform-browser';\nimport { DomSanitizer } from '@angular/platform-browser';\nimport * as i3 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport { uuid, focus, relativePosition, absolutePosition, isTouchDevice, find, findSingle } from '@primeuix/utils';\nimport * as i5 from 'primeng/api';\nimport { SharedModule, PrimeTemplate } from 'primeng/api';\nimport * as i4 from 'primeng/badge';\nimport { BadgeModule } from 'primeng/badge';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { <PERSON><PERSON><PERSON><PERSON>, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { Ripple } from 'primeng/ripple';\nimport * as i6 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ZIndexUtils } from 'primeng/utils';\nimport { style } from '@primeuix/styles/menu';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"pMenuItemContent\", \"\"];\nconst _c1 = a0 => ({\n  $implicit: a0\n});\nconst _c2 = () => ({\n  exact: false\n});\nconst _c3 = a0 => ({\n  item: a0\n});\nfunction MenuItemContent_ng_container_1_a_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction MenuItemContent_ng_container_1_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 6);\n    i0.ɵɵtemplate(1, MenuItemContent_ng_container_1_a_1_ng_container_1_Template, 1, 0, \"ng-container\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    const itemContent_r3 = i0.ɵɵreference(4);\n    i0.ɵɵclassMap(ctx_r1.cx(\"itemLink\"));\n    i0.ɵɵproperty(\"target\", ctx_r1.item.target);\n    i0.ɵɵattribute(\"title\", ctx_r1.item.title)(\"href\", ctx_r1.item.url || null, i0.ɵɵsanitizeUrl)(\"data-automationid\", ctx_r1.item.automationId)(\"tabindex\", -1)(\"data-pc-section\", \"action\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", itemContent_r3)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(10, _c1, ctx_r1.item));\n  }\n}\nfunction MenuItemContent_ng_container_1_a_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction MenuItemContent_ng_container_1_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 8);\n    i0.ɵɵtemplate(1, MenuItemContent_ng_container_1_a_2_ng_container_1_Template, 1, 0, \"ng-container\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    const itemContent_r3 = i0.ɵɵreference(4);\n    i0.ɵɵclassMap(ctx_r1.cx(\"itemLink\"));\n    i0.ɵɵproperty(\"routerLink\", ctx_r1.item.routerLink)(\"queryParams\", ctx_r1.item.queryParams)(\"routerLinkActiveOptions\", ctx_r1.item.routerLinkActiveOptions || i0.ɵɵpureFunction0(18, _c2))(\"target\", ctx_r1.item.target)(\"fragment\", ctx_r1.item.fragment)(\"queryParamsHandling\", ctx_r1.item.queryParamsHandling)(\"preserveFragment\", ctx_r1.item.preserveFragment)(\"skipLocationChange\", ctx_r1.item.skipLocationChange)(\"replaceUrl\", ctx_r1.item.replaceUrl)(\"state\", ctx_r1.item.state);\n    i0.ɵɵattribute(\"data-automationid\", ctx_r1.item.automationId)(\"tabindex\", -1)(\"data-pc-section\", \"action\")(\"title\", ctx_r1.item.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", itemContent_r3)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(19, _c1, ctx_r1.item));\n  }\n}\nfunction MenuItemContent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenuItemContent_ng_container_1_a_1_Template, 2, 12, \"a\", 4)(2, MenuItemContent_ng_container_1_a_2_Template, 2, 21, \"a\", 5);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r1.item == null ? null : ctx_r1.item.routerLink));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.item == null ? null : ctx_r1.item.routerLink);\n  }\n}\nfunction MenuItemContent_ng_container_2_1_ng_template_0_Template(rf, ctx) {}\nfunction MenuItemContent_ng_container_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MenuItemContent_ng_container_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction MenuItemContent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenuItemContent_ng_container_2_1_Template, 1, 0, null, 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c1, ctx_r1.item));\n  }\n}\nfunction MenuItemContent_ng_template_3_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\");\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(ctx_r1.item.iconStyle);\n    i0.ɵɵclassMap(ctx_r1.cx(\"itemIcon\", i0.ɵɵpureFunction1(4, _c3, ctx_r1.item)));\n  }\n}\nfunction MenuItemContent_ng_template_3_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r1.cx(\"itemLabel\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.item.label);\n  }\n}\nfunction MenuItemContent_ng_template_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 12);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.sanitizeHtml(ctx_r1.item.label), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction MenuItemContent_ng_template_3_p_badge_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-badge\", 13);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"styleClass\", ctx_r1.item.badgeStyleClass)(\"value\", ctx_r1.item.badge);\n  }\n}\nfunction MenuItemContent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MenuItemContent_ng_template_3_span_0_Template, 1, 6, \"span\", 9)(1, MenuItemContent_ng_template_3_span_1_Template, 2, 3, \"span\", 10)(2, MenuItemContent_ng_template_3_ng_template_2_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(4, MenuItemContent_ng_template_3_p_badge_4_Template, 1, 2, \"p-badge\", 11);\n  }\n  if (rf & 2) {\n    const htmlLabel_r4 = i0.ɵɵreference(3);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.item.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.item.escape !== false)(\"ngIfElse\", htmlLabel_r4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.item.badge);\n  }\n}\nconst _c4 = [\"start\"];\nconst _c5 = [\"end\"];\nconst _c6 = [\"header\"];\nconst _c7 = [\"item\"];\nconst _c8 = [\"submenuheader\"];\nconst _c9 = [\"list\"];\nconst _c10 = [\"container\"];\nconst _c11 = (a0, a1) => ({\n  showTransitionParams: a0,\n  hideTransitionParams: a1\n});\nconst _c12 = a0 => ({\n  value: \"visible\",\n  params: a0\n});\nconst _c13 = (a0, a1) => ({\n  item: a0,\n  id: a1\n});\nfunction Menu_div_0_div_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Menu_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, Menu_div_0_div_2_ng_container_1_Template, 1, 0, \"ng-container\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r1.cx(\"start\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"start\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.startTemplate ?? ctx_r1._startTemplate);\n  }\n}\nfunction Menu_div_0_5_ng_template_0_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 12);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassMap(ctx_r1.cx(\"separator\"));\n  }\n}\nfunction Menu_div_0_5_ng_template_0_li_1_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const submenu_r3 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(submenu_r3.label);\n  }\n}\nfunction Menu_div_0_5_ng_template_0_li_1_ng_container_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 16);\n  }\n  if (rf & 2) {\n    const submenu_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.sanitizeHtml(submenu_r3.label), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction Menu_div_0_5_ng_template_0_li_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Menu_div_0_5_ng_template_0_li_1_ng_container_1_span_1_Template, 2, 1, \"span\", 15)(2, Menu_div_0_5_ng_template_0_li_1_ng_container_1_ng_template_2_Template, 1, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const htmlSubmenuLabel_r4 = i0.ɵɵreference(3);\n    const submenu_r3 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", submenu_r3.escape !== false)(\"ngIfElse\", htmlSubmenuLabel_r4);\n  }\n}\nfunction Menu_div_0_5_ng_template_0_li_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Menu_div_0_5_ng_template_0_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 13);\n    i0.ɵɵtemplate(1, Menu_div_0_5_ng_template_0_li_1_ng_container_1_Template, 4, 2, \"ng-container\", 7)(2, Menu_div_0_5_ng_template_0_li_1_ng_container_2_Template, 1, 0, \"ng-container\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    const submenu_r3 = ctx_r4.$implicit;\n    const i_r6 = ctx_r4.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(ctx_r1.cx(\"submenuLabel\"));\n    i0.ɵɵproperty(\"tooltipOptions\", submenu_r3.tooltipOptions);\n    i0.ɵɵattribute(\"data-automationid\", submenu_r3.automationId)(\"id\", ctx_r1.menuitemId(submenu_r3, ctx_r1.id, i_r6));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.submenuHeaderTemplate && !ctx_r1._submenuHeaderTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.submenuHeaderTemplate ?? ctx_r1._submenuHeaderTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(8, _c1, submenu_r3));\n  }\n}\nfunction Menu_div_0_5_ng_template_0_ng_template_2_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 12);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵclassMap(ctx_r1.cx(\"separator\"));\n  }\n}\nfunction Menu_div_0_5_ng_template_0_ng_template_2_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 18);\n    i0.ɵɵlistener(\"onMenuItemClick\", function Menu_div_0_5_ng_template_0_ng_template_2_li_1_Template_li_onMenuItemClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r7 = i0.ɵɵnextContext();\n      const item_r9 = ctx_r7.$implicit;\n      const j_r10 = ctx_r7.index;\n      const i_r6 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.itemClick($event, ctx_r1.menuitemId(item_r9, ctx_r1.id, i_r6, j_r10)));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    const item_r9 = ctx_r7.$implicit;\n    const j_r10 = ctx_r7.index;\n    const i_r6 = i0.ɵɵnextContext().index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleMap(item_r9.style);\n    i0.ɵɵclassMap(ctx_r1.cn(ctx_r1.cx(\"item\", i0.ɵɵpureFunction2(13, _c13, item_r9, ctx_r1.menuitemId(item_r9, ctx_r1.id, i_r6, j_r10))), item_r9 == null ? null : item_r9.styleClass));\n    i0.ɵɵproperty(\"pMenuItemContent\", item_r9)(\"itemTemplate\", ctx_r1.itemTemplate ?? ctx_r1._itemTemplate)(\"tooltipOptions\", item_r9.tooltipOptions);\n    i0.ɵɵattribute(\"data-pc-section\", \"menuitem\")(\"aria-label\", ctx_r1.label(item_r9.label))(\"data-p-focused\", ctx_r1.isItemFocused(ctx_r1.menuitemId(item_r9, ctx_r1.id, i_r6, j_r10)))(\"data-p-disabled\", ctx_r1.disabled(item_r9.disabled))(\"aria-disabled\", ctx_r1.disabled(item_r9.disabled))(\"id\", ctx_r1.menuitemId(item_r9, ctx_r1.id, i_r6, j_r10));\n  }\n}\nfunction Menu_div_0_5_ng_template_0_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Menu_div_0_5_ng_template_0_ng_template_2_li_0_Template, 1, 2, \"li\", 10)(1, Menu_div_0_5_ng_template_0_ng_template_2_li_1_Template, 1, 16, \"li\", 17);\n  }\n  if (rf & 2) {\n    const item_r9 = ctx.$implicit;\n    const submenu_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngIf\", item_r9.separator && (item_r9.visible !== false || submenu_r3.visible !== false));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r9.separator && item_r9.visible !== false && (item_r9.visible !== undefined || submenu_r3.visible !== false));\n  }\n}\nfunction Menu_div_0_5_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Menu_div_0_5_ng_template_0_li_0_Template, 1, 2, \"li\", 10)(1, Menu_div_0_5_ng_template_0_li_1_Template, 3, 10, \"li\", 11)(2, Menu_div_0_5_ng_template_0_ng_template_2_Template, 2, 2, \"ng-template\", 9);\n  }\n  if (rf & 2) {\n    const submenu_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", submenu_r3.separator && submenu_r3.visible !== false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !submenu_r3.separator);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", submenu_r3.items);\n  }\n}\nfunction Menu_div_0_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Menu_div_0_5_ng_template_0_Template, 3, 3, \"ng-template\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.model);\n  }\n}\nfunction Menu_div_0_6_ng_template_0_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 12);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassMap(ctx_r1.cx(\"separator\"));\n  }\n}\nfunction Menu_div_0_6_ng_template_0_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 20);\n    i0.ɵɵlistener(\"onMenuItemClick\", function Menu_div_0_6_ng_template_0_li_1_Template_li_onMenuItemClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r11 = i0.ɵɵnextContext();\n      const item_r13 = ctx_r11.$implicit;\n      const i_r14 = ctx_r11.index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.itemClick($event, ctx_r1.menuitemId(item_r13, ctx_r1.id, i_r14)));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    const item_r13 = ctx_r11.$implicit;\n    const i_r14 = ctx_r11.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(ctx_r1.cn(ctx_r1.cx(\"item\", i0.ɵɵpureFunction2(12, _c13, item_r13, ctx_r1.menuitemId(item_r13, ctx_r1.id, i_r14))), item_r13 == null ? null : item_r13.styleClass));\n    i0.ɵɵproperty(\"pMenuItemContent\", item_r13)(\"itemTemplate\", ctx_r1.itemTemplate ?? ctx_r1._itemTemplate)(\"ngStyle\", item_r13.style)(\"tooltipOptions\", item_r13.tooltipOptions);\n    i0.ɵɵattribute(\"data-pc-section\", \"menuitem\")(\"aria-label\", ctx_r1.label(item_r13.label))(\"data-p-focused\", ctx_r1.isItemFocused(ctx_r1.menuitemId(item_r13, ctx_r1.id, i_r14)))(\"data-p-disabled\", ctx_r1.disabled(item_r13.disabled))(\"aria-disabled\", ctx_r1.disabled(item_r13.disabled))(\"id\", ctx_r1.menuitemId(item_r13, ctx_r1.id, i_r14));\n  }\n}\nfunction Menu_div_0_6_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Menu_div_0_6_ng_template_0_li_0_Template, 1, 2, \"li\", 10)(1, Menu_div_0_6_ng_template_0_li_1_Template, 1, 15, \"li\", 19);\n  }\n  if (rf & 2) {\n    const item_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", item_r13.separator && item_r13.visible !== false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r13.separator && item_r13.visible !== false);\n  }\n}\nfunction Menu_div_0_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Menu_div_0_6_ng_template_0_Template, 2, 2, \"ng-template\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.model);\n  }\n}\nfunction Menu_div_0_div_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Menu_div_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, Menu_div_0_div_7_ng_container_1_Template, 1, 0, \"ng-container\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r1.cx(\"end\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"end\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.endTemplate ?? ctx_r1._endTemplate);\n  }\n}\nfunction Menu_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4, 0);\n    i0.ɵɵlistener(\"click\", function Menu_div_0_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onOverlayClick($event));\n    })(\"@overlayAnimation.start\", function Menu_div_0_Template_div_animation_overlayAnimation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onOverlayAnimationStart($event));\n    })(\"@overlayAnimation.done\", function Menu_div_0_Template_div_animation_overlayAnimation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onOverlayAnimationEnd($event));\n    });\n    i0.ɵɵtemplate(2, Menu_div_0_div_2_Template, 2, 4, \"div\", 5);\n    i0.ɵɵelementStart(3, \"ul\", 6, 1);\n    i0.ɵɵlistener(\"focus\", function Menu_div_0_Template_ul_focus_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onListFocus($event));\n    })(\"blur\", function Menu_div_0_Template_ul_blur_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onListBlur($event));\n    })(\"keydown\", function Menu_div_0_Template_ul_keydown_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onListKeyDown($event));\n    });\n    i0.ɵɵtemplate(5, Menu_div_0_5_Template, 1, 1, null, 7)(6, Menu_div_0_6_Template, 1, 1, null, 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, Menu_div_0_div_7_Template, 2, 4, \"div\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(ctx_r1.sx(\"root\"));\n    i0.ɵɵclassMap(ctx_r1.cn(ctx_r1.cx(\"root\"), ctx_r1.styleClass));\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.style)(\"@overlayAnimation\", i0.ɵɵpureFunction1(24, _c12, i0.ɵɵpureFunction2(21, _c11, ctx_r1.showTransitionOptions, ctx_r1.hideTransitionOptions)))(\"@.disabled\", ctx_r1.popup !== true);\n    i0.ɵɵattribute(\"data-pc-name\", \"menu\")(\"id\", ctx_r1.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.startTemplate ?? ctx_r1._startTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1.cx(\"list\"));\n    i0.ɵɵattribute(\"id\", ctx_r1.id + \"_list\")(\"tabindex\", ctx_r1.getTabIndexValue())(\"data-pc-section\", \"menu\")(\"aria-activedescendant\", ctx_r1.activedescendant())(\"aria-label\", ctx_r1.ariaLabel)(\"aria-labelledBy\", ctx_r1.ariaLabelledBy);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasSubMenu());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.hasSubMenu());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.endTemplate ?? ctx_r1._endTemplate);\n  }\n}\nconst inlineStyles = {\n  root: ({\n    instance\n  }) => ({\n    position: instance.popup ? 'absolute' : 'relative'\n  })\n};\nconst classes = {\n  root: ({\n    instance\n  }) => ['p-menu p-component', {\n    'p-menu-overlay': instance.popup\n  }],\n  start: 'p-menu-start',\n  list: 'p-menu-list',\n  submenuLabel: 'p-menu-submenu-label',\n  separator: 'p-menu-separator',\n  end: 'p-menu-end',\n  item: ({\n    instance,\n    item,\n    id\n  }) => ['p-menu-item', {\n    'p-focus': instance.focusedOptionId() && id === instance.focusedOptionId(),\n    'p-disabled': instance.disabled(item.disabled)\n  }, item.styleClass],\n  itemContent: 'p-menu-item-content',\n  itemLink: 'p-menu-item-link',\n  itemIcon: ({\n    item\n  }) => ['p-menu-item-icon', item.icon, item.iconClass],\n  itemLabel: 'p-menu-item-label'\n};\nclass MenuStyle extends BaseStyle {\n  name = 'menu';\n  theme = style;\n  classes = classes;\n  inlineStyles = inlineStyles;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMenuStyle_BaseFactory;\n    return function MenuStyle_Factory(__ngFactoryType__) {\n      return (ɵMenuStyle_BaseFactory || (ɵMenuStyle_BaseFactory = i0.ɵɵgetInheritedFactory(MenuStyle)))(__ngFactoryType__ || MenuStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MenuStyle,\n    factory: MenuStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MenuStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * Menu is a navigation / command component that supports dynamic and static positioning.\n *\n * [Live Demo](https://www.primeng.org/menu/)\n *\n * @module menustyle\n *\n */\nvar MenuClasses;\n(function (MenuClasses) {\n  /**\n   * Class name of the root element\n   */\n  MenuClasses[\"root\"] = \"p-menu\";\n  /**\n   * Class name of the start element\n   */\n  MenuClasses[\"start\"] = \"p-menu-start\";\n  /**\n   * Class name of the list element\n   */\n  MenuClasses[\"list\"] = \"p-menu-list\";\n  /**\n   * Class name of the submenu item element\n   */\n  MenuClasses[\"submenuItem\"] = \"p-menu-submenu-item\";\n  /**\n   * Class name of the separator element\n   */\n  MenuClasses[\"separator\"] = \"p-menu-separator\";\n  /**\n   * Class name of the end element\n   */\n  MenuClasses[\"end\"] = \"p-menu-end\";\n  /**\n   * Class name of the item element\n   */\n  MenuClasses[\"item\"] = \"p-menu-item\";\n  /**\n   * Class name of the item content element\n   */\n  MenuClasses[\"itemContent\"] = \"p-menu-item-content\";\n  /**\n   * Class name of the item link element\n   */\n  MenuClasses[\"itemLink\"] = \"p-menu-item-link\";\n  /**\n   * Class name of the item icon element\n   */\n  MenuClasses[\"itemIcon\"] = \"p-menu-item-icon\";\n  /**\n   * Class name of the item label element\n   */\n  MenuClasses[\"itemLabel\"] = \"p-menu-item-label\";\n})(MenuClasses || (MenuClasses = {}));\nclass SafeHtmlPipe {\n  platformId;\n  sanitizer;\n  constructor(platformId, sanitizer) {\n    this.platformId = platformId;\n    this.sanitizer = sanitizer;\n  }\n  transform(value) {\n    if (!value || !isPlatformBrowser(this.platformId)) {\n      return value;\n    }\n    return this.sanitizer.bypassSecurityTrustHtml(value);\n  }\n  static ɵfac = function SafeHtmlPipe_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || SafeHtmlPipe)(i0.ɵɵdirectiveInject(PLATFORM_ID, 16), i0.ɵɵdirectiveInject(i1.DomSanitizer, 16));\n  };\n  static ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"safeHtml\",\n    type: SafeHtmlPipe,\n    pure: true\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SafeHtmlPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'safeHtml',\n      standalone: true\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i1.DomSanitizer\n  }], null);\n})();\nfunction sanitizeHtml(value) {\n  const platformId = inject(PLATFORM_ID);\n  const sanitizer = inject(DomSanitizer);\n  if (!value || !isPlatformBrowser(platformId)) {\n    return value;\n  }\n  return sanitizer.bypassSecurityTrustHtml(value);\n}\nclass MenuItemContent extends BaseComponent {\n  item;\n  itemTemplate;\n  onMenuItemClick = new EventEmitter();\n  menu;\n  _componentStyle = inject(MenuStyle);\n  constructor(menu) {\n    super();\n    this.menu = menu;\n  }\n  onItemClick(event, item) {\n    this.onMenuItemClick.emit({\n      originalEvent: event,\n      item\n    });\n  }\n  static ɵfac = function MenuItemContent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MenuItemContent)(i0.ɵɵdirectiveInject(forwardRef(() => Menu)));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MenuItemContent,\n    selectors: [[\"\", \"pMenuItemContent\", \"\"]],\n    inputs: {\n      item: [0, \"pMenuItemContent\", \"item\"],\n      itemTemplate: \"itemTemplate\"\n    },\n    outputs: {\n      onMenuItemClick: \"onMenuItemClick\"\n    },\n    features: [i0.ɵɵProvidersFeature([MenuStyle]), i0.ɵɵInheritDefinitionFeature],\n    attrs: _c0,\n    decls: 5,\n    vars: 5,\n    consts: [[\"itemContent\", \"\"], [\"htmlLabel\", \"\"], [3, \"click\"], [4, \"ngIf\"], [\"pRipple\", \"\", 3, \"class\", \"target\", 4, \"ngIf\"], [\"routerLinkActive\", \"p-menu-item-link-active\", \"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActiveOptions\", \"class\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"target\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"routerLinkActive\", \"p-menu-item-link-active\", \"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActiveOptions\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\"], [3, \"class\", \"style\", 4, \"ngIf\"], [3, \"class\", 4, \"ngIf\", \"ngIfElse\"], [3, \"styleClass\", \"value\", 4, \"ngIf\"], [1, \"p-menu-item-label\", 3, \"innerHTML\"], [3, \"styleClass\", \"value\"]],\n    template: function MenuItemContent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 2);\n        i0.ɵɵlistener(\"click\", function MenuItemContent_Template_div_click_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onItemClick($event, ctx.item));\n        });\n        i0.ɵɵtemplate(1, MenuItemContent_ng_container_1_Template, 3, 2, \"ng-container\", 3)(2, MenuItemContent_ng_container_2_Template, 2, 4, \"ng-container\", 3)(3, MenuItemContent_ng_template_3_Template, 5, 4, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.cx(\"itemContent\"));\n        i0.ɵɵattribute(\"data-pc-section\", \"content\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.itemTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.itemTemplate);\n      }\n    },\n    dependencies: [CommonModule, i2.NgIf, i2.NgTemplateOutlet, RouterModule, i3.RouterLink, i3.RouterLinkActive, Ripple, TooltipModule, BadgeModule, i4.Badge, SharedModule],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MenuItemContent, [{\n    type: Component,\n    args: [{\n      selector: '[pMenuItemContent]',\n      standalone: true,\n      imports: [CommonModule, RouterModule, Ripple, TooltipModule, BadgeModule, SharedModule],\n      template: ` <div [class]=\"cx('itemContent')\" (click)=\"onItemClick($event, item)\" [attr.data-pc-section]=\"'content'\">\n        <ng-container *ngIf=\"!itemTemplate\">\n            <a\n                *ngIf=\"!item?.routerLink\"\n                [attr.title]=\"item.title\"\n                [attr.href]=\"item.url || null\"\n                [attr.data-automationid]=\"item.automationId\"\n                [attr.tabindex]=\"-1\"\n                [attr.data-pc-section]=\"'action'\"\n                [class]=\"cx('itemLink')\"\n                [target]=\"item.target\"\n                pRipple\n            >\n                <ng-container *ngTemplateOutlet=\"itemContent; context: { $implicit: item }\"></ng-container>\n            </a>\n            <a\n                *ngIf=\"item?.routerLink\"\n                [routerLink]=\"item.routerLink\"\n                [attr.data-automationid]=\"item.automationId\"\n                [attr.tabindex]=\"-1\"\n                [attr.data-pc-section]=\"'action'\"\n                [attr.title]=\"item.title\"\n                [queryParams]=\"item.queryParams\"\n                routerLinkActive=\"p-menu-item-link-active\"\n                [routerLinkActiveOptions]=\"item.routerLinkActiveOptions || { exact: false }\"\n                [class]=\"cx('itemLink')\"\n                [target]=\"item.target\"\n                [fragment]=\"item.fragment\"\n                [queryParamsHandling]=\"item.queryParamsHandling\"\n                [preserveFragment]=\"item.preserveFragment\"\n                [skipLocationChange]=\"item.skipLocationChange\"\n                [replaceUrl]=\"item.replaceUrl\"\n                [state]=\"item.state\"\n                pRipple\n            >\n                <ng-container *ngTemplateOutlet=\"itemContent; context: { $implicit: item }\"></ng-container>\n            </a>\n        </ng-container>\n\n        <ng-container *ngIf=\"itemTemplate\">\n            <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item }\"></ng-template>\n        </ng-container>\n\n        <ng-template #itemContent>\n            <span [class]=\"cx('itemIcon', { item })\" *ngIf=\"item.icon\" [style]=\"item.iconStyle\"></span>\n            <span [class]=\"cx('itemLabel')\" *ngIf=\"item.escape !== false; else htmlLabel\">{{ item.label }}</span>\n            <ng-template #htmlLabel><span class=\"p-menu-item-label\" [innerHTML]=\"sanitizeHtml(item.label)\"></span></ng-template>\n            <p-badge *ngIf=\"item.badge\" [styleClass]=\"item.badgeStyleClass\" [value]=\"item.badge\" />\n        </ng-template>\n    </div>`,\n      encapsulation: ViewEncapsulation.None,\n      providers: [MenuStyle]\n    }]\n  }], () => [{\n    type: Menu,\n    decorators: [{\n      type: Inject,\n      args: [forwardRef(() => Menu)]\n    }]\n  }], {\n    item: [{\n      type: Input,\n      args: ['pMenuItemContent']\n    }],\n    itemTemplate: [{\n      type: Input\n    }],\n    onMenuItemClick: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * Menu is a navigation / command component that supports dynamic and static positioning.\n * @group Components\n */\nclass Menu extends BaseComponent {\n  overlayService;\n  /**\n   * An array of menuitems.\n   * @group Props\n   */\n  model;\n  /**\n   * Defines if menu would displayed as a popup.\n   * @group Props\n   */\n  popup;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Whether to automatically manage layering.\n   * @group Props\n   */\n  autoZIndex = true;\n  /**\n   * Base zIndex value to use in layering.\n   * @group Props\n   */\n  baseZIndex = 0;\n  /**\n   * Transition options of the show animation.\n   * @group Props\n   */\n  showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n  /**\n   * Transition options of the hide animation.\n   * @group Props\n   */\n  hideTransitionOptions = '.1s linear';\n  /**\n   * Defines a string value that labels an interactive element.\n   * @group Props\n   */\n  ariaLabel;\n  /**\n   * Identifier of the underlying input element.\n   * @group Props\n   */\n  ariaLabelledBy;\n  /**\n   * Current id state as a string.\n   * @group Props\n   */\n  id;\n  /**\n   * Index of the element in tabbing order.\n   * @group Props\n   */\n  tabindex = 0;\n  /**\n   * Target element to attach the overlay, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n   * @defaultValue 'self'\n   * @group Props\n   */\n  appendTo = input(undefined, ...(ngDevMode ? [{\n    debugName: \"appendTo\"\n  }] : []));\n  /**\n   * Callback to invoke when overlay menu is shown.\n   * @group Emits\n   */\n  onShow = new EventEmitter();\n  /**\n   * Callback to invoke when overlay menu is hidden.\n   * @group Emits\n   */\n  onHide = new EventEmitter();\n  /**\n   * Callback to invoke when the list loses focus.\n   * @param {Event} event - blur event.\n   * @group Emits\n   */\n  onBlur = new EventEmitter();\n  /**\n   * Callback to invoke when the list receives focus.\n   * @param {Event} event - focus event.\n   * @group Emits\n   */\n  onFocus = new EventEmitter();\n  listViewChild;\n  containerViewChild;\n  $appendTo = computed(() => this.appendTo() || this.config.overlayAppendTo(), ...(ngDevMode ? [{\n    debugName: \"$appendTo\"\n  }] : []));\n  container;\n  scrollHandler;\n  documentClickListener;\n  documentResizeListener;\n  preventDocumentDefault;\n  target;\n  visible;\n  focusedOptionId = computed(() => {\n    return this.focusedOptionIndex() !== -1 ? this.focusedOptionIndex() : null;\n  }, ...(ngDevMode ? [{\n    debugName: \"focusedOptionId\"\n  }] : []));\n  focusedOptionIndex = signal(-1, ...(ngDevMode ? [{\n    debugName: \"focusedOptionIndex\"\n  }] : []));\n  selectedOptionIndex = signal(-1, ...(ngDevMode ? [{\n    debugName: \"selectedOptionIndex\"\n  }] : []));\n  focused = false;\n  overlayVisible = false;\n  relativeAlign;\n  _componentStyle = inject(MenuStyle);\n  constructor(overlayService) {\n    super();\n    this.overlayService = overlayService;\n    this.id = this.id || uuid('pn_id_');\n  }\n  /**\n   * Toggles the visibility of the popup menu.\n   * @param {Event} event - Browser event.\n   * @group Method\n   */\n  toggle(event) {\n    if (this.visible) this.hide();else this.show(event);\n    this.preventDocumentDefault = true;\n  }\n  /**\n   * Displays the popup menu.\n   * @param {Event} event - Browser event.\n   * @group Method\n   */\n  show(event) {\n    this.target = event.currentTarget;\n    this.relativeAlign = event.relativeAlign;\n    this.visible = true;\n    this.preventDocumentDefault = true;\n    this.overlayVisible = true;\n    this.cd.markForCheck();\n  }\n  ngOnInit() {\n    super.ngOnInit();\n    if (!this.popup) {\n      this.bindDocumentClickListener();\n    }\n  }\n  /**\n   * Defines template option for start.\n   * @group Templates\n   */\n  startTemplate;\n  _startTemplate;\n  /**\n   * Defines template option for end.\n   * @group Templates\n   */\n  endTemplate;\n  _endTemplate;\n  /**\n   * Defines template option for header.\n   * @group Templates\n   */\n  headerTemplate;\n  _headerTemplate;\n  /**\n   * Defines template option for item.\n   * @group Templates\n   */\n  itemTemplate;\n  _itemTemplate;\n  /**\n   * Defines template option for item.\n   * @group Templates\n   */\n  submenuHeaderTemplate;\n  _submenuHeaderTemplate;\n  templates;\n  ngAfterContentInit() {\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'start':\n          this._startTemplate = item.template;\n          break;\n        case 'end':\n          this._endTemplate = item.template;\n          break;\n        case 'item':\n          this._itemTemplate = item.template;\n          break;\n        case 'submenuheader':\n          this._submenuHeaderTemplate = item.template;\n          break;\n        default:\n          this._itemTemplate = item.template;\n          break;\n      }\n    });\n  }\n  getTabIndexValue() {\n    return this.tabindex !== undefined ? this.tabindex.toString() : null;\n  }\n  onOverlayAnimationStart(event) {\n    switch (event.toState) {\n      case 'visible':\n        if (this.popup) {\n          this.container = event.element;\n          this.moveOnTop();\n          this.onShow.emit({});\n          this.attrSelector && this.container.setAttribute(this.attrSelector, '');\n          this.appendOverlay();\n          this.alignOverlay();\n          this.bindDocumentClickListener();\n          this.bindDocumentResizeListener();\n          this.bindScrollListener();\n          focus(this.listViewChild.nativeElement);\n        }\n        break;\n      case 'void':\n        this.onOverlayHide();\n        this.onHide.emit({});\n        break;\n    }\n  }\n  onOverlayAnimationEnd(event) {\n    switch (event.toState) {\n      case 'void':\n        if (this.autoZIndex) {\n          ZIndexUtils.clear(event.element);\n        }\n        break;\n    }\n  }\n  alignOverlay() {\n    if (this.relativeAlign) relativePosition(this.container, this.target);else absolutePosition(this.container, this.target);\n  }\n  appendOverlay() {\n    DomHandler.appendOverlay(this.container, this.$appendTo() === 'body' ? this.document.body : this.$appendTo(), this.$appendTo());\n  }\n  restoreOverlayAppend() {\n    if (this.container && this.$appendTo() !== 'self') {\n      this.renderer.appendChild(this.el.nativeElement, this.container);\n    }\n  }\n  moveOnTop() {\n    if (this.autoZIndex) {\n      ZIndexUtils.set('menu', this.container, this.baseZIndex + this.config.zIndex.menu);\n    }\n  }\n  /**\n   * Hides the popup menu.\n   * @group Method\n   */\n  hide() {\n    this.visible = false;\n    this.relativeAlign = false;\n    this.cd.markForCheck();\n  }\n  onWindowResize() {\n    if (this.visible && !isTouchDevice()) {\n      this.hide();\n    }\n  }\n  menuitemId(item, id, index, childIndex) {\n    return item?.id ?? `${id}_${index}${childIndex !== undefined ? '_' + childIndex : ''}`;\n  }\n  isItemFocused(id) {\n    return this.focusedOptionId() === id;\n  }\n  label(label) {\n    return typeof label === 'function' ? label() : label;\n  }\n  disabled(disabled) {\n    return typeof disabled === 'function' ? disabled() : typeof disabled === 'undefined' ? false : disabled;\n  }\n  activedescendant() {\n    return this.focused ? this.focusedOptionId() : undefined;\n  }\n  onListFocus(event) {\n    if (!this.focused) {\n      this.focused = true;\n      !this.popup && this.changeFocusedOptionIndex(0);\n      this.onFocus.emit(event);\n    }\n  }\n  onListBlur(event) {\n    if (this.focused) {\n      this.focused = false;\n      this.changeFocusedOptionIndex(-1);\n      this.selectedOptionIndex.set(-1);\n      this.focusedOptionIndex.set(-1);\n      this.onBlur.emit(event);\n    }\n  }\n  onListKeyDown(event) {\n    switch (event.code) {\n      case 'ArrowDown':\n        this.onArrowDownKey(event);\n        break;\n      case 'ArrowUp':\n        this.onArrowUpKey(event);\n        break;\n      case 'Home':\n        this.onHomeKey(event);\n        break;\n      case 'End':\n        this.onEndKey(event);\n        break;\n      case 'Enter':\n        this.onEnterKey(event);\n        break;\n      case 'NumpadEnter':\n        this.onEnterKey(event);\n        break;\n      case 'Space':\n        this.onSpaceKey(event);\n        break;\n      case 'Escape':\n      case 'Tab':\n        if (this.popup) {\n          focus(this.target);\n          this.hide();\n        }\n        this.overlayVisible && this.hide();\n        break;\n      default:\n        break;\n    }\n  }\n  onArrowDownKey(event) {\n    const optionIndex = this.findNextOptionIndex(this.focusedOptionIndex());\n    this.changeFocusedOptionIndex(optionIndex);\n    event.preventDefault();\n  }\n  onArrowUpKey(event) {\n    if (event.altKey && this.popup) {\n      focus(this.target);\n      this.hide();\n      event.preventDefault();\n    } else {\n      const optionIndex = this.findPrevOptionIndex(this.focusedOptionIndex());\n      this.changeFocusedOptionIndex(optionIndex);\n      event.preventDefault();\n    }\n  }\n  onHomeKey(event) {\n    this.changeFocusedOptionIndex(0);\n    event.preventDefault();\n  }\n  onEndKey(event) {\n    this.changeFocusedOptionIndex(find(this.containerViewChild.nativeElement, 'li[data-pc-section=\"menuitem\"][data-p-disabled=\"false\"]').length - 1);\n    event.preventDefault();\n  }\n  onEnterKey(event) {\n    const element = findSingle(this.containerViewChild.nativeElement, `li[id=\"${`${this.focusedOptionIndex()}`}\"]`);\n    const anchorElement = element && findSingle(element, 'a[data-pc-section=\"action\"]');\n    this.popup && focus(this.target);\n    anchorElement ? anchorElement.click() : element && element.click();\n    event.preventDefault();\n  }\n  onSpaceKey(event) {\n    this.onEnterKey(event);\n  }\n  findNextOptionIndex(index) {\n    const links = find(this.containerViewChild.nativeElement, 'li[data-pc-section=\"menuitem\"][data-p-disabled=\"false\"]');\n    const matchedOptionIndex = [...links].findIndex(link => link.id === index);\n    return matchedOptionIndex > -1 ? matchedOptionIndex + 1 : 0;\n  }\n  findPrevOptionIndex(index) {\n    const links = find(this.containerViewChild.nativeElement, 'li[data-pc-section=\"menuitem\"][data-p-disabled=\"false\"]');\n    const matchedOptionIndex = [...links].findIndex(link => link.id === index);\n    return matchedOptionIndex > -1 ? matchedOptionIndex - 1 : 0;\n  }\n  changeFocusedOptionIndex(index) {\n    const links = find(this.containerViewChild.nativeElement, 'li[data-pc-section=\"menuitem\"][data-p-disabled=\"false\"]');\n    if (links.length > 0) {\n      let order = index >= links.length ? links.length - 1 : index < 0 ? 0 : index;\n      order > -1 && this.focusedOptionIndex.set(links[order].getAttribute('id'));\n    }\n  }\n  itemClick(event, id) {\n    const {\n      originalEvent,\n      item\n    } = event;\n    if (!this.focused) {\n      this.focused = true;\n      this.onFocus.emit();\n    }\n    if (item.disabled) {\n      originalEvent.preventDefault();\n      return;\n    }\n    if (!item.url && !item.routerLink) {\n      originalEvent.preventDefault();\n    }\n    if (item.command) {\n      item.command({\n        originalEvent: originalEvent,\n        item: item\n      });\n    }\n    if (this.popup) {\n      this.hide();\n    }\n    if (!this.popup && this.focusedOptionIndex() !== id) {\n      this.focusedOptionIndex.set(id);\n    }\n  }\n  onOverlayClick(event) {\n    if (this.popup) {\n      this.overlayService.add({\n        originalEvent: event,\n        target: this.el.nativeElement\n      });\n    }\n    this.preventDocumentDefault = true;\n  }\n  bindDocumentClickListener() {\n    if (!this.documentClickListener && isPlatformBrowser(this.platformId)) {\n      const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n      this.documentClickListener = this.renderer.listen(documentTarget, 'click', event => {\n        const isOutsideContainer = this.containerViewChild?.nativeElement && !this.containerViewChild?.nativeElement.contains(event.target);\n        const isOutsideTarget = !(this.target && (this.target === event.target || this.target.contains(event.target)));\n        if (!this.popup && isOutsideContainer && isOutsideTarget) {\n          this.onListBlur(event);\n        }\n        if (this.preventDocumentDefault && this.overlayVisible && isOutsideContainer && isOutsideTarget) {\n          this.hide();\n          this.preventDocumentDefault = false;\n        }\n      });\n    }\n  }\n  unbindDocumentClickListener() {\n    if (this.documentClickListener) {\n      this.documentClickListener();\n      this.documentClickListener = null;\n    }\n  }\n  bindDocumentResizeListener() {\n    if (!this.documentResizeListener && isPlatformBrowser(this.platformId)) {\n      const window = this.document.defaultView;\n      this.documentResizeListener = this.renderer.listen(window, 'resize', this.onWindowResize.bind(this));\n    }\n  }\n  unbindDocumentResizeListener() {\n    if (this.documentResizeListener) {\n      this.documentResizeListener();\n      this.documentResizeListener = null;\n    }\n  }\n  bindScrollListener() {\n    if (!this.scrollHandler && isPlatformBrowser(this.platformId)) {\n      this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, () => {\n        if (this.visible) {\n          this.hide();\n        }\n      });\n    }\n    this.scrollHandler?.bindScrollListener();\n  }\n  unbindScrollListener() {\n    if (this.scrollHandler) {\n      this.scrollHandler.unbindScrollListener();\n      this.scrollHandler = null;\n    }\n  }\n  onOverlayHide() {\n    this.unbindDocumentClickListener();\n    this.unbindDocumentResizeListener();\n    this.unbindScrollListener();\n    this.preventDocumentDefault = false;\n    if (!this.cd.destroyed) {\n      this.target = null;\n    }\n  }\n  ngOnDestroy() {\n    if (this.popup) {\n      if (this.scrollHandler) {\n        this.scrollHandler.destroy();\n        this.scrollHandler = null;\n      }\n      if (this.container && this.autoZIndex) {\n        ZIndexUtils.clear(this.container);\n      }\n      this.restoreOverlayAppend();\n      this.onOverlayHide();\n    }\n    if (!this.popup) {\n      this.unbindDocumentClickListener();\n    }\n    super.ngOnDestroy();\n  }\n  hasSubMenu() {\n    return this.model?.some(item => item.items) ?? false;\n  }\n  isItemHidden(item) {\n    if (item.separator) {\n      return item.visible === false || item.items && item.items.some(subitem => subitem.visible !== false);\n    }\n    return item.visible === false;\n  }\n  static ɵfac = function Menu_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Menu)(i0.ɵɵdirectiveInject(i5.OverlayService));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Menu,\n    selectors: [[\"p-menu\"]],\n    contentQueries: function Menu_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, _c4, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c5, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c6, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c7, 4);\n        i0.ɵɵcontentQuery(dirIndex, _c8, 4);\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.startTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.endTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.itemTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.submenuHeaderTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function Menu_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c9, 5);\n        i0.ɵɵviewQuery(_c10, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listViewChild = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);\n      }\n    },\n    inputs: {\n      model: \"model\",\n      popup: [2, \"popup\", \"popup\", booleanAttribute],\n      style: \"style\",\n      styleClass: \"styleClass\",\n      autoZIndex: [2, \"autoZIndex\", \"autoZIndex\", booleanAttribute],\n      baseZIndex: [2, \"baseZIndex\", \"baseZIndex\", numberAttribute],\n      showTransitionOptions: \"showTransitionOptions\",\n      hideTransitionOptions: \"hideTransitionOptions\",\n      ariaLabel: \"ariaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\",\n      id: \"id\",\n      tabindex: [2, \"tabindex\", \"tabindex\", numberAttribute],\n      appendTo: [1, \"appendTo\"]\n    },\n    outputs: {\n      onShow: \"onShow\",\n      onHide: \"onHide\",\n      onBlur: \"onBlur\",\n      onFocus: \"onFocus\"\n    },\n    features: [i0.ɵɵProvidersFeature([MenuStyle]), i0.ɵɵInheritDefinitionFeature],\n    decls: 1,\n    vars: 1,\n    consts: [[\"container\", \"\"], [\"list\", \"\"], [\"htmlSubmenuLabel\", \"\"], [3, \"class\", \"style\", \"ngStyle\", \"click\", 4, \"ngIf\"], [3, \"click\", \"ngStyle\"], [3, \"class\", 4, \"ngIf\"], [\"role\", \"menu\", 3, \"focus\", \"blur\", \"keydown\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"role\", \"separator\", 3, \"class\", 4, \"ngIf\"], [\"pTooltip\", \"\", \"role\", \"none\", 3, \"class\", \"tooltipOptions\", 4, \"ngIf\"], [\"role\", \"separator\"], [\"pTooltip\", \"\", \"role\", \"none\", 3, \"tooltipOptions\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"innerHTML\"], [\"pTooltip\", \"\", \"role\", \"menuitem\", 3, \"class\", \"pMenuItemContent\", \"itemTemplate\", \"style\", \"tooltipOptions\", \"onMenuItemClick\", 4, \"ngIf\"], [\"pTooltip\", \"\", \"role\", \"menuitem\", 3, \"onMenuItemClick\", \"pMenuItemContent\", \"itemTemplate\", \"tooltipOptions\"], [\"pTooltip\", \"\", \"role\", \"menuitem\", 3, \"class\", \"pMenuItemContent\", \"itemTemplate\", \"ngStyle\", \"tooltipOptions\", \"onMenuItemClick\", 4, \"ngIf\"], [\"pTooltip\", \"\", \"role\", \"menuitem\", 3, \"onMenuItemClick\", \"pMenuItemContent\", \"itemTemplate\", \"ngStyle\", \"tooltipOptions\"]],\n    template: function Menu_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, Menu_div_0_Template, 8, 26, \"div\", 3);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", !ctx.popup || ctx.visible);\n      }\n    },\n    dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, RouterModule, MenuItemContent, TooltipModule, i6.Tooltip, BadgeModule, SharedModule],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('overlayAnimation', [transition(':enter', [style$1({\n        opacity: 0,\n        transform: 'scaleY(0.8)'\n      }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style$1({\n        opacity: 0\n      }))])])]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Menu, [{\n    type: Component,\n    args: [{\n      selector: 'p-menu',\n      standalone: true,\n      imports: [CommonModule, RouterModule, MenuItemContent, TooltipModule, BadgeModule, SharedModule],\n      template: `\n        <div\n            #container\n            [class]=\"cn(cx('root'), styleClass)\"\n            [style]=\"sx('root')\"\n            [ngStyle]=\"style\"\n            *ngIf=\"!popup || visible\"\n            (click)=\"onOverlayClick($event)\"\n            [@overlayAnimation]=\"{\n                value: 'visible',\n                params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions }\n            }\"\n            [@.disabled]=\"popup !== true\"\n            (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\"\n            (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\"\n            [attr.data-pc-name]=\"'menu'\"\n            [attr.id]=\"id\"\n        >\n            <div *ngIf=\"startTemplate ?? _startTemplate\" [class]=\"cx('start')\" [attr.data-pc-section]=\"'start'\">\n                <ng-container *ngTemplateOutlet=\"startTemplate ?? _startTemplate\"></ng-container>\n            </div>\n            <ul\n                #list\n                [class]=\"cx('list')\"\n                role=\"menu\"\n                [attr.id]=\"id + '_list'\"\n                [attr.tabindex]=\"getTabIndexValue()\"\n                [attr.data-pc-section]=\"'menu'\"\n                [attr.aria-activedescendant]=\"activedescendant()\"\n                [attr.aria-label]=\"ariaLabel\"\n                [attr.aria-labelledBy]=\"ariaLabelledBy\"\n                (focus)=\"onListFocus($event)\"\n                (blur)=\"onListBlur($event)\"\n                (keydown)=\"onListKeyDown($event)\"\n            >\n                <ng-template ngFor let-submenu let-i=\"index\" [ngForOf]=\"model\" *ngIf=\"hasSubMenu()\">\n                    <li [class]=\"cx('separator')\" *ngIf=\"submenu.separator && submenu.visible !== false\" role=\"separator\"></li>\n                    <li [class]=\"cx('submenuLabel')\" [attr.data-automationid]=\"submenu.automationId\" *ngIf=\"!submenu.separator\" pTooltip [tooltipOptions]=\"submenu.tooltipOptions\" role=\"none\" [attr.id]=\"menuitemId(submenu, id, i)\">\n                        <ng-container *ngIf=\"!submenuHeaderTemplate && !_submenuHeaderTemplate\">\n                            <span *ngIf=\"submenu.escape !== false; else htmlSubmenuLabel\">{{ submenu.label }}</span>\n                            <ng-template #htmlSubmenuLabel><span [innerHTML]=\"sanitizeHtml(submenu.label)\"></span></ng-template>\n                        </ng-container>\n                        <ng-container *ngTemplateOutlet=\"submenuHeaderTemplate ?? _submenuHeaderTemplate; context: { $implicit: submenu }\"></ng-container>\n                    </li>\n                    <ng-template ngFor let-item let-j=\"index\" [ngForOf]=\"submenu.items\">\n                        <li [class]=\"cx('separator')\" *ngIf=\"item.separator && (item.visible !== false || submenu.visible !== false)\" role=\"separator\"></li>\n                        <li\n                            [class]=\"cn(cx('item', { item, id: menuitemId(item, id, i, j) }), item?.styleClass)\"\n                            *ngIf=\"!item.separator && item.visible !== false && (item.visible !== undefined || submenu.visible !== false)\"\n                            [pMenuItemContent]=\"item\"\n                            [itemTemplate]=\"itemTemplate ?? _itemTemplate\"\n                            [style]=\"item.style\"\n                            (onMenuItemClick)=\"itemClick($event, menuitemId(item, id, i, j))\"\n                            pTooltip\n                            [tooltipOptions]=\"item.tooltipOptions\"\n                            role=\"menuitem\"\n                            [attr.data-pc-section]=\"'menuitem'\"\n                            [attr.aria-label]=\"label(item.label)\"\n                            [attr.data-p-focused]=\"isItemFocused(menuitemId(item, id, i, j))\"\n                            [attr.data-p-disabled]=\"disabled(item.disabled)\"\n                            [attr.aria-disabled]=\"disabled(item.disabled)\"\n                            [attr.id]=\"menuitemId(item, id, i, j)\"\n                        ></li>\n                    </ng-template>\n                </ng-template>\n                <ng-template ngFor let-item let-i=\"index\" [ngForOf]=\"model\" *ngIf=\"!hasSubMenu()\">\n                    <li [class]=\"cx('separator')\" *ngIf=\"item.separator && item.visible !== false\" role=\"separator\"></li>\n                    <li\n                        [class]=\"cn(cx('item', { item, id: menuitemId(item, id, i) }), item?.styleClass)\"\n                        *ngIf=\"!item.separator && item.visible !== false\"\n                        [pMenuItemContent]=\"item\"\n                        [itemTemplate]=\"itemTemplate ?? _itemTemplate\"\n                        [ngStyle]=\"item.style\"\n                        (onMenuItemClick)=\"itemClick($event, menuitemId(item, id, i))\"\n                        pTooltip\n                        [tooltipOptions]=\"item.tooltipOptions\"\n                        role=\"menuitem\"\n                        [attr.data-pc-section]=\"'menuitem'\"\n                        [attr.aria-label]=\"label(item.label)\"\n                        [attr.data-p-focused]=\"isItemFocused(menuitemId(item, id, i))\"\n                        [attr.data-p-disabled]=\"disabled(item.disabled)\"\n                        [attr.aria-disabled]=\"disabled(item.disabled)\"\n                        [attr.id]=\"menuitemId(item, id, i)\"\n                    ></li>\n                </ng-template>\n            </ul>\n            <div *ngIf=\"endTemplate ?? _endTemplate\" [class]=\"cx('end')\" [attr.data-pc-section]=\"'end'\">\n                <ng-container *ngTemplateOutlet=\"endTemplate ?? _endTemplate\"></ng-container>\n            </div>\n        </div>\n    `,\n      animations: [trigger('overlayAnimation', [transition(':enter', [style$1({\n        opacity: 0,\n        transform: 'scaleY(0.8)'\n      }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style$1({\n        opacity: 0\n      }))])])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [MenuStyle]\n    }]\n  }], () => [{\n    type: i5.OverlayService\n  }], {\n    model: [{\n      type: Input\n    }],\n    popup: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    baseZIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    showTransitionOptions: [{\n      type: Input\n    }],\n    hideTransitionOptions: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    onShow: [{\n      type: Output\n    }],\n    onHide: [{\n      type: Output\n    }],\n    onBlur: [{\n      type: Output\n    }],\n    onFocus: [{\n      type: Output\n    }],\n    listViewChild: [{\n      type: ViewChild,\n      args: ['list']\n    }],\n    containerViewChild: [{\n      type: ViewChild,\n      args: ['container']\n    }],\n    startTemplate: [{\n      type: ContentChild,\n      args: ['start', {\n        descendants: false\n      }]\n    }],\n    endTemplate: [{\n      type: ContentChild,\n      args: ['end', {\n        descendants: false\n      }]\n    }],\n    headerTemplate: [{\n      type: ContentChild,\n      args: ['header', {\n        descendants: false\n      }]\n    }],\n    itemTemplate: [{\n      type: ContentChild,\n      args: ['item', {\n        descendants: false\n      }]\n    }],\n    submenuHeaderTemplate: [{\n      type: ContentChild,\n      args: ['submenuheader', {\n        descendants: false\n      }]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass MenuModule {\n  static ɵfac = function MenuModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MenuModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MenuModule,\n    imports: [Menu, SharedModule, SafeHtmlPipe],\n    exports: [Menu, SharedModule, SafeHtmlPipe]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [Menu, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Menu, SharedModule, SafeHtmlPipe],\n      exports: [Menu, SharedModule, SafeHtmlPipe]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Menu, MenuClasses, MenuItemContent, MenuModule, MenuStyle, SafeHtmlPipe, sanitizeHtml };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBA,IAAM,MAAM,CAAC,oBAAoB,EAAE;AACnC,IAAM,MAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,IAAM,MAAM,OAAO;AAAA,EACjB,OAAO;AACT;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,MAAM;AACR;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,CAAC;AAC3B,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,gBAAgB,CAAC;AACpG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,UAAM,iBAAoB,YAAY,CAAC;AACvC,IAAG,WAAW,OAAO,GAAG,UAAU,CAAC;AACnC,IAAG,WAAW,UAAU,OAAO,KAAK,MAAM;AAC1C,IAAG,YAAY,SAAS,OAAO,KAAK,KAAK,EAAE,QAAQ,OAAO,KAAK,OAAO,MAAS,aAAa,EAAE,qBAAqB,OAAO,KAAK,YAAY,EAAE,YAAY,EAAE,EAAE,mBAAmB,QAAQ;AACxL,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,cAAc,EAAE,2BAA8B,gBAAgB,IAAI,KAAK,OAAO,IAAI,CAAC;AAAA,EACvH;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,CAAC;AAC3B,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,gBAAgB,CAAC;AACpG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,UAAM,iBAAoB,YAAY,CAAC;AACvC,IAAG,WAAW,OAAO,GAAG,UAAU,CAAC;AACnC,IAAG,WAAW,cAAc,OAAO,KAAK,UAAU,EAAE,eAAe,OAAO,KAAK,WAAW,EAAE,2BAA2B,OAAO,KAAK,2BAA8B,gBAAgB,IAAI,GAAG,CAAC,EAAE,UAAU,OAAO,KAAK,MAAM,EAAE,YAAY,OAAO,KAAK,QAAQ,EAAE,uBAAuB,OAAO,KAAK,mBAAmB,EAAE,oBAAoB,OAAO,KAAK,gBAAgB,EAAE,sBAAsB,OAAO,KAAK,kBAAkB,EAAE,cAAc,OAAO,KAAK,UAAU,EAAE,SAAS,OAAO,KAAK,KAAK;AAC3d,IAAG,YAAY,qBAAqB,OAAO,KAAK,YAAY,EAAE,YAAY,EAAE,EAAE,mBAAmB,QAAQ,EAAE,SAAS,OAAO,KAAK,KAAK;AACrI,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,cAAc,EAAE,2BAA8B,gBAAgB,IAAI,KAAK,OAAO,IAAI,CAAC;AAAA,EACvH;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,6CAA6C,GAAG,IAAI,KAAK,CAAC,EAAE,GAAG,6CAA6C,GAAG,IAAI,KAAK,CAAC;AAC1I,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,EAAE,OAAO,QAAQ,OAAO,OAAO,OAAO,KAAK,WAAW;AAC5E,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,QAAQ,OAAO,OAAO,OAAO,KAAK,UAAU;AAAA,EAC3E;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AAAC;AAC3E,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,aAAa;AAAA,EAC/F;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,MAAM,CAAC;AACzE,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,YAAY,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,IAAI,CAAC;AAAA,EAC3H;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,MAAM;AAAA,EACxB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,KAAK,SAAS;AACnC,IAAG,WAAW,OAAO,GAAG,YAAe,gBAAgB,GAAG,KAAK,OAAO,IAAI,CAAC,CAAC;AAAA,EAC9E;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,GAAG,WAAW,CAAC;AACpC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,KAAK,KAAK;AAAA,EACxC;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,aAAa,OAAO,aAAa,OAAO,KAAK,KAAK,GAAM,cAAc;AAAA,EACtF;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,WAAW,EAAE;AAAA,EAC/B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,cAAc,OAAO,KAAK,eAAe,EAAE,SAAS,OAAO,KAAK,KAAK;AAAA,EACrF;AACF;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,+CAA+C,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,+CAA+C,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,sDAAsD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,kDAAkD,GAAG,GAAG,WAAW,EAAE;AAAA,EACjV;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAkB,YAAY,CAAC;AACrC,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,QAAQ,OAAO,KAAK,IAAI;AACtC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,KAAK,WAAW,KAAK,EAAE,YAAY,YAAY;AAC5E,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,KAAK,KAAK;AAAA,EACzC;AACF;AACA,IAAM,MAAM,CAAC,OAAO;AACpB,IAAM,MAAM,CAAC,KAAK;AAClB,IAAM,MAAM,CAAC,QAAQ;AACrB,IAAM,MAAM,CAAC,MAAM;AACnB,IAAM,MAAM,CAAC,eAAe;AAC5B,IAAM,MAAM,CAAC,MAAM;AACnB,IAAM,OAAO,CAAC,WAAW;AACzB,IAAM,OAAO,CAAC,IAAI,QAAQ;AAAA,EACxB,sBAAsB;AAAA,EACtB,sBAAsB;AACxB;AACA,IAAM,OAAO,SAAO;AAAA,EAClB,OAAO;AAAA,EACP,QAAQ;AACV;AACA,IAAM,OAAO,CAAC,IAAI,QAAQ;AAAA,EACxB,MAAM;AAAA,EACN,IAAI;AACN;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,0BAA0B,IAAI,KAAK;AAC1C,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK;AAC1B,IAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,gBAAgB,CAAC;AAClF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,GAAG,OAAO,CAAC;AAChC,IAAG,YAAY,mBAAmB,OAAO;AACzC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,iBAAiB,OAAO,cAAc;AAAA,EACjF;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,MAAM,EAAE;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,GAAG,WAAW,CAAC;AAAA,EACtC;AACF;AACA,SAAS,+DAA+D,IAAI,KAAK;AAC/E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAgB,cAAc,CAAC,EAAE;AACvC,IAAG,UAAU;AACb,IAAG,kBAAkB,WAAW,KAAK;AAAA,EACvC;AACF;AACA,SAAS,sEAAsE,IAAI,KAAK;AACtF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAgB,cAAc,CAAC,EAAE;AACvC,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,aAAa,OAAO,aAAa,WAAW,KAAK,GAAM,cAAc;AAAA,EACrF;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,gEAAgE,GAAG,GAAG,QAAQ,EAAE,EAAE,GAAG,uEAAuE,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AACpO,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,sBAAyB,YAAY,CAAC;AAC5C,UAAM,aAAgB,cAAc,CAAC,EAAE;AACvC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,WAAW,WAAW,KAAK,EAAE,YAAY,mBAAmB;AAAA,EACpF;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,EAAE;AAC7B,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,yDAAyD,GAAG,GAAG,gBAAgB,EAAE;AACvL,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,aAAa,OAAO;AAC1B,UAAM,OAAO,OAAO;AACpB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,GAAG,cAAc,CAAC;AACvC,IAAG,WAAW,kBAAkB,WAAW,cAAc;AACzD,IAAG,YAAY,qBAAqB,WAAW,YAAY,EAAE,MAAM,OAAO,WAAW,YAAY,OAAO,IAAI,IAAI,CAAC;AACjH,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,yBAAyB,CAAC,OAAO,sBAAsB;AACrF,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,yBAAyB,OAAO,sBAAsB,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,UAAU,CAAC;AAAA,EACpK;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,MAAM,EAAE;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,GAAG,WAAW,CAAC;AAAA,EACtC;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,EAAE;AAC7B,IAAG,WAAW,mBAAmB,SAAS,qFAAqF,QAAQ;AACrI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,YAAM,UAAU,OAAO;AACvB,YAAM,QAAQ,OAAO;AACrB,YAAM,OAAU,cAAc,EAAE;AAChC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,UAAU,QAAQ,OAAO,WAAW,SAAS,OAAO,IAAI,MAAM,KAAK,CAAC,CAAC;AAAA,IACpG,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,UAAU,OAAO;AACvB,UAAM,QAAQ,OAAO;AACrB,UAAM,OAAU,cAAc,EAAE;AAChC,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,QAAQ,KAAK;AAC3B,IAAG,WAAW,OAAO,GAAG,OAAO,GAAG,QAAW,gBAAgB,IAAI,MAAM,SAAS,OAAO,WAAW,SAAS,OAAO,IAAI,MAAM,KAAK,CAAC,CAAC,GAAG,WAAW,OAAO,OAAO,QAAQ,UAAU,CAAC;AAClL,IAAG,WAAW,oBAAoB,OAAO,EAAE,gBAAgB,OAAO,gBAAgB,OAAO,aAAa,EAAE,kBAAkB,QAAQ,cAAc;AAChJ,IAAG,YAAY,mBAAmB,UAAU,EAAE,cAAc,OAAO,MAAM,QAAQ,KAAK,CAAC,EAAE,kBAAkB,OAAO,cAAc,OAAO,WAAW,SAAS,OAAO,IAAI,MAAM,KAAK,CAAC,CAAC,EAAE,mBAAmB,OAAO,SAAS,QAAQ,QAAQ,CAAC,EAAE,iBAAiB,OAAO,SAAS,QAAQ,QAAQ,CAAC,EAAE,MAAM,OAAO,WAAW,SAAS,OAAO,IAAI,MAAM,KAAK,CAAC;AAAA,EACzV;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,MAAM,EAAE,EAAE,GAAG,wDAAwD,GAAG,IAAI,MAAM,EAAE;AAAA,EACrK;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,aAAgB,cAAc,EAAE;AACtC,IAAG,WAAW,QAAQ,QAAQ,cAAc,QAAQ,YAAY,SAAS,WAAW,YAAY,MAAM;AACtG,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,QAAQ,aAAa,QAAQ,YAAY,UAAU,QAAQ,YAAY,UAAa,WAAW,YAAY,MAAM;AAAA,EAC1I;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,MAAM,EAAE,EAAE,GAAG,0CAA0C,GAAG,IAAI,MAAM,EAAE,EAAE,GAAG,mDAAmD,GAAG,GAAG,eAAe,CAAC;AAAA,EACvN;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAa,IAAI;AACvB,IAAG,WAAW,QAAQ,WAAW,aAAa,WAAW,YAAY,KAAK;AAC1E,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,WAAW,SAAS;AAC3C,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,WAAW,KAAK;AAAA,EAC3C;AACF;AACA,SAAS,sBAAsB,IAAI,KAAK;AACtC,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,qCAAqC,GAAG,GAAG,eAAe,CAAC;AAAA,EAC9E;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,KAAK;AAAA,EACvC;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,MAAM,EAAE;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,GAAG,WAAW,CAAC;AAAA,EACtC;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,MAAM,EAAE;AAC7B,IAAG,WAAW,mBAAmB,SAAS,uEAAuE,QAAQ;AACvH,MAAG,cAAc,IAAI;AACrB,YAAM,UAAa,cAAc;AACjC,YAAM,WAAW,QAAQ;AACzB,YAAM,QAAQ,QAAQ;AACtB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,UAAU,QAAQ,OAAO,WAAW,UAAU,OAAO,IAAI,KAAK,CAAC,CAAC;AAAA,IAC/F,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc;AACjC,UAAM,WAAW,QAAQ;AACzB,UAAM,QAAQ,QAAQ;AACtB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,GAAG,OAAO,GAAG,QAAW,gBAAgB,IAAI,MAAM,UAAU,OAAO,WAAW,UAAU,OAAO,IAAI,KAAK,CAAC,CAAC,GAAG,YAAY,OAAO,OAAO,SAAS,UAAU,CAAC;AAChL,IAAG,WAAW,oBAAoB,QAAQ,EAAE,gBAAgB,OAAO,gBAAgB,OAAO,aAAa,EAAE,WAAW,SAAS,KAAK,EAAE,kBAAkB,SAAS,cAAc;AAC7K,IAAG,YAAY,mBAAmB,UAAU,EAAE,cAAc,OAAO,MAAM,SAAS,KAAK,CAAC,EAAE,kBAAkB,OAAO,cAAc,OAAO,WAAW,UAAU,OAAO,IAAI,KAAK,CAAC,CAAC,EAAE,mBAAmB,OAAO,SAAS,SAAS,QAAQ,CAAC,EAAE,iBAAiB,OAAO,SAAS,SAAS,QAAQ,CAAC,EAAE,MAAM,OAAO,WAAW,UAAU,OAAO,IAAI,KAAK,CAAC;AAAA,EAClV;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,MAAM,EAAE,EAAE,GAAG,0CAA0C,GAAG,IAAI,MAAM,EAAE;AAAA,EACzI;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,IAAG,WAAW,QAAQ,SAAS,aAAa,SAAS,YAAY,KAAK;AACtE,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,SAAS,aAAa,SAAS,YAAY,KAAK;AAAA,EACzE;AACF;AACA,SAAS,sBAAsB,IAAI,KAAK;AACtC,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,qCAAqC,GAAG,GAAG,eAAe,CAAC;AAAA,EAC9E;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,KAAK;AAAA,EACvC;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,0BAA0B,IAAI,KAAK;AAC1C,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK;AAC1B,IAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,gBAAgB,CAAC;AAClF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,GAAG,KAAK,CAAC;AAC9B,IAAG,YAAY,mBAAmB,KAAK;AACvC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,eAAe,OAAO,YAAY;AAAA,EAC7E;AACF;AACA,SAAS,oBAAoB,IAAI,KAAK;AACpC,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,yCAAyC,QAAQ;AAC/E,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,eAAe,MAAM,CAAC;AAAA,IACrD,CAAC,EAAE,2BAA2B,SAAS,oEAAoE,QAAQ;AACjH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,wBAAwB,MAAM,CAAC;AAAA,IAC9D,CAAC,EAAE,0BAA0B,SAAS,mEAAmE,QAAQ;AAC/G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,sBAAsB,MAAM,CAAC;AAAA,IAC5D,CAAC;AACD,IAAG,WAAW,GAAG,2BAA2B,GAAG,GAAG,OAAO,CAAC;AAC1D,IAAG,eAAe,GAAG,MAAM,GAAG,CAAC;AAC/B,IAAG,WAAW,SAAS,SAAS,wCAAwC,QAAQ;AAC9E,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,YAAY,MAAM,CAAC;AAAA,IAClD,CAAC,EAAE,QAAQ,SAAS,uCAAuC,QAAQ;AACjE,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,WAAW,MAAM,CAAC;AAAA,IACjD,CAAC,EAAE,WAAW,SAAS,0CAA0C,QAAQ;AACvE,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,MAAM,CAAC;AAAA,IACpD,CAAC;AACD,IAAG,WAAW,GAAG,uBAAuB,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,uBAAuB,GAAG,GAAG,MAAM,CAAC;AAC9F,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,2BAA2B,GAAG,GAAG,OAAO,CAAC;AAC1D,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,GAAG,MAAM,CAAC;AAC/B,IAAG,WAAW,OAAO,GAAG,OAAO,GAAG,MAAM,GAAG,OAAO,UAAU,CAAC;AAC7D,IAAG,WAAW,WAAW,OAAO,KAAK,EAAE,qBAAwB,gBAAgB,IAAI,MAAS,gBAAgB,IAAI,MAAM,OAAO,uBAAuB,OAAO,qBAAqB,CAAC,CAAC,EAAE,cAAc,OAAO,UAAU,IAAI;AACvN,IAAG,YAAY,gBAAgB,MAAM,EAAE,MAAM,OAAO,EAAE;AACtD,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,iBAAiB,OAAO,cAAc;AACnE,IAAG,UAAU;AACb,IAAG,WAAW,OAAO,GAAG,MAAM,CAAC;AAC/B,IAAG,YAAY,MAAM,OAAO,KAAK,OAAO,EAAE,YAAY,OAAO,iBAAiB,CAAC,EAAE,mBAAmB,MAAM,EAAE,yBAAyB,OAAO,iBAAiB,CAAC,EAAE,cAAc,OAAO,SAAS,EAAE,mBAAmB,OAAO,cAAc;AACxO,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,WAAW,CAAC;AACzC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,WAAW,CAAC;AAC1C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,eAAe,OAAO,YAAY;AAAA,EACjE;AACF;AACA,IAAM,eAAe;AAAA,EACnB,MAAM,CAAC;AAAA,IACL;AAAA,EACF,OAAO;AAAA,IACL,UAAU,SAAS,QAAQ,aAAa;AAAA,EAC1C;AACF;AACA,IAAM,UAAU;AAAA,EACd,MAAM,CAAC;AAAA,IACL;AAAA,EACF,MAAM,CAAC,sBAAsB;AAAA,IAC3B,kBAAkB,SAAS;AAAA,EAC7B,CAAC;AAAA,EACD,OAAO;AAAA,EACP,MAAM;AAAA,EACN,cAAc;AAAA,EACd,WAAW;AAAA,EACX,KAAK;AAAA,EACL,MAAM,CAAC;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF,MAAM,CAAC,eAAe;AAAA,IACpB,WAAW,SAAS,gBAAgB,KAAK,OAAO,SAAS,gBAAgB;AAAA,IACzE,cAAc,SAAS,SAAS,KAAK,QAAQ;AAAA,EAC/C,GAAG,KAAK,UAAU;AAAA,EAClB,aAAa;AAAA,EACb,UAAU;AAAA,EACV,UAAU,CAAC;AAAA,IACT;AAAA,EACF,MAAM,CAAC,oBAAoB,KAAK,MAAM,KAAK,SAAS;AAAA,EACpD,WAAW;AACb;AACA,IAAM,YAAN,MAAM,mBAAkB,UAAU;AAAA,EAChC,OAAO;AAAA,EACP,QAAQA;AAAA,EACR,UAAU;AAAA,EACV,eAAe;AAAA,EACf,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,kBAAkB,mBAAmB;AACnD,cAAQ,2BAA2B,yBAA4B,sBAAsB,UAAS,IAAI,qBAAqB,UAAS;AAAA,IAClI;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,WAAU;AAAA,EACrB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUC,cAAa;AAItB,EAAAA,aAAY,MAAM,IAAI;AAItB,EAAAA,aAAY,OAAO,IAAI;AAIvB,EAAAA,aAAY,MAAM,IAAI;AAItB,EAAAA,aAAY,aAAa,IAAI;AAI7B,EAAAA,aAAY,WAAW,IAAI;AAI3B,EAAAA,aAAY,KAAK,IAAI;AAIrB,EAAAA,aAAY,MAAM,IAAI;AAItB,EAAAA,aAAY,aAAa,IAAI;AAI7B,EAAAA,aAAY,UAAU,IAAI;AAI1B,EAAAA,aAAY,UAAU,IAAI;AAI1B,EAAAA,aAAY,WAAW,IAAI;AAC7B,GAAG,gBAAgB,cAAc,CAAC,EAAE;AACpC,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB;AAAA,EACA;AAAA,EACA,YAAY,YAAY,WAAW;AACjC,SAAK,aAAa;AAClB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,UAAU,OAAO;AACf,QAAI,CAAC,SAAS,CAAC,kBAAkB,KAAK,UAAU,GAAG;AACjD,aAAO;AAAA,IACT;AACA,WAAO,KAAK,UAAU,wBAAwB,KAAK;AAAA,EACrD;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAiB,kBAAkB,aAAa,EAAE,GAAM,kBAAqB,cAAc,EAAE,CAAC;AAAA,EACjI;AAAA,EACA,OAAO,QAA0B,aAAa;AAAA,IAC5C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,EACR,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,aAAa,OAAO;AAC3B,QAAM,aAAa,OAAO,WAAW;AACrC,QAAM,YAAY,OAAO,YAAY;AACrC,MAAI,CAAC,SAAS,CAAC,kBAAkB,UAAU,GAAG;AAC5C,WAAO;AAAA,EACT;AACA,SAAO,UAAU,wBAAwB,KAAK;AAChD;AACA,IAAM,kBAAN,MAAM,yBAAwB,cAAc;AAAA,EAC1C;AAAA,EACA;AAAA,EACA,kBAAkB,IAAI,aAAa;AAAA,EACnC;AAAA,EACA,kBAAkB,OAAO,SAAS;AAAA,EAClC,YAAY,MAAM;AAChB,UAAM;AACN,SAAK,OAAO;AAAA,EACd;AAAA,EACA,YAAY,OAAO,MAAM;AACvB,SAAK,gBAAgB,KAAK;AAAA,MACxB,eAAe;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAoB,kBAAkB,WAAW,MAAM,IAAI,CAAC,CAAC;AAAA,EAChG;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,IACxC,QAAQ;AAAA,MACN,MAAM,CAAC,GAAG,oBAAoB,MAAM;AAAA,MACpC,cAAc;AAAA,IAChB;AAAA,IACA,SAAS;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,SAAS,CAAC,GAAM,0BAA0B;AAAA,IAC5E,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,eAAe,EAAE,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,WAAW,IAAI,GAAG,SAAS,UAAU,GAAG,MAAM,GAAG,CAAC,oBAAoB,2BAA2B,WAAW,IAAI,GAAG,cAAc,eAAe,2BAA2B,SAAS,UAAU,YAAY,uBAAuB,oBAAoB,sBAAsB,cAAc,SAAS,GAAG,MAAM,GAAG,CAAC,WAAW,IAAI,GAAG,QAAQ,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,oBAAoB,2BAA2B,WAAW,IAAI,GAAG,cAAc,eAAe,2BAA2B,UAAU,YAAY,uBAAuB,oBAAoB,sBAAsB,cAAc,OAAO,GAAG,CAAC,GAAG,SAAS,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,cAAc,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,qBAAqB,GAAG,WAAW,GAAG,CAAC,GAAG,cAAc,OAAO,CAAC;AAAA,IACx2B,UAAU,SAAS,yBAAyB,IAAI,KAAK;AACnD,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,WAAW,SAAS,SAAS,8CAA8C,QAAQ;AACpF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,YAAY,QAAQ,IAAI,IAAI,CAAC;AAAA,QACzD,CAAC;AACD,QAAG,WAAW,GAAG,yCAAyC,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,yCAAyC,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,wCAAwC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAC1P,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,GAAG,aAAa,CAAC;AACnC,QAAG,YAAY,mBAAmB,SAAS;AAC3C,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,CAAC,IAAI,YAAY;AACvC,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,YAAY;AAAA,MACxC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAiB,MAAS,kBAAkB,cAAiB,YAAe,kBAAkB,QAAQ,eAAe,aAAgB,OAAO,YAAY;AAAA,IACvK,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,cAAc,QAAQ,eAAe,aAAa,YAAY;AAAA,MACtF,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkDV,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,SAAS;AAAA,IACvB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW,MAAM,IAAI,CAAC;AAAA,IAC/B,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,OAAN,MAAM,cAAa,cAAc;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMX,WAAW,MAAM,QAAW,GAAI,YAAY,CAAC;AAAA,IAC3C,WAAW;AAAA,EACb,CAAC,IAAI,CAAC,CAAE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKR,SAAS,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1B,SAAS,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM1B,SAAS,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM1B,UAAU,IAAI,aAAa;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,YAAY,SAAS,MAAM,KAAK,SAAS,KAAK,KAAK,OAAO,gBAAgB,GAAG,GAAI,YAAY,CAAC;AAAA,IAC5F,WAAW;AAAA,EACb,CAAC,IAAI,CAAC,CAAE;AAAA,EACR;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAkB,SAAS,MAAM;AAC/B,WAAO,KAAK,mBAAmB,MAAM,KAAK,KAAK,mBAAmB,IAAI;AAAA,EACxE,GAAG,GAAI,YAAY,CAAC;AAAA,IAClB,WAAW;AAAA,EACb,CAAC,IAAI,CAAC,CAAE;AAAA,EACR,qBAAqB,OAAO,IAAI,GAAI,YAAY,CAAC;AAAA,IAC/C,WAAW;AAAA,EACb,CAAC,IAAI,CAAC,CAAE;AAAA,EACR,sBAAsB,OAAO,IAAI,GAAI,YAAY,CAAC;AAAA,IAChD,WAAW;AAAA,EACb,CAAC,IAAI,CAAC,CAAE;AAAA,EACR,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB;AAAA,EACA,kBAAkB,OAAO,SAAS;AAAA,EAClC,YAAY,gBAAgB;AAC1B,UAAM;AACN,SAAK,iBAAiB;AACtB,SAAK,KAAK,KAAK,MAAM,EAAK,QAAQ;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,OAAO;AACZ,QAAI,KAAK,QAAS,MAAK,KAAK;AAAA,QAAO,MAAK,KAAK,KAAK;AAClD,SAAK,yBAAyB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,OAAO;AACV,SAAK,SAAS,MAAM;AACpB,SAAK,gBAAgB,MAAM;AAC3B,SAAK,UAAU;AACf,SAAK,yBAAyB;AAC9B,SAAK,iBAAiB;AACtB,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA,EACA,WAAW;AACT,UAAM,SAAS;AACf,QAAI,CAAC,KAAK,OAAO;AACf,WAAK,0BAA0B;AAAA,IACjC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA;AAAA,EACA;AAAA,EACA,qBAAqB;AACnB,SAAK,WAAW,QAAQ,UAAQ;AAC9B,cAAQ,KAAK,QAAQ,GAAG;AAAA,QACtB,KAAK;AACH,eAAK,iBAAiB,KAAK;AAC3B;AAAA,QACF,KAAK;AACH,eAAK,eAAe,KAAK;AACzB;AAAA,QACF,KAAK;AACH,eAAK,gBAAgB,KAAK;AAC1B;AAAA,QACF,KAAK;AACH,eAAK,yBAAyB,KAAK;AACnC;AAAA,QACF;AACE,eAAK,gBAAgB,KAAK;AAC1B;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,aAAa,SAAY,KAAK,SAAS,SAAS,IAAI;AAAA,EAClE;AAAA,EACA,wBAAwB,OAAO;AAC7B,YAAQ,MAAM,SAAS;AAAA,MACrB,KAAK;AACH,YAAI,KAAK,OAAO;AACd,eAAK,YAAY,MAAM;AACvB,eAAK,UAAU;AACf,eAAK,OAAO,KAAK,CAAC,CAAC;AACnB,eAAK,gBAAgB,KAAK,UAAU,aAAa,KAAK,cAAc,EAAE;AACtE,eAAK,cAAc;AACnB,eAAK,aAAa;AAClB,eAAK,0BAA0B;AAC/B,eAAK,2BAA2B;AAChC,eAAK,mBAAmB;AACxB,aAAM,KAAK,cAAc,aAAa;AAAA,QACxC;AACA;AAAA,MACF,KAAK;AACH,aAAK,cAAc;AACnB,aAAK,OAAO,KAAK,CAAC,CAAC;AACnB;AAAA,IACJ;AAAA,EACF;AAAA,EACA,sBAAsB,OAAO;AAC3B,YAAQ,MAAM,SAAS;AAAA,MACrB,KAAK;AACH,YAAI,KAAK,YAAY;AACnB,sBAAY,MAAM,MAAM,OAAO;AAAA,QACjC;AACA;AAAA,IACJ;AAAA,EACF;AAAA,EACA,eAAe;AACb,QAAI,KAAK,cAAe,GAAiB,KAAK,WAAW,KAAK,MAAM;AAAA,QAAO,GAAiB,KAAK,WAAW,KAAK,MAAM;AAAA,EACzH;AAAA,EACA,gBAAgB;AACd,eAAW,cAAc,KAAK,WAAW,KAAK,UAAU,MAAM,SAAS,KAAK,SAAS,OAAO,KAAK,UAAU,GAAG,KAAK,UAAU,CAAC;AAAA,EAChI;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,aAAa,KAAK,UAAU,MAAM,QAAQ;AACjD,WAAK,SAAS,YAAY,KAAK,GAAG,eAAe,KAAK,SAAS;AAAA,IACjE;AAAA,EACF;AAAA,EACA,YAAY;AACV,QAAI,KAAK,YAAY;AACnB,kBAAY,IAAI,QAAQ,KAAK,WAAW,KAAK,aAAa,KAAK,OAAO,OAAO,IAAI;AAAA,IACnF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AACL,SAAK,UAAU;AACf,SAAK,gBAAgB;AACrB,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA,EACA,iBAAiB;AACf,QAAI,KAAK,WAAW,CAAC,GAAc,GAAG;AACpC,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EACA,WAAW,MAAM,IAAI,OAAO,YAAY;AACtC,WAAO,MAAM,MAAM,GAAG,EAAE,IAAI,KAAK,GAAG,eAAe,SAAY,MAAM,aAAa,EAAE;AAAA,EACtF;AAAA,EACA,cAAc,IAAI;AAChB,WAAO,KAAK,gBAAgB,MAAM;AAAA,EACpC;AAAA,EACA,MAAM,OAAO;AACX,WAAO,OAAO,UAAU,aAAa,MAAM,IAAI;AAAA,EACjD;AAAA,EACA,SAAS,UAAU;AACjB,WAAO,OAAO,aAAa,aAAa,SAAS,IAAI,OAAO,aAAa,cAAc,QAAQ;AAAA,EACjG;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,UAAU,KAAK,gBAAgB,IAAI;AAAA,EACjD;AAAA,EACA,YAAY,OAAO;AACjB,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU;AACf,OAAC,KAAK,SAAS,KAAK,yBAAyB,CAAC;AAC9C,WAAK,QAAQ,KAAK,KAAK;AAAA,IACzB;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,QAAI,KAAK,SAAS;AAChB,WAAK,UAAU;AACf,WAAK,yBAAyB,EAAE;AAChC,WAAK,oBAAoB,IAAI,EAAE;AAC/B,WAAK,mBAAmB,IAAI,EAAE;AAC9B,WAAK,OAAO,KAAK,KAAK;AAAA,IACxB;AAAA,EACF;AAAA,EACA,cAAc,OAAO;AACnB,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK;AACH,aAAK,eAAe,KAAK;AACzB;AAAA,MACF,KAAK;AACH,aAAK,aAAa,KAAK;AACvB;AAAA,MACF,KAAK;AACH,aAAK,UAAU,KAAK;AACpB;AAAA,MACF,KAAK;AACH,aAAK,SAAS,KAAK;AACnB;AAAA,MACF,KAAK;AACH,aAAK,WAAW,KAAK;AACrB;AAAA,MACF,KAAK;AACH,aAAK,WAAW,KAAK;AACrB;AAAA,MACF,KAAK;AACH,aAAK,WAAW,KAAK;AACrB;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,YAAI,KAAK,OAAO;AACd,aAAM,KAAK,MAAM;AACjB,eAAK,KAAK;AAAA,QACZ;AACA,aAAK,kBAAkB,KAAK,KAAK;AACjC;AAAA,MACF;AACE;AAAA,IACJ;AAAA,EACF;AAAA,EACA,eAAe,OAAO;AACpB,UAAM,cAAc,KAAK,oBAAoB,KAAK,mBAAmB,CAAC;AACtE,SAAK,yBAAyB,WAAW;AACzC,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,aAAa,OAAO;AAClB,QAAI,MAAM,UAAU,KAAK,OAAO;AAC9B,SAAM,KAAK,MAAM;AACjB,WAAK,KAAK;AACV,YAAM,eAAe;AAAA,IACvB,OAAO;AACL,YAAM,cAAc,KAAK,oBAAoB,KAAK,mBAAmB,CAAC;AACtE,WAAK,yBAAyB,WAAW;AACzC,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA,EACA,UAAU,OAAO;AACf,SAAK,yBAAyB,CAAC;AAC/B,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,SAAS,OAAO;AACd,SAAK,yBAAyB,EAAK,KAAK,mBAAmB,eAAe,yDAAyD,EAAE,SAAS,CAAC;AAC/I,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,WAAW,OAAO;AAChB,UAAM,UAAU,EAAW,KAAK,mBAAmB,eAAe,UAAU,GAAG,KAAK,mBAAmB,CAAC,EAAE,IAAI;AAC9G,UAAM,gBAAgB,WAAW,EAAW,SAAS,6BAA6B;AAClF,SAAK,SAAS,GAAM,KAAK,MAAM;AAC/B,oBAAgB,cAAc,MAAM,IAAI,WAAW,QAAQ,MAAM;AACjE,UAAM,eAAe;AAAA,EACvB;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,WAAW,KAAK;AAAA,EACvB;AAAA,EACA,oBAAoB,OAAO;AACzB,UAAM,QAAQ,EAAK,KAAK,mBAAmB,eAAe,yDAAyD;AACnH,UAAM,qBAAqB,CAAC,GAAG,KAAK,EAAE,UAAU,UAAQ,KAAK,OAAO,KAAK;AACzE,WAAO,qBAAqB,KAAK,qBAAqB,IAAI;AAAA,EAC5D;AAAA,EACA,oBAAoB,OAAO;AACzB,UAAM,QAAQ,EAAK,KAAK,mBAAmB,eAAe,yDAAyD;AACnH,UAAM,qBAAqB,CAAC,GAAG,KAAK,EAAE,UAAU,UAAQ,KAAK,OAAO,KAAK;AACzE,WAAO,qBAAqB,KAAK,qBAAqB,IAAI;AAAA,EAC5D;AAAA,EACA,yBAAyB,OAAO;AAC9B,UAAM,QAAQ,EAAK,KAAK,mBAAmB,eAAe,yDAAyD;AACnH,QAAI,MAAM,SAAS,GAAG;AACpB,UAAI,QAAQ,SAAS,MAAM,SAAS,MAAM,SAAS,IAAI,QAAQ,IAAI,IAAI;AACvE,cAAQ,MAAM,KAAK,mBAAmB,IAAI,MAAM,KAAK,EAAE,aAAa,IAAI,CAAC;AAAA,IAC3E;AAAA,EACF;AAAA,EACA,UAAU,OAAO,IAAI;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU;AACf,WAAK,QAAQ,KAAK;AAAA,IACpB;AACA,QAAI,KAAK,UAAU;AACjB,oBAAc,eAAe;AAC7B;AAAA,IACF;AACA,QAAI,CAAC,KAAK,OAAO,CAAC,KAAK,YAAY;AACjC,oBAAc,eAAe;AAAA,IAC/B;AACA,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ;AAAA,QACX;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,KAAK,OAAO;AACd,WAAK,KAAK;AAAA,IACZ;AACA,QAAI,CAAC,KAAK,SAAS,KAAK,mBAAmB,MAAM,IAAI;AACnD,WAAK,mBAAmB,IAAI,EAAE;AAAA,IAChC;AAAA,EACF;AAAA,EACA,eAAe,OAAO;AACpB,QAAI,KAAK,OAAO;AACd,WAAK,eAAe,IAAI;AAAA,QACtB,eAAe;AAAA,QACf,QAAQ,KAAK,GAAG;AAAA,MAClB,CAAC;AAAA,IACH;AACA,SAAK,yBAAyB;AAAA,EAChC;AAAA,EACA,4BAA4B;AAC1B,QAAI,CAAC,KAAK,yBAAyB,kBAAkB,KAAK,UAAU,GAAG;AACrE,YAAM,iBAAiB,KAAK,KAAK,KAAK,GAAG,cAAc,gBAAgB;AACvE,WAAK,wBAAwB,KAAK,SAAS,OAAO,gBAAgB,SAAS,WAAS;AAClF,cAAM,qBAAqB,KAAK,oBAAoB,iBAAiB,CAAC,KAAK,oBAAoB,cAAc,SAAS,MAAM,MAAM;AAClI,cAAM,kBAAkB,EAAE,KAAK,WAAW,KAAK,WAAW,MAAM,UAAU,KAAK,OAAO,SAAS,MAAM,MAAM;AAC3G,YAAI,CAAC,KAAK,SAAS,sBAAsB,iBAAiB;AACxD,eAAK,WAAW,KAAK;AAAA,QACvB;AACA,YAAI,KAAK,0BAA0B,KAAK,kBAAkB,sBAAsB,iBAAiB;AAC/F,eAAK,KAAK;AACV,eAAK,yBAAyB;AAAA,QAChC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,8BAA8B;AAC5B,QAAI,KAAK,uBAAuB;AAC9B,WAAK,sBAAsB;AAC3B,WAAK,wBAAwB;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,6BAA6B;AAC3B,QAAI,CAAC,KAAK,0BAA0B,kBAAkB,KAAK,UAAU,GAAG;AACtE,YAAM,SAAS,KAAK,SAAS;AAC7B,WAAK,yBAAyB,KAAK,SAAS,OAAO,QAAQ,UAAU,KAAK,eAAe,KAAK,IAAI,CAAC;AAAA,IACrG;AAAA,EACF;AAAA,EACA,+BAA+B;AAC7B,QAAI,KAAK,wBAAwB;AAC/B,WAAK,uBAAuB;AAC5B,WAAK,yBAAyB;AAAA,IAChC;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,iBAAiB,kBAAkB,KAAK,UAAU,GAAG;AAC7D,WAAK,gBAAgB,IAAI,8BAA8B,KAAK,QAAQ,MAAM;AACxE,YAAI,KAAK,SAAS;AAChB,eAAK,KAAK;AAAA,QACZ;AAAA,MACF,CAAC;AAAA,IACH;AACA,SAAK,eAAe,mBAAmB;AAAA,EACzC;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,qBAAqB;AACxC,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,SAAK,4BAA4B;AACjC,SAAK,6BAA6B;AAClC,SAAK,qBAAqB;AAC1B,SAAK,yBAAyB;AAC9B,QAAI,CAAC,KAAK,GAAG,WAAW;AACtB,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,OAAO;AACd,UAAI,KAAK,eAAe;AACtB,aAAK,cAAc,QAAQ;AAC3B,aAAK,gBAAgB;AAAA,MACvB;AACA,UAAI,KAAK,aAAa,KAAK,YAAY;AACrC,oBAAY,MAAM,KAAK,SAAS;AAAA,MAClC;AACA,WAAK,qBAAqB;AAC1B,WAAK,cAAc;AAAA,IACrB;AACA,QAAI,CAAC,KAAK,OAAO;AACf,WAAK,4BAA4B;AAAA,IACnC;AACA,UAAM,YAAY;AAAA,EACpB;AAAA,EACA,aAAa;AACX,WAAO,KAAK,OAAO,KAAK,UAAQ,KAAK,KAAK,KAAK;AAAA,EACjD;AAAA,EACA,aAAa,MAAM;AACjB,QAAI,KAAK,WAAW;AAClB,aAAO,KAAK,YAAY,SAAS,KAAK,SAAS,KAAK,MAAM,KAAK,aAAW,QAAQ,YAAY,KAAK;AAAA,IACrG;AACA,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA,EACA,OAAO,OAAO,SAAS,aAAa,mBAAmB;AACrD,WAAO,KAAK,qBAAqB,OAAS,kBAAqB,cAAc,CAAC;AAAA,EAChF;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,QAAQ,CAAC;AAAA,IACtB,gBAAgB,SAAS,oBAAoB,IAAI,KAAK,UAAU;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AACpE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAClE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AACrE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AACnE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,wBAAwB,GAAG;AAC5E,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW,SAAS,WAAW,IAAI,KAAK;AACtC,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,MAAM,CAAC;AAAA,MACxB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AACpE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB,GAAG;AAAA,MAC3E;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,OAAO,CAAC,GAAG,SAAS,SAAS,gBAAgB;AAAA,MAC7C,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,YAAY,CAAC,GAAG,cAAc,cAAc,eAAe;AAAA,MAC3D,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,IAAI;AAAA,MACJ,UAAU,CAAC,GAAG,YAAY,YAAY,eAAe;AAAA,MACrD,UAAU,CAAC,GAAG,UAAU;AAAA,IAC1B;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,IACX;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,SAAS,CAAC,GAAM,0BAA0B;AAAA,IAC5E,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,aAAa,EAAE,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,GAAG,SAAS,SAAS,WAAW,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,SAAS,SAAS,GAAG,CAAC,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,QAAQ,QAAQ,GAAG,SAAS,QAAQ,SAAS,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,SAAS,IAAI,GAAG,SAAS,GAAG,CAAC,QAAQ,aAAa,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,YAAY,IAAI,QAAQ,QAAQ,GAAG,SAAS,kBAAkB,GAAG,MAAM,GAAG,CAAC,QAAQ,WAAW,GAAG,CAAC,YAAY,IAAI,QAAQ,QAAQ,GAAG,gBAAgB,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,YAAY,IAAI,QAAQ,YAAY,GAAG,SAAS,oBAAoB,gBAAgB,SAAS,kBAAkB,mBAAmB,GAAG,MAAM,GAAG,CAAC,YAAY,IAAI,QAAQ,YAAY,GAAG,mBAAmB,oBAAoB,gBAAgB,gBAAgB,GAAG,CAAC,YAAY,IAAI,QAAQ,YAAY,GAAG,SAAS,oBAAoB,gBAAgB,WAAW,kBAAkB,mBAAmB,GAAG,MAAM,GAAG,CAAC,YAAY,IAAI,QAAQ,YAAY,GAAG,mBAAmB,oBAAoB,gBAAgB,WAAW,gBAAgB,CAAC;AAAA,IACnlC,UAAU,SAAS,cAAc,IAAI,KAAK;AACxC,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,GAAG,qBAAqB,GAAG,IAAI,OAAO,CAAC;AAAA,MACvD;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,QAAQ,CAAC,IAAI,SAAS,IAAI,OAAO;AAAA,MACjD;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAiB,SAAY,MAAS,kBAAqB,SAAS,cAAc,iBAAiB,eAAkB,SAAS,aAAa,YAAY;AAAA,IACtK,eAAe;AAAA,IACf,MAAM;AAAA,MACJ,WAAW,CAAC,QAAQ,oBAAoB,CAAC,WAAW,UAAU,CAAC,MAAQ;AAAA,QACrE,SAAS;AAAA,QACT,WAAW;AAAA,MACb,CAAC,GAAG,QAAQ,0BAA0B,CAAC,CAAC,GAAG,WAAW,UAAU,CAAC,QAAQ,4BAA4B,MAAQ;AAAA,QAC3G,SAAS;AAAA,MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,IACT;AAAA,IACA,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,MAAM,CAAC;AAAA,IAC7E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,cAAc,iBAAiB,eAAe,aAAa,YAAY;AAAA,MAC/F,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA2FV,YAAY,CAAC,QAAQ,oBAAoB,CAAC,WAAW,UAAU,CAAC,MAAQ;AAAA,QACtE,SAAS;AAAA,QACT,WAAW;AAAA,MACb,CAAC,GAAG,QAAQ,0BAA0B,CAAC,CAAC,GAAG,WAAW,UAAU,CAAC,QAAQ,4BAA4B,MAAQ;AAAA,QAC3G,SAAS;AAAA,MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,MACP,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,SAAS;AAAA,IACvB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,QACd,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,QACZ,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,QACf,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,QACb,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,QACtB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,OAAO,OAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAY;AAAA,EAC/C;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,MAAM,cAAc,YAAY;AAAA,IAC1C,SAAS,CAAC,MAAM,cAAc,YAAY;AAAA,EAC5C,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,MAAM,cAAc,YAAY;AAAA,EAC5C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,MAAM,cAAc,YAAY;AAAA,MAC1C,SAAS,CAAC,MAAM,cAAc,YAAY;AAAA,IAC5C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["style", "MenuClasses"]}