<div class="page-layout" [class.sidebar-collapsed]="!isDesktopMenuOpen && !isMobile">
  <app-sidebar 
    [isCollapsed]="!isDesktopMenuOpen && !isMobile"
    [isVisible]="isMobileMenuVisible"
    (onToggle)="handleSidebarToggle()"
    (onNavigate)="closeMobileSidebar()">
  </app-sidebar>

  <main class="main-content">
    <div class="content-wrapper">
      <router-outlet></router-outlet>
    </div>
  </main>

  <!-- Backdrop for mobile overlay menu -->
  <div 
    class="sidebar-backdrop" 
    *ngIf="isMobileMenuVisible" 
    (click)="closeMobileSidebar()">
  </div>
</div>