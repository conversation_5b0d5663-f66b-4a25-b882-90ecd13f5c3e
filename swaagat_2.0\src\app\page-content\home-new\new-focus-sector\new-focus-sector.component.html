<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="font-merriweather font-bold text-2xl sm:text-3xl text-gray-900 mb-4">
                Focus Sectors - Sector Profiles
            </h2>
            <p class="font-open-sans text-lg text-gray-600 max-w-3xl mx-auto">
                Explore detailed profiles of key industries that drive Tripura's economic growth, complete with opportunities, policies, and incentives.
            </p>
        </div>
        <div class="grid grid-cols-3 lg:grid-cols-3 gap-6">
            <div class="lg:col-span-1">
                <h3 class="font-merriweather font-bold text-lg text-gray-900 mb-4">
                    Select Sector
                </h3>
                <div class="space-y-1">
                    <button *ngFor="let sector of sectors" (click)="selectSector(sector)" 
                            class="w-full text-left p-3 rounded-lg transition-all duration-300"
                            [ngClass]="sector.isActive ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-md transform scale-102' : 'bg-gray-50 hover:bg-gray-100 text-gray-700'">
                        <div class="flex items-center space-x-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" 
                                 fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                 [ngClass]="'lucide lucide-' + sector.icon" [innerHTML]="sector.iconSvgSafe"></svg>
                            <div class="flex-1">
                                <h4 class="font-merriweather font-semibold text-xs">{{ sector.title }}</h4>
                                <div class="flex items-center space-x-3 mt-0.5">
                                    <span class="text-xs opacity-80">{{ sector.investment }}</span>
                                    <span class="text-xs opacity-80">{{ sector.jobs }}</span>
                                </div>
                            </div>
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" 
                                 fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" 
                                 class="lucide lucide-chevron-right" [innerHTML]="chevronSvgSafe"></svg>
                        </div>
                    </button>
                </div>
            </div>
            <div class="lg:col-span-2">
                <div class="rounded-2xl p-6 transition-all duration-300" [ngClass]="selectedSector.bgClass">
                    <div class="flex flex-col md:flex-row items-start space-y-4 md:space-y-0 md:space-x-6 mb-6 gap-6">
                        <div class="w-16 h-16 rounded-lg flex items-center justify-center"
                             [ngClass]="selectedSector.gradientClass">
                            <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" 
                                 fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                 [ngClass]="'lucide lucide-' + selectedSector.icon" [innerHTML]="selectedSector.iconSvgSafe"></svg>
                        </div>
                        <div class="flex-1">
                            <h3 class="font-merriweather font-bold text-xl text-gray-900 mb-2">{{ selectedSector.title }}</h3>
                            <p class="font-open-sans text-gray-600 text-sm leading-relaxed mb-4">{{ selectedSector.description }}</p>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div *ngFor="let stat of selectedSector.stats" class="text-center">
                                    <div class="text-xl font-bold mb-1" [ngClass]="'text-' + stat.color">{{ stat.value }}</div>
                                    <div class="text-xs text-gray-600">{{ stat.label }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <div class="bg-white rounded-lg p-4 shadow-sm">
                            <h4 class="font-merriweather font-bold text-base text-gray-900 mb-3 flex items-center">
                                <svg xmlns="http://www.w3.org/200 three/svg" width="18" height="18" viewBox="0 0 24 24" 
                                     fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" 
                                     class="lucide lucide-factory mr-2 text-blue-600" [innerHTML]="policySvgSafe"></svg>
                                Key Policies
                            </h4>
                            <ul class="space-y-1.5">
                                <li *ngFor="let policy of selectedSector.policies" class="font-open-sans text-xs text-gray-600 flex items-start">
                                    <span class="w-1.5 h-1.5 rounded-full mt-1.5 mr-2 flex-shrink-0" [ngClass]="'bg-' + policy.iconColor"></span>
                                    {{ policy.text }}
                                </li>
                            </ul>
                        </div>
                        <div class="bg-white rounded-lg p-4 shadow-sm">
                            <h4 class="font-merriweather font-bold text-base text-gray-900 mb-3 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" 
                                     fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" 
                                     class="lucide lucide-trending-up mr-2 text-green-600" [innerHTML]="opportunitySvgSafe"></svg>
                                Opportunities
                            </h4>
                            <ul class="space-y-1.5">
                                <li *ngFor="let opportunity of selectedSector.opportunities" class="font-open-sans text-xs text-gray-600 flex items-start">
                                    <span class="w-1.5 h-1.5 rounded-full mt-1.5 mr-2 flex-shrink-0" [ngClass]="'bg-' + opportunity.iconColor"></span>
                                    {{ opportunity.text }}
                                </li>
                            </ul>
                        </div>
                        <div class="bg-white rounded-lg p-4 shadow-sm">
                            <h4 class="font-merriweather font-bold text-base text-gray-900 mb-3 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" 
                                     fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" 
                                     class="lucide lucide-leaf mr-2 text-orange-600" [innerHTML]="incentiveSvgSafe"></svg>
                                Incentives
                            </h4>
                            <ul class="space-y-1.5">
                                <li *ngFor="let incentive of selectedSector.incentives" class="font-open-sans text-xs text-gray-600 flex items-start">
                                    <span class="w-1.5 h-1.5 rounded-full mt-1.5 mr-2 flex-shrink-0" [ngClass]="'bg-' + incentive.iconColor"></span>
                                    {{ incentive.text }}
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="flex justify-center mt-6">
                        <button (click)="applyForInvestment()" 
                                class="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-6 py-2 rounded-lg font-semibold 
                                hover:from-orange-600 hover:to-orange-700 transition-all duration-300 transform hover:scale-105">
                            Apply Now
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>