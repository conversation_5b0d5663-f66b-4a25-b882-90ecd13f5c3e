@import url('https://fonts.googleapis.com/css2?family=Merriweather:wght@700&family=Open+Sans:wght@400;600&display=swap');

body {
  margin: 0;
  font-family: 'Open Sans', sans-serif;
  overflow-x: hidden;
}

.min-h-screen {
  min-height: 100vh;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.overflow-hidden {
  overflow: hidden;
}

.absolute {
  position: absolute;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.z-0 {
  z-index: 0;
}

.z-5 {
  z-index: 5;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.from-blue-700\/60 {
  // --tw-gradient-from: rgba(29, 78, 216, 0.6);
  --tw-gradient-from: rgba(17, 190, 221, 0.6);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(29, 78, 216, 0));
}

.via-blue-600\/60 {
  // --tw-gradient-stops: var(--tw-gradient-from), rgba(37, 99, 235, 0.6), var(--tw-gradient-to, rgba(37, 99, 235, 0));
  --tw-gradient-stops: var(--tw-gradient-from), rgba(255, 146, 3, 0.6), var(--tw-gradient-to, rgba(37, 99, 235, 0));
}

.to-teal-700\/60 {
  // --tw-gradient-to: rgba(15, 118, 110, 0.6);
  --tw-gradient-to: rgba(17, 190, 221, 0.6);
}

.random-dot {
  width: 0.5rem;
  height: 0.5rem;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 9999px;
  position: absolute;
  animation: random-dot 5s ease-in-out infinite;
}

@keyframes random-dot {
  0%, 100% {
    opacity: 0;
    transform: scale(0);
  }
  20%, 80% {
    opacity: 0.7;
    transform: scale(1);
  }
}

.w-96 {
  width: 24rem;
}

.h-96 {
  height: 24rem;
}

.bg-gradient-radial {
  background-image: radial-gradient(var(--tw-gradient-stops));
}

.from-orange-400\/10 {
  --tw-gradient-from: rgba(251, 191, 36, 0.1);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(251, 191, 36, 0));
}

.to-transparent {
  --tw-gradient-to: transparent;
}

.blur-2xl {
  filter: blur(40px);
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.relative {
  position: relative;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.bg-cover {
  background-size: cover;
}

.bg-center {
  background-position: center;
}

.bg-no-repeat {
  background-repeat: no-repeat;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-1000 {
  transition-duration: 1000ms;
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.max-w-7xl {
  max-width: 80rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.sm\:px-6 {
  @media (min-width: 640px) {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

.lg\:px-8 {
  @media (min-width: 1024px) {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.text-center {
  text-align: center;
}

.inline-flex {
  display: inline-flex;
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  margin-left: calc(0.5rem * var(--tw-space-x-reverse));
}

.bg-white\/10 {
  background-color: rgba(255, 255, 255, 0.1);
}

.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.text-white {
  color: #ffffff;
}

.font-medium {
  font-weight: 500;
}

.font-merriweather {
  font-family: 'Merriweather', serif;
}

.font-bold {
  font-weight: 700;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.sm\:text-5xl {
  @media (min-width: 640px) {
    font-size: 3rem;
    line-height: 1;
  }
}

.lg\:text-6xl {
  @media (min-width: 1024px) {
    font-size: 3.75rem;
    line-height: 1;
  }
}

.mb-8 {
  margin-bottom: 2rem;
}

.leading-tight {
  line-height: 1.25;
}

.text-orange-400 {
  color: #fb923c;
}

.inline-block {
  display: inline-block;
}

.mr-4 {
  margin-right: 1rem;
}

.animate-fade-in-up {
  animation: fade-in-up 0.5s ease-out forwards;
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.font-open-sans {
  font-family: 'Open Sans', sans-serif;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.sm\:text-2xl {
  @media (min-width: 640px) {
    font-size: 1.5rem;
    line-height: 2rem;
  }
}

.text-gray-200 {
  color: #e5e7eb;
}

.max-w-4xl {
  max-width: 56rem;
}

.leading-relaxed {
  line-height: 1.625;
}

.mb-12 {
  margin-bottom: 3rem;
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-orange-500 {
  --tw-gradient-from: #f97316;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(249, 115, 22, 0));
}

.to-orange-600 {
  --tw-gradient-to: #ea580c;
}

.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.rounded-full {
  border-radius: 9999px;
}

.font-semibold {
  font-weight: 600;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.transform {
  transform: translate(0, 0);
}

.hover\:scale-105:hover {
  transform: scale(1.05);
}

.shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.hover\:shadow-orange-400\/40:hover {
  box-shadow: 0 10px 15px -3px rgba(249, 115, 22, 0.4), 0 4px 6px -2px rgba(249, 115, 22, 0.4);
}

.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
  margin-left: calc(0.75rem * var(--tw-space-x-reverse));
}

.min-w-\[280px\] {
  min-width: 280px;
}

.group-hover\:translate-x-1:hover {
  transform: translateX(0.25rem);
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.duration-200 {
  transition-duration: 200ms;
}

.grid {
  display: grid;
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.gap-6 {
  gap: 1.5rem;
}

.p-4 {
  padding: 1rem;
}

.group-hover\:bg-white\/20:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.sm\:text-3xl {
  @media (min-width: 640px) {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.group-hover\:text-orange-300:hover {
  color: #fdba74;
}

.text-gray-300 {
  color: #d1d5db;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.bottom-8 {
  bottom: 2rem;
}

.left-1\/2 {
  left: 50%;
}

.-translate-x-1\/2 {
  transform: translateX(-50%);
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.3);
  border: none;
  cursor: pointer;
}

.dot:hover {
  background: rgba(255, 255, 255, 0.5);
}

.dot.active {
  background: #fb923c;
  width: 24px;
  border-radius: 4px;
}

.right-8 {
  right: 2rem;
}

.w-6 {
  width: 1.5rem;
}

.h-10 {
  height: 2.5rem;
}

.border-2 {
  border-width: 2px;
}

.border-white\/50 {
  border-color: rgba(255, 255, 255, 0.5);
}

.w-1 {
  width: 0.25rem;
}

.h-3 {
  height: 0.75rem;
}

.bg-white\/70 {
  background-color: rgba(255, 255, 255, 0.7);
}

.mt-2 {
  margin-top: 0.5rem;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

.opacity-5 {
  opacity: 0.05;
} 