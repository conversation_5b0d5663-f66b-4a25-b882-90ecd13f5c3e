.dynamic-form {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
  background-color: var(--color-primary);
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px var(--shadow-light);
  
  .form-fields {
    margin-bottom: 2rem;
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--color-border-light);
  }
}

.date-picker-container {
  margin-bottom: 1.5rem;
  
  .form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--color-text-dark);
    font-size: 0.875rem;
    
    .required-indicator {
      color: var(--color-poppy-red);
      margin-left: 0.25rem;
    }
  }
  
  .date-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--color-form-input-border);
    border-radius: 0.375rem;
    font-size: 1rem;
    color: var(--color-form-input-text);
    background-color: var(--color-form-input-bg);
    transition: all 0.2s ease-in-out;
    
    &:hover {
      border-color: var(--color-form-input-border-hover);
    }
    
    &:focus {
      outline: none;
      border-color: var(--color-form-input-border-focus);
      box-shadow: 0 0 0 3px var(--shadow-focus);
    }
    
    &.error {
      border-color: var(--color-form-input-error);
      background-color: var(--color-notification-error-bg);
      
      &:focus {
        box-shadow: 0 0 0 3px hsla(348, 99%, 33%, 0.15);
      }
    }
    
    // Custom date picker styling
    &::-webkit-calendar-picker-indicator {
      cursor: pointer;
      background-color: var(--color-tertiary);
      color: white;
      border-radius: 0.25rem;
      padding: 0.25rem;
      
      &:hover {
        background-color: var(--color-tertiary);
      }
    }
  }
  
  .error-messages {
    margin-top: 0.5rem;
    
    .error-message {
      display: block;
      color: var(--color-notification-error-text);
      font-size: 0.875rem;
      margin-bottom: 0.25rem;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Global form styles
.form-container {
  background-color: var(--color-primary);
  min-height: 100vh;
  padding: 2rem;
  
  .form-wrapper {
    max-width: 800px;
    margin: 0 auto;
    
    .form-header {
      text-align: center;
      margin-bottom: 2rem;
      
      h1 {
        color: var(--color-text-dark);
        font-size: 2rem;
        margin-bottom: 0.5rem;
      }
      
      p {
        color: var(--color-text-medium);
        font-size: 1.125rem;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .dynamic-form {
    padding: 1rem;
    margin: 1rem;
  }
  
  .form-container {
    padding: 1rem;
  }
  
  .form-actions {
    flex-direction: column;
    
    .btn {
      width: 100%;
    }
  }
}

// Animation classes
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Focus ring utilities
.focus-ring {
  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px var(--shadow-focus);
  }
}

// Loading states
.loading {
  position: relative;
  pointer-events: none;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1rem;
    height: 1rem;
    margin: -0.5rem 0 0 -0.5rem;
    border: 2px solid var(--color-tertiary-shade);
    border-top: 2px solid var(--color-tertiary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}