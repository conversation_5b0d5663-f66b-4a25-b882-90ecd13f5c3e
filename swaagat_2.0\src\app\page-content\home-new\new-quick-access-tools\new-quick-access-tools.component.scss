@import url('https://fonts.googleapis.com/css2?family=Merriweather:wght@400;700&family=Open+Sans:wght@400;600&display=swap');

body {
  font-family: 'Open Sans', sans-serif;
  margin: 0;
  padding: 0;
  background-color: #f9fafb;
}

.container {
  max-width: 1280px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.px-20{
  padding-left: 5rem;
  padding-right: 5rem;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.text-center {
  text-align: center;
}

.mb-16 {
  margin-bottom: 4rem;
}

.font-merriweather {
  font-family: 'Merriweather', serif;
}

.font-bold {
  font-weight: 700;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.sm\:text-4xl {
  @media (min-width: 640px) {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

.text-gray-900 {
  color: #111827;
}

.mb-4 {
  margin-bottom: 1rem;
}

.font-open-sans {
  font-family: 'Open Sans', sans-serif;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-gray-600 {
  color: #4b5563;
}

.max-w-3xl {
  max-width: 48rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

.gap-6 {
  gap: 1.5rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

button {
  border: none;
  cursor: pointer;
  outline: none;
}

.bg-blue-500 {
  background-color: #3b82f6;
}

.hover\:bg-blue-600:hover {
  background-color: #2563eb;
}

.bg-green-500 {
  background-color: #22c55e;
}

.hover\:bg-green-600:hover {
  background-color: #16a34a;
}

.bg-indigo-500 {
  background-color: #6366f1;
}

.hover\:bg-indigo-600:hover {
  background-color: #4f46e5;
}

.bg-purple-500 {
  background-color: #a855f7;
}

.hover\:bg-purple-600:hover {
  background-color: #9333ea;
}

.bg-pink-500 {
  background-color: #ec4899;
}

.hover\:bg-pink-600:hover {
  background-color: #db2777;
}

.bg-yellow-500 {
  background-color: #eab308;
}

.hover\:bg-yellow-600:hover {
  background-color: #ca8a04;
}

.bg-cyan-500 {
  background-color: #06b6d4;
}

.hover\:bg-cyan-600:hover {
  background-color: #0891b2;
}

.bg-red-500 {
  background-color: #ef4444;
}

.hover\:bg-red-600:hover {
  background-color: #dc2626;
}

.text-white {
  color: #ffffff;
}

.p-6 {
  padding: 1.5rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.transform {
  transform: translate(0, 0);
}

.hover\:scale-105:hover {
  transform: scale(1.05);
}

.shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.hover\:shadow-xl:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.text-left {
  text-align: left;
}

.flex {
  display: flex;
}

.items-start {
  align-items: flex-start;
}

.justify-between {
  justify-content: space-between;
}

.mb-4 {
  margin-bottom: 1rem;
}

.group:hover .group-hover\:scale-110 {
  transform: scale(1.1);
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

.bg-white\/20 {
  background-color: rgba(255, 255, 255, 0.2);
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.rounded-full {
  border-radius: 9999px;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-semibold {
  font-weight: 600;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.opacity-90 {
  opacity: 0.9;
}