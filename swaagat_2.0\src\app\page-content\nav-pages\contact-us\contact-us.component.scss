@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:host {
    display: block;
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #1F2937;
    background: #F9FAFB;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    max-width: clamp(60rem, 90%, 75rem);
    margin: 0 auto;
    padding: 1.5rem;
}

/* Hero Section */
.ContactUs-section {
    text-align: center;
    margin-bottom: 3rem;
    padding: 1.5rem 1rem;
    background: linear-gradient(135deg, #1E40AF 0%, #3B82F6 100%);
    border-radius: 1rem;
    color: #FFFFFF;
    position: relative;
    overflow: hidden;
}

.ContactUs-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.hero-title {
    font-size: clamp(1.75rem, 3vw, 2.25rem);
    font-weight: 700;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 1;
}

.hero-subtitle {
    font-size: 0.875rem;
    opacity: 0.9;
    position: relative;
    z-index: 1;
    max-width: 31.25rem;
    margin: 0 auto;
}

/* Contact Grid */
.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.contact-card {
    background: #FFFFFF;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 0.0625rem 0.1875rem 0 rgba(0, 0, 0, 0.1), 0 0.0625rem 0.125rem 0 rgba(0, 0, 0, 0.06);
    border: 0.0625rem solid #E5E7EB;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.contact-card:hover {
    transform: translateY(-0.25rem);
    box-shadow: 0 0.625rem 0.9375rem -0.1875rem rgba(0, 0, 0, 0.1), 0 0.25rem 0.375rem -0.125rem rgba(0, 0, 0, 0.05);
    border-color: #60A5FA;
}

.contact-card.secondary:hover {
    border-color: #A78BFA;
}

.card-header {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 0.0625rem solid #E5E7EB;
}

.card-icon {
    width: 3.5rem;
    height: 3.5rem;
    background: #F3F4F6;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
}

.card-icon.secondary {
    width: 3.75rem;
    height: 3.75rem;
}

.card-icon img {
    width: 2.5rem;
    height: 2.5rem;
    object-fit: contain;
}

.card-icon:hover {
    transform: scale(1.05);
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 0.25rem;
    line-height: 1.3;
}

.card-subtitle {
    color: #4B5563;
    font-size: 0.875rem;
    font-weight: 400;
}

/* Contact Info */
.contact-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.info-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem;
    border-radius: 0.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    background: #F3F4F6;
    border: 0.0625rem solid transparent;
}

.info-item:hover {
    background: #FFFFFF;
    border-color: #E5E7EB;
    transform: translateX(0.25rem);
    box-shadow: 0 0.0625rem 0.1875rem 0 rgba(0, 0, 0, 0.1), 0 0.0625rem 0.125rem 0 rgba(0, 0, 0, 0.06);
}

.info-icon {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.75rem;
    margin-top: 0.125rem;
    flex-shrink: 0;
    filter: invert(46%) sepia(99%) saturate(1350%) hue-rotate(208deg) brightness(100%) contrast(101%); /* Blue tint */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.info-item.secondary .info-icon {
    filter: invert(45%) sepia(90%) saturate(1350%) hue-rotate(240deg) brightness(95%) contrast(101%); /* Violet tint */
}

.info-content {
    flex: 1;
}

.info-label {
    font-weight: 500;
    color: #1F2937;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.info-value {
    color: #4B5563;
    font-size: 0.875rem;
    line-height: 1.5;
}

.info-value a {
    color: #3B82F6;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.info-value a:hover {
    color: #1E40AF;
    text-decoration: underline;
}

.info-item.secondary .info-value a {
    color: #8B5CF6;
}

.info-item.secondary .info-value a:hover {
    color: #8B5CF6;
}

/* Map Section */
.map-card {
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 0.0625rem 0.1875rem 0 rgba(0, 0, 0, 0.1), 0 0.0625rem 0.125rem 0 rgba(0, 0, 0, 0.06);
    border: 0.0625rem solid #E5E7EB;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin-top: 2rem;
}

.map-card:hover {
    transform: translateY(-0.125rem);
    box-shadow: 0 0.25rem 0.375rem -0.0625rem rgba(0, 0, 0, 0.1), 0 0.125rem 0.25rem -0.0625rem rgba(0, 0, 0, 0.06);
}

.map-card-header {
    background: linear-gradient(135deg, #3B82F6 0%, #60A5FA 100%);
    color: #FFFFFF;
    padding: 1rem 1.5rem;
    font-weight: 600;
    text-align: center;
}

.map-card-header.secondary {
    background: linear-gradient(135deg, #8B5CF6 0%, #A78BFA 100%);
}

.map-embed {
    height: 17.5rem;
    width: 100%;
    border: none;
    display: block;
}

/* Copy notification */
.copy-notification {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: #10B981;
    color: #FFFFFF;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 0.625rem 0.9375rem -0.1875rem rgba(0, 0, 0, 0.1), 0 0.25rem 0.375rem -0.125rem rgba(0, 0, 0, 0.05);
    transform: translateY(6.25rem);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    font-weight: 500;
    font-size: 0.875rem;
}

.copy-notification {
    transform: translateY(0);
    opacity: 1;
}

/* Responsive Design */
@media (max-width: 48rem) {
    .container {
        padding: 1rem;
    }
    
    .ContactUs-section {
        padding: 2.5rem 1.5rem;
        margin-bottom: 2.5rem;
    }
    
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        margin-bottom: 2.5rem;
    }
    
    .contact-card {
        padding: 1.5rem;
    }
    
    .map-card {
        margin-top: 1.5rem;
    }
    
    .hero-title {
        font-size: clamp(1.5rem, 3vw, 2rem);
    }
}

/* Subtle animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(1.25rem);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.contact-card {
    animation: fadeInUp 0.6s ease-out;
}

.contact-card:nth-child(2) {
    animation-delay: 0.1s;
}

.map-card {
    animation: fadeInUp 0.6s ease-out;
}

.map-card:nth-child(2) {
    animation-delay: 0.1s;
}