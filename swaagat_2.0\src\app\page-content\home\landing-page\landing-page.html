<section class="hero-section">
  <!-- Image Slider Background -->
  <div class="slider-container">
    <div
      *ngFor="let slide of slideContent; let i = index"
      class="slide"
      [class.active]="i === currentSlide"
      [style.background-image]="'url(' + slide.imageUrl + ')'">
    </div>
  </div>

  <!-- Gradient Overlay -->
  <div class="gradient-overlay"></div>

  <!-- Content Container -->
  <div class="content-container">
    <!-- Left Section (30%) -->
    <div class="left-section">
      <div class="person-info">
        <!-- First Person -->
        <div class="person-card">
          <div class="logo-circle">
            <img [src]="personInfo.person1.imageUrl" [alt]="personInfo.person1.name" class="person-image">
          </div>
          <div class="person-details">
            <h3 class="person-name">{{ personInfo.person1.name }}</h3>
            <p class="person-designation">{{ personInfo.person1.designation }}</p>
            <p class="person-message">{{ personInfo.person1.message }}</p>
          </div>
        </div>

        <!-- Second Person -->
        <div class="person-card">
          <div class="logo-circle">
            <img [src]="personInfo.person2.imageUrl" [alt]="personInfo.person2.name" class="person-image">
          </div>
          <div class="person-details">
            <h3 class="person-name">{{ personInfo.person2.name }}</h3>
            <p class="person-designation">{{ personInfo.person2.designation }}</p>
            <p class="person-message">{{ personInfo.person2.message }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Section (70%) -->
    <div class="right-section">
      <div class="content-wrapper" [class.animate]="!isAnimating">
        <h1 class="title">{{ getCurrentSlide().title }}</h1>
        <p class="paragraph">{{ getCurrentSlide().paragraph }}</p>
        <button class="action-button" (click)="onButtonClick()">
          Get Started
        </button>
      </div>
    </div>
  </div>

  <!-- Slide Indicators -->
  <div class="slide-indicators">
    <div
      *ngFor="let slide of slideContent; let i = index"
      class="indicator"
      [class.active]="i === currentSlide"
      (click)="onSlideClick(i)">
    </div>
  </div>
</section>