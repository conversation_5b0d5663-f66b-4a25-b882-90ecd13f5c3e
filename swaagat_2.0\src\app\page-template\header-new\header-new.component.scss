@use 'sass:color';

// Variables
$primary-blue: #004aad;
$secondary-blue: #00b4d8;
$white: #ffffff;
$gray-100: #f3f4f6;
$gray-300: #e5e7eb;
$gray-500: #4b5563;
$success-green: #10b981;
$success-green-dark: #047857;

// Mixins
@mixin button-base {
  display: flex;
  align-items: center;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 600;
  background: none;
  gap: 8px;
  padding: 12px 16px;
  font-size: 14px;
  text-decoration: none;
  outline: none;
}

@mixin icon-hover {
  &:hover svg {
    transform: scale(1.1);
  }
}

// Main header container
.brand-header {
  background-color: $white;
  border-bottom: 1px solid $gray-300;
  height: 10vh;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 5vw;
  max-width: 1440px;
  min-width: 100vw;
  margin: 0 auto;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

// Logo section
.logo-section {
  display: flex;
  align-items: center;

  .logo {
    max-width: 15vw;
    height: auto;
  }
}

// Action section (right side)
.action-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

// Action buttons
.action-btn {
  @include button-base;
  @include icon-hover;

  &.secondary {
    color: $gray-500;

    &:hover {
      color: $primary-blue;
      background-color: $gray-100;
    }
  }

  &.primary {
    background: linear-gradient(to right, $primary-blue, $secondary-blue);
    color: $white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    
    &:hover {
      background: linear-gradient(to right, #003d91, #0099c7);
      box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
    }
  }

  // New registration button styling
  &.register-btn {
    background: linear-gradient(90deg, #0051b4, #01b1d4);
    color: $white;
    box-shadow: 0 4px 6px rgba(0, 81, 180, 0.3);
    position: relative;
    overflow: hidden;
    
    &:hover {
      background: linear-gradient(90deg, #00429a, #0096b9);
      box-shadow: 0 6px 12px rgba(0, 81, 180, 0.4);
      transform: translateY(-1px);
    }
    
    &:active {
      transform: translateY(0);
    }
    
    // Subtle animation effect
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }
    
    &:hover::before {
      left: 100%;
    }
  }

  .main-icon {
    width: 20px;
    height: 20px;
    stroke: currentColor;
    stroke-width: 2;
    stroke-linecap: round;
    stroke-linejoin: round;
    transition: transform 0.2s ease;
  }
}

// Responsive design
@media (max-width: 768px) {
  .brand-header {
    padding: 0 16px;
    height: 70px;
  }

  .logo-section .logo {
    max-width: 25vw;
  }

  .action-section {
    gap: 12px;
  }

  .action-btn {
    padding: 10px 12px;
    font-size: 13px;

    span {
      display: none;
    }
  }
}

@media (max-width: 480px) {
  .brand-header {
    padding: 0 12px;
    height: 60px;
  }

  .logo-section .logo {
    max-width: 35vw;
  }

  .action-section {
    gap: 8px;
  }

  .action-btn {
    padding: 8px;

    .main-icon {
      width: 18px;
      height: 18px;
    }
  }
}