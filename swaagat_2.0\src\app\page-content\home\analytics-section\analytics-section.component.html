<section class="analytics-section-modern">
  <div class="analytics-wrapper">

    <!-- Header Section -->
    <div class="analytics-header">
      <span class="analytics-tagline">Real-time Insights</span>
      <h2 class="analytics-title">
        Application <span class="highlight">Analytics</span> Dashboard
      </h2>
      <p class="analytics-description">
        Track application processing performance with comprehensive metrics and data visualizations.
        Monitor NOC issuance, investor queries, and system efficiency in real-time.
      </p>
    </div>

    <!-- KPI Cards Grid -->
    <div class="kpi-cards-grid">
      <div *ngFor="let kpi of kpiData" class="kpi-card" [ngClass]="getColorClass(kpi.color)">
        <div class="kpi-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path [attr.d]="kpi.icon"></path>
          </svg>
        </div>
        <div class="kpi-content">
          <h3 class="kpi-title">{{ kpi.title }}</h3>
          <div class="kpi-value">{{ kpi.value }}</div>
          <div class="kpi-change" [ngClass]="getTrendClass(kpi.trend)">
            {{ kpi.change }}
          </div>
        </div>
      </div>
    </div>

    <!-- Summary Stats -->
    <div class="summary-stats">
      <div *ngFor="let stat of summaryStats" class="stat-item">
        <span class="stat-number">{{ stat.value }}</span>
        <small>{{ stat.label }}</small>
      </div>
    </div>

  </div>
</section>