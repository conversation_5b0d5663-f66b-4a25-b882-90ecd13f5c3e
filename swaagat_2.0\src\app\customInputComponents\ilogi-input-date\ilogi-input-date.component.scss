.form-group {
  // margin-bottom: 1.25rem;
  position: relative;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--color-text-dark, #333);
}

.required-indicator { 
  color: var(--color-poppy-red, #dc3545);
  margin-left: 0.25rem;
}

.readonly-display {
  padding: 0.75rem 1rem;
  background-color: var(--color-background-light, #f8f9fa);
  border: 1px solid var(--color-border, #ced4da);
  border-radius: 6px;
  font-size: 1rem;
  color: var(--color-text-dark, #333);
  min-height: calc(1.5em + 0.75rem + 2px);
  word-break: break-word;
}

.date-input-container {
  position: relative;
  display: inline-block;
  width: 100%;
}

.form-control.date-input {
  border: 1px solid var(--color-border, #ced4da);
  border-radius: 6px;
  background-color: var(--color-background, #ffffff);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  color: var(--color-text-dark, #333333);
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  width: 100%;
  padding-right: 3rem; // Space for calendar icon

  &:focus {
    outline: none;
    border-color: var(--color-poppy-blue, #0a54c4);
    box-shadow: 0 0 0 3px rgba(6, 41, 236, 0.2);
  }

  &.is-invalid {
    border-color: var(--color-poppy-red, #dc3545);

    &:focus {
      box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2);
    }
  }

  &:disabled {
    background-color: var(--color-background-light, #f8f9fa);
    cursor: not-allowed;
  }
}

.calendar-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: var(--color-text-dark, #333);
  font-size: 1.2rem;
  background: none;
  border: none;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  &:active {
    background-color: rgba(0, 0, 0, 0.1);
  }
}

.invalid-input {
  opacity: 0;
  color: var(--color-poppy-red, #dc3545);
  font-size: 0.875rem;
  margin-top: 0.25rem;
  transform: translateY(-4px);
  transition: opacity 0.2s ease, transform 0.2s ease;

  &.show-error {
    opacity: 1;
    transform: translateY(0);
  }
}

.x-error-msg-text {
  font-size: 0.875rem;
  line-height: 1.5;
}

// Hide the default date picker icon in WebKit browsers
.date-input::-webkit-calendar-picker-indicator {
  display: none;
}

.date-input-container  {
   height: 32px;
  width: 100%;
  // padding: 4px 8px;
  font-size: 0.8rem; 
  background-color: #fff;
  color: #3b3b3b;
  // border: 1px solid #b8b8b8
}

.date-input-container input {
  //  height: 32px;
    width: 100%;

      padding: 10px 8px;

  font-size: 0.8rem; 
  background-color: #fff;
  color: #3b3b3b;
  border: 1px solid #b8b8b8
}
label{
     font-size: 0.75rem;
  color: #2C82AA;
  margin-bottom: 0;

}

.date-input-container .mat-icon{
  // width: 0!important;
  height: 27px!important;
  fill: #2C82AA;
  color: #b8b8b8;
}
.mat-datepicker-content{
  background: #fff!important;
}

