import { Routes } from "@angular/router";
import { PageContentComponent } from "./page-content.component";
import { LoginComponent } from "./auth/login/login.component";
import { HomeComponent } from "./home/<USER>";





export const PAGE_CONTENT_ROUTES: Routes = [
    {
        path: '',
        canActivate: [],

        children: [
            {
                path: 'login',
                loadComponent: () =>
                    import('./auth/login/login.component').then(m => m.LoginComponent)
            },{
                path: 'registration',
                loadComponent: () =>
                    import('./auth/registration/registration.component').then(m => m.RegistrationComponent)
            },{
                path: 'admin',
                loadComponent: () => import('./auth/admin-login/admin-login.component').then(m => m.AdminLoginComponent)
            },{
                path: 'about-us',
                loadComponent: () =>
                    import('./nav-pages/about-us/about-us.component').then(m => m.AboutUsComponent)
            },{
                path: 'related-departments',
                loadComponent: () =>
                    import('./nav-pages/related-departments/related-departments.component').then(m => m.RelatedDepartmentsComponent)
            },{
                path: 'information-wizard',
                loadComponent: () =>
                    import('./nav-pages/information-wizard/information-wizard.component').then(m => m.InformationWizardComponent)
            },{
                path: 'acts-rules',
                loadComponent: () =>
                    import('./nav-pages/acts-rules/acts-rules.component').then(m => m.ActsRulesComponent)
            },{
                path: 'contact-us',
                loadComponent: () =>
                    import('./nav-pages/contact-us/contact-us.component').then(m => m.ContactUsComponent)
            },{
                path: 'feedback-rating',
                loadComponent: () =>
                    import('./nav-pages/feedback-rating/feedback-rating.component').then(m => m.FeedbackRatingComponent)
            },{
                path: 'incentive-calculator',
                loadComponent: () =>
                    import('./nav-pages/incentive-calculator/incentive-calculator.component').then(m => m.IncentiveCalculatorComponent)
            },
            
        ]
    }
];                  