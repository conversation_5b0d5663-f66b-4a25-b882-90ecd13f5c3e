@import url('https://fonts.googleapis.com/css2?family=Merriweather:wght@400;700&family=Open+Sans:wght@400;600&display=swap');

body {
    font-family: 'Open Sans', sans-serif;
    margin: 0;
    padding: 0;
}

.py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
}

.bg-white {
    background-color: #ffffff;
}

.max-w-7xl {
    max-width: 80rem;
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
}

.sm\:px-6 {
    @media (min-width: 640px) {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

.lg\:px-8 {
    @media (min-width: 1024px) {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

.text-center {
    text-align: center;
}

.mb-12 {
    margin-bottom: 3rem;
}

.font-merriweather {
    font-family: 'Merriweather', serif;
}

.font-bold {
    font-weight: 700;
}

.text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
}

.sm\:text-3xl {
    @media (min-width: 640px) {
        font-size: 1.875rem;
        line-height: 2.25rem;
    }
}

.text-gray-900 {
    color: #1a202c;
}

.mb-4 {
    margin-bottom: 1rem;
}

.font-open-sans {
    font-family: 'Open Sans', sans-serif;
}

.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
}

.text-gray-600 {
    color: #4a5568;
}

.max-w-3xl {
    max-width: 48rem;
}

.rounded-2xl{
    border-radius: 1rem;
}
.mt-6{
    margin-top: 1.5rem;
}
.grid {
    display: grid;
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

.lg\:grid-cols-3 {
    @media (min-width: 1024px) {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
}

.gap-6 {
    gap: 1.5rem;
}

.lg\:col-span-1 {
    @media (min-width: 1024px) {
        grid-column: span 1 / span 1;
    }
}

.lg\:col-span-2 {
    @media (min-width: 1024px) {
        grid-column: span 2 / span 2;
    }
}

.space-y-1 > * + * {
    margin-top: 0.25rem;
}

button {
    cursor: pointer;
    border: none;
    outline: none;
}

.w-full {
    width: 100%;
}

.text-left {
    text-align: left;
}

.p-3 {
    padding: 0.75rem;
}

.rounded-lg {
    border-radius: 0.5rem;
}

.transition-all {
    transition: all 0.3s ease;
}

.duration-300 {
    transition-duration: 300ms;
}

.bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-blue-500 {
    --tw-gradient-from: #3b82f6;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(59, 130, 246, 0));
}

.to-purple-600 {
    --tw-gradient-to: #9333ea;
}

.text-white {
    color: #ffffff;
}

.shadow-md {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.transform {
    transform: translate(0, 0) scale(1);
}

.scale-102 {
    transform: scale(1.02);
}

.bg-gray-50 {
    background-color: #f9fafb;
}

.hover\:bg-gray-100:hover {
    background-color: #f3f4f6;
}

.text-gray-700 {
    color: #374151;
}

.flex {
    display: flex;
}

.items-center {
    align-items: center;
}

.space-x-2 > * + * {
    margin-left: 0.5rem;
}

.flex-1 {
    flex: 1 1 0%;
}

.mt-0\.5 {
    margin-top: 0.125rem;
}

.text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
}

.opacity-80 {
    opacity: 0.8;
}

.lucide {
    display: inline-block;
    vertical-align: middle;
}

.p-6 {
    padding: 1.5rem;
}

.items-start {
    align-items: flex-start;
}

.mb-6 {
    margin-bottom: 1.5rem;
}

.w-16 {
    width: 4rem;
}

.h-16 {
    height: 4rem;
}

.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.leading-relaxed {
    line-height: 1.625;
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
}

.md\:grid-cols-4 {
    @media (min-width: 768px) {
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }
}

.gap-4 {
    gap: 1rem;
}

.text-green-600 {
    color: #059669;
}

.text-blue-600 {
    color: #2563eb;
}

.text-orange-600 {
    color: #f97316;
}

.text-purple-600 {
    color: #9333ea;
}

.justify-center {
    justify-content: center;
}

.shadow-sm {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.p-4 {
    padding: 1rem;
}

.text-base {
    font-size: 1rem;
    line-height: 1.5rem;
}

.mr-2 {
    margin-right: 0.5rem;
}

.space-y-1\.5 > * + * {
    margin-top: 0.375rem;
}

.w-1\.5 {
    width: 0.375rem;
}

.h-1\.5 {
    height: 0.375rem;
}

.bg-blue-400 {
    background-color: #60a5fa;
}

.bg-green-400 {
    background-color: #34d399;
}

.bg-orange-400 {
    background-color: #fb923c;
}

.rounded-full {
    border-radius: 9999px;
}

.mt-1\.5 {
    margin-top: 0.375rem;
}

.mr-2 {
    margin-right: 0.5rem;
}

.flex-shrink-0 {
    flex-shrink: 0;
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

.sm\:grid-cols-3 {
    @media (min-width: 640px) {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
}

.from-orange-500 {
    --tw-gradient-from: #f97316;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(249, 115, 22, 0));
}

.to-orange-600 {
    --tw-gradient-to: #ea580c;
}

.px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

.py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

.font-semibold {
    font-weight: 600;
}

.hover\:from-orange-600:hover {
    --tw-gradient-from: #ea580c;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(234, 88, 12, 0));
}

.hover\:to-orange-700:hover {
    --tw-gradient-to: #c2410c;
}

.bg-it-electronics {
    background-color: #e6f0ff;
}

.bg-agro-processing {
    background-color: #e6ffec;
}

.bg-tourism-hospitality {
    background-color: #fff5e6;
}

.bg-textiles-handloom {
    background-color: #f0e6ff;
}

.bg-renewable-energy {
    background-color: #e6fff5;
}

.bg-rubber-plantation {
    background-color: #ffe6e6;
}

.gradient-it-electronics {
    background-image: linear-gradient(to right, #3b82f6, #1e40af);
}

.gradient-agro-processing {
    background-image: linear-gradient(to right, #22c55e, #15803d);
}

.gradient-tourism-hospitality {
    background-image: linear-gradient(to right, #f97316, #c2410c);
}

.gradient-textiles-handloom {
    background-image: linear-gradient(to right, #9333ea, #6b21a8);
}

.gradient-renewable-energy {
    background-image: linear-gradient(to right, #14b8a6, #0f766e);
}

.gradient-rubber-plantation {
    background-image: linear-gradient(to right, #ef4444, #b91c1c);
}