{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-basemodelholder.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { signal, computed, Directive } from '@angular/core';\nimport { isNotEmpty } from '@primeuix/utils';\nimport { BaseComponent } from 'primeng/basecomponent';\nclass BaseModelHolder extends BaseComponent {\n  modelValue = signal(undefined, ...(ngDevMode ? [{\n    debugName: \"modelValue\"\n  }] : []));\n  $filled = computed(() => isNotEmpty(this.modelValue()), ...(ngDevMode ? [{\n    debugName: \"$filled\"\n  }] : []));\n  writeModelValue(value) {\n    this.modelValue.set(value);\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵBaseModelHolder_BaseFactory;\n    return function BaseModelHolder_Factory(__ngFactoryType__) {\n      return (ɵBaseModelHolder_BaseFactory || (ɵBaseModelHolder_BaseFactory = i0.ɵɵgetInheritedFactory(BaseModelHolder)))(__ngFactoryType__ || BaseModelHolder);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: BaseModelHolder,\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseModelHolder, [{\n    type: Directive,\n    args: [{\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BaseModelHolder };\n"], "mappings": ";;;;;;;;;;;;;;;;;AAIA,IAAM,kBAAN,MAAM,yBAAwB,cAAc;AAAA,EAC1C,aAAa,OAAO,QAAW,GAAI,YAAY,CAAC;AAAA,IAC9C,WAAW;AAAA,EACb,CAAC,IAAI,CAAC,CAAE;AAAA,EACR,UAAU,SAAS,MAAM,EAAW,KAAK,WAAW,CAAC,GAAG,GAAI,YAAY,CAAC;AAAA,IACvE,WAAW;AAAA,EACb,CAAC,IAAI,CAAC,CAAE;AAAA,EACR,gBAAgB,OAAO;AACrB,SAAK,WAAW,IAAI,KAAK;AAAA,EAC3B;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,wBAAwB,mBAAmB;AACzD,cAAQ,iCAAiC,+BAAkC,sBAAsB,gBAAe,IAAI,qBAAqB,gBAAe;AAAA,IAC1J;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}