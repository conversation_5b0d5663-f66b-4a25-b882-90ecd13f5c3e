{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-basecomponent.mjs"], "sourcesContent": ["import { DOCUMENT, isPlatformServer } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, PLATFORM_ID, ElementRef, Injector, ChangeDetectorRef, Renderer2, Input, Directive } from '@angular/core';\nimport { ThemeService, Theme } from '@primeuix/styled';\nimport { uuid, getKeyValue, cn } from '@primeuix/utils';\nimport { BaseStyle, Base } from 'primeng/base';\nimport { PrimeNG } from 'primeng/config';\nclass BaseComponentStyle extends BaseStyle {\n  name = 'common';\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵBaseComponentStyle_BaseFactory;\n    return function BaseComponentStyle_Factory(__ngFactoryType__) {\n      return (ɵBaseComponentStyle_BaseFactory || (ɵBaseComponentStyle_BaseFactory = i0.ɵɵgetInheritedFactory(BaseComponentStyle)))(__ngFactoryType__ || BaseComponentStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BaseComponentStyle,\n    factory: BaseComponentStyle.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseComponentStyle, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass BaseComponent {\n  document = inject(DOCUMENT);\n  platformId = inject(PLATFORM_ID);\n  el = inject(ElementRef);\n  injector = inject(Injector);\n  cd = inject(ChangeDetectorRef);\n  renderer = inject(Renderer2);\n  config = inject(PrimeNG);\n  baseComponentStyle = inject(BaseComponentStyle);\n  baseStyle = inject(BaseStyle);\n  scopedStyleEl;\n  rootEl;\n  dt;\n  get styleOptions() {\n    return {\n      nonce: this.config?.csp().nonce\n    };\n  }\n  get _name() {\n    return this.constructor.name.replace(/^_/, '').toLowerCase();\n  }\n  get componentStyle() {\n    return this['_componentStyle'];\n  }\n  attrSelector = uuid('pc');\n  themeChangeListeners = [];\n  _getHostInstance(instance) {\n    if (instance) {\n      return instance ? this['hostName'] ? instance['name'] === this['hostName'] ? instance : this._getHostInstance(instance.parentInstance) : instance.parentInstance : undefined;\n    }\n  }\n  _getOptionValue(options, key = '', params = {}) {\n    return getKeyValue(options, key, params);\n  }\n  ngOnInit() {\n    if (this.document) {\n      this._loadCoreStyles();\n      this._loadStyles();\n    }\n  }\n  ngAfterViewInit() {\n    this.rootEl = this.el?.nativeElement;\n    if (this.rootEl) {\n      this.rootEl?.setAttribute(this.attrSelector, '');\n    }\n  }\n  ngOnChanges(changes) {\n    if (this.document && !isPlatformServer(this.platformId)) {\n      const {\n        dt\n      } = changes;\n      if (dt && dt.currentValue) {\n        this._loadScopedThemeStyles(dt.currentValue);\n        this._themeChangeListener(() => this._loadScopedThemeStyles(dt.currentValue));\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._unloadScopedThemeStyles();\n    // @ts-ignore\n    this.themeChangeListeners.forEach(callback => ThemeService.off('theme:change', callback));\n  }\n  _loadStyles() {\n    const _load = () => {\n      if (!Base.isStyleNameLoaded('base')) {\n        this.baseStyle.loadGlobalCSS(this.styleOptions);\n        Base.setLoadedStyleName('base');\n      }\n      this._loadThemeStyles();\n      // @todo update config.theme()\n    };\n    _load();\n    this._themeChangeListener(() => _load());\n  }\n  _loadCoreStyles() {\n    if (!Base.isStyleNameLoaded('base') && this.componentStyle?.name) {\n      this.baseComponentStyle.loadCSS(this.styleOptions);\n      this.componentStyle && this.componentStyle?.loadCSS(this.styleOptions);\n      Base.setLoadedStyleName(this.componentStyle?.name);\n    }\n  }\n  _loadThemeStyles() {\n    // common\n    if (!Theme.isStyleNameLoaded('common')) {\n      const {\n        primitive,\n        semantic,\n        global,\n        style\n      } = this.componentStyle?.getCommonTheme?.() || {};\n      this.baseStyle.load(primitive?.css, {\n        name: 'primitive-variables',\n        ...this.styleOptions\n      });\n      this.baseStyle.load(semantic?.css, {\n        name: 'semantic-variables',\n        ...this.styleOptions\n      });\n      this.baseStyle.load(global?.css, {\n        name: 'global-variables',\n        ...this.styleOptions\n      });\n      this.baseStyle.loadGlobalTheme({\n        name: 'global-style',\n        ...this.styleOptions\n      }, style);\n      Theme.setLoadedStyleName('common');\n    }\n    // component\n    if (!Theme.isStyleNameLoaded(this.componentStyle?.name) && this.componentStyle?.name) {\n      const {\n        css,\n        style\n      } = this.componentStyle?.getComponentTheme?.() || {};\n      this.componentStyle?.load(css, {\n        name: `${this.componentStyle?.name}-variables`,\n        ...this.styleOptions\n      });\n      this.componentStyle?.loadTheme({\n        name: `${this.componentStyle?.name}-style`,\n        ...this.styleOptions\n      }, style);\n      Theme.setLoadedStyleName(this.componentStyle?.name);\n    }\n    // layer order\n    if (!Theme.isStyleNameLoaded('layer-order')) {\n      const layerOrder = this.componentStyle?.getLayerOrderThemeCSS?.();\n      this.baseStyle.load(layerOrder, {\n        name: 'layer-order',\n        first: true,\n        ...this.styleOptions\n      });\n      Theme.setLoadedStyleName('layer-order');\n    }\n    if (this.dt) {\n      this._loadScopedThemeStyles(this.dt);\n      this._themeChangeListener(() => this._loadScopedThemeStyles(this.dt));\n    }\n  }\n  _loadScopedThemeStyles(preset) {\n    const {\n      css\n    } = this.componentStyle?.getPresetTheme?.(preset, `[${this.attrSelector}]`) || {};\n    const scopedStyle = this.componentStyle?.load(css, {\n      name: `${this.attrSelector}-${this.componentStyle?.name}`,\n      ...this.styleOptions\n    });\n    this.scopedStyleEl = scopedStyle?.el;\n  }\n  _unloadScopedThemeStyles() {\n    this.scopedStyleEl?.remove();\n  }\n  _themeChangeListener(callback = () => {}) {\n    Base.clearLoadedStyleNames();\n    ThemeService.on('theme:change', callback);\n    this.themeChangeListeners.push(callback);\n  }\n  cx(key, params = {}) {\n    return cn(this._getOptionValue(this.$style?.classes, key, {\n      instance: this,\n      ...params\n    }));\n  }\n  sx(key = '', when = true, params = {}) {\n    if (when) {\n      const self = this._getOptionValue(this.$style?.inlineStyles, key, {\n        instance: this,\n        ...params\n      });\n      //const base = this._getOptionValue(BaseComponentStyle.inlineStyles, key, { ...this.$params, ...params });\n      return self;\n    }\n    return undefined;\n  }\n  get parent() {\n    return this['parentInstance'];\n  }\n  get $style() {\n    return this.parent ? this.parent.componentStyle : this.componentStyle;\n  }\n  cn = cn;\n  static ɵfac = function BaseComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BaseComponent)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: BaseComponent,\n    inputs: {\n      dt: \"dt\"\n    },\n    features: [i0.ɵɵProvidersFeature([BaseComponentStyle, BaseStyle]), i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseComponent, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      providers: [BaseComponentStyle, BaseStyle]\n    }]\n  }], null, {\n    dt: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BaseComponent, BaseComponentStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,qBAAN,MAAM,4BAA2B,UAAU;AAAA,EACzC,OAAO;AAAA,EACP,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,2BAA2B,mBAAmB;AAC5D,cAAQ,oCAAoC,kCAAqC,sBAAsB,mBAAkB,IAAI,qBAAqB,mBAAkB;AAAA,IACtK;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,oBAAmB;AAAA,IAC5B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,WAAW,OAAO,QAAQ;AAAA,EAC1B,aAAa,OAAO,WAAW;AAAA,EAC/B,KAAK,OAAO,UAAU;AAAA,EACtB,WAAW,OAAO,QAAQ;AAAA,EAC1B,KAAK,OAAO,iBAAiB;AAAA,EAC7B,WAAW,OAAO,SAAS;AAAA,EAC3B,SAAS,OAAO,OAAO;AAAA,EACvB,qBAAqB,OAAO,kBAAkB;AAAA,EAC9C,YAAY,OAAO,SAAS;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA,IAAI,eAAe;AACjB,WAAO;AAAA,MACL,OAAO,KAAK,QAAQ,IAAI,EAAE;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK,YAAY,KAAK,QAAQ,MAAM,EAAE,EAAE,YAAY;AAAA,EAC7D;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO,KAAK,iBAAiB;AAAA,EAC/B;AAAA,EACA,eAAe,EAAK,IAAI;AAAA,EACxB,uBAAuB,CAAC;AAAA,EACxB,iBAAiB,UAAU;AACzB,QAAI,UAAU;AACZ,aAAO,WAAW,KAAK,UAAU,IAAI,SAAS,MAAM,MAAM,KAAK,UAAU,IAAI,WAAW,KAAK,iBAAiB,SAAS,cAAc,IAAI,SAAS,iBAAiB;AAAA,IACrK;AAAA,EACF;AAAA,EACA,gBAAgB,SAAS,MAAM,IAAI,SAAS,CAAC,GAAG;AAC9C,WAAO,EAAY,SAAS,KAAK,MAAM;AAAA,EACzC;AAAA,EACA,WAAW;AACT,QAAI,KAAK,UAAU;AACjB,WAAK,gBAAgB;AACrB,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,SAAK,SAAS,KAAK,IAAI;AACvB,QAAI,KAAK,QAAQ;AACf,WAAK,QAAQ,aAAa,KAAK,cAAc,EAAE;AAAA,IACjD;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,KAAK,YAAY,CAAC,iBAAiB,KAAK,UAAU,GAAG;AACvD,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,UAAI,MAAM,GAAG,cAAc;AACzB,aAAK,uBAAuB,GAAG,YAAY;AAC3C,aAAK,qBAAqB,MAAM,KAAK,uBAAuB,GAAG,YAAY,CAAC;AAAA,MAC9E;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,yBAAyB;AAE9B,SAAK,qBAAqB,QAAQ,cAAY,EAAa,IAAI,gBAAgB,QAAQ,CAAC;AAAA,EAC1F;AAAA,EACA,cAAc;AACZ,UAAM,QAAQ,MAAM;AAClB,UAAI,CAAC,KAAK,kBAAkB,MAAM,GAAG;AACnC,aAAK,UAAU,cAAc,KAAK,YAAY;AAC9C,aAAK,mBAAmB,MAAM;AAAA,MAChC;AACA,WAAK,iBAAiB;AAAA,IAExB;AACA,UAAM;AACN,SAAK,qBAAqB,MAAM,MAAM,CAAC;AAAA,EACzC;AAAA,EACA,kBAAkB;AAChB,QAAI,CAAC,KAAK,kBAAkB,MAAM,KAAK,KAAK,gBAAgB,MAAM;AAChE,WAAK,mBAAmB,QAAQ,KAAK,YAAY;AACjD,WAAK,kBAAkB,KAAK,gBAAgB,QAAQ,KAAK,YAAY;AACrE,WAAK,mBAAmB,KAAK,gBAAgB,IAAI;AAAA,IACnD;AAAA,EACF;AAAA,EACA,mBAAmB;AAEjB,QAAI,CAAC,EAAM,kBAAkB,QAAQ,GAAG;AACtC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,KAAK,gBAAgB,iBAAiB,KAAK,CAAC;AAChD,WAAK,UAAU,KAAK,WAAW,KAAK;AAAA,QAClC,MAAM;AAAA,SACH,KAAK,aACT;AACD,WAAK,UAAU,KAAK,UAAU,KAAK;AAAA,QACjC,MAAM;AAAA,SACH,KAAK,aACT;AACD,WAAK,UAAU,KAAK,QAAQ,KAAK;AAAA,QAC/B,MAAM;AAAA,SACH,KAAK,aACT;AACD,WAAK,UAAU,gBAAgB;AAAA,QAC7B,MAAM;AAAA,SACH,KAAK,eACP,KAAK;AACR,QAAM,mBAAmB,QAAQ;AAAA,IACnC;AAEA,QAAI,CAAC,EAAM,kBAAkB,KAAK,gBAAgB,IAAI,KAAK,KAAK,gBAAgB,MAAM;AACpF,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK,gBAAgB,oBAAoB,KAAK,CAAC;AACnD,WAAK,gBAAgB,KAAK,KAAK;AAAA,QAC7B,MAAM,GAAG,KAAK,gBAAgB,IAAI;AAAA,SAC/B,KAAK,aACT;AACD,WAAK,gBAAgB,UAAU;AAAA,QAC7B,MAAM,GAAG,KAAK,gBAAgB,IAAI;AAAA,SAC/B,KAAK,eACP,KAAK;AACR,QAAM,mBAAmB,KAAK,gBAAgB,IAAI;AAAA,IACpD;AAEA,QAAI,CAAC,EAAM,kBAAkB,aAAa,GAAG;AAC3C,YAAM,aAAa,KAAK,gBAAgB,wBAAwB;AAChE,WAAK,UAAU,KAAK,YAAY;AAAA,QAC9B,MAAM;AAAA,QACN,OAAO;AAAA,SACJ,KAAK,aACT;AACD,QAAM,mBAAmB,aAAa;AAAA,IACxC;AACA,QAAI,KAAK,IAAI;AACX,WAAK,uBAAuB,KAAK,EAAE;AACnC,WAAK,qBAAqB,MAAM,KAAK,uBAAuB,KAAK,EAAE,CAAC;AAAA,IACtE;AAAA,EACF;AAAA,EACA,uBAAuB,QAAQ;AAC7B,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,KAAK,gBAAgB,iBAAiB,QAAQ,IAAI,KAAK,YAAY,GAAG,KAAK,CAAC;AAChF,UAAM,cAAc,KAAK,gBAAgB,KAAK,KAAK;AAAA,MACjD,MAAM,GAAG,KAAK,YAAY,IAAI,KAAK,gBAAgB,IAAI;AAAA,OACpD,KAAK,aACT;AACD,SAAK,gBAAgB,aAAa;AAAA,EACpC;AAAA,EACA,2BAA2B;AACzB,SAAK,eAAe,OAAO;AAAA,EAC7B;AAAA,EACA,qBAAqB,WAAW,MAAM;AAAA,EAAC,GAAG;AACxC,SAAK,sBAAsB;AAC3B,MAAa,GAAG,gBAAgB,QAAQ;AACxC,SAAK,qBAAqB,KAAK,QAAQ;AAAA,EACzC;AAAA,EACA,GAAG,KAAK,SAAS,CAAC,GAAG;AACnB,WAAO,EAAG,KAAK,gBAAgB,KAAK,QAAQ,SAAS,KAAK;AAAA,MACxD,UAAU;AAAA,OACP,OACJ,CAAC;AAAA,EACJ;AAAA,EACA,GAAG,MAAM,IAAI,OAAO,MAAM,SAAS,CAAC,GAAG;AACrC,QAAI,MAAM;AACR,YAAM,OAAO,KAAK,gBAAgB,KAAK,QAAQ,cAAc,KAAK;AAAA,QAChE,UAAU;AAAA,SACP,OACJ;AAED,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,gBAAgB;AAAA,EAC9B;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,SAAS,KAAK,OAAO,iBAAiB,KAAK;AAAA,EACzD;AAAA,EACA,KAAK;AAAA,EACL,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,IAAI;AAAA,IACN;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,oBAAoB,SAAS,CAAC,GAAM,oBAAoB;AAAA,EAC5F,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,WAAW,CAAC,oBAAoB,SAAS;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;", "names": []}