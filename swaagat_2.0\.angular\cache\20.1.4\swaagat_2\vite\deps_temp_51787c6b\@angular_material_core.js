import {
  MAT_NATIVE_DATE_FORMATS,
  MatLine,
  MatLineModule,
  MatNativeDateModule,
  NativeDateAdapter,
  NativeDateModule,
  VERSION,
  provideNativeDateAdapter,
  setLines
} from "./chunk-GQ6SNZ46.js";
import {
  DateAdapter,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
  MAT_DATE_LOCALE_FACTORY
} from "./chunk-BLOFK7YY.js";
import {
  MAT_OPTGROUP,
  MAT_OPTION_PARENT_COMPONENT,
  MatOptgroup,
  MatOption,
  MatOptionModule,
  MatOptionSelectionChange,
  MatPseudoCheckbox,
  MatPseudoCheckboxModule,
  _countGroupLabelsBeforeOption,
  _getOptionScrollPosition
} from "./chunk-C2VZO5UO.js";
import {
  ErrorStateMatcher,
  ShowOnDirtyErrorStateMatcher,
  _ErrorStateTracker
} from "./chunk-S5UL7KYN.js";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>oader
} from "./chunk-DJXF35A6.js";
import {
  _MatInternalFormField
} from "./chunk-34HBIZ7U.js";
import {
  MatRippleModule
} from "./chunk-GYI7C4XY.js";
import {
  MAT_RIPPLE_GLOBAL_OPTIONS,
  MatRipple,
  RippleRef,
  RippleRenderer,
  RippleState,
  defaultRippleAnimationConfig
} from "./chunk-NT2QRSC5.js";
import {
  _StructuralStylesLoader
} from "./chunk-W3N3GKGQ.js";
import "./chunk-KRME3YF5.js";
import "./chunk-ME6T2RWV.js";
import {
  AnimationCurves,
  AnimationDurations,
  MATERIAL_ANIMATIONS,
  _animationsDisabled,
  _getAnimationsState
} from "./chunk-AF7BFAZY.js";
import {
  MATERIAL_SANITY_CHECKS,
  MatCommonModule
} from "./chunk-OJ5V74OB.js";
import "./chunk-QCETVJKM.js";
import "./chunk-GWE4MCPP.js";
import "./chunk-C4MWPVUO.js";
import "./chunk-EOFW2REK.js";
import "./chunk-YQ55SDGC.js";
import "./chunk-LHVGIKP3.js";
import "./chunk-PQF5LFR6.js";
import "./chunk-FVRNZMEJ.js";
import "./chunk-NDZIWK7R.js";
import "./chunk-4G4PEPLI.js";
import "./chunk-3KKC7HMJ.js";
import "./chunk-KBUIKKCC.js";
export {
  AnimationCurves,
  AnimationDurations,
  DateAdapter,
  ErrorStateMatcher,
  MATERIAL_ANIMATIONS,
  MATERIAL_SANITY_CHECKS,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
  MAT_DATE_LOCALE_FACTORY,
  MAT_NATIVE_DATE_FORMATS,
  MAT_OPTGROUP,
  MAT_OPTION_PARENT_COMPONENT,
  MAT_RIPPLE_GLOBAL_OPTIONS,
  MatCommonModule,
  MatLine,
  MatLineModule,
  MatNativeDateModule,
  MatOptgroup,
  MatOption,
  MatOptionModule,
  MatOptionSelectionChange,
  MatPseudoCheckbox,
  MatPseudoCheckboxModule,
  MatRipple,
  MatRippleLoader,
  MatRippleModule,
  NativeDateAdapter,
  NativeDateModule,
  RippleRef,
  RippleRenderer,
  RippleState,
  ShowOnDirtyErrorStateMatcher,
  VERSION,
  _ErrorStateTracker,
  _MatInternalFormField,
  _StructuralStylesLoader,
  _animationsDisabled,
  _countGroupLabelsBeforeOption,
  _getAnimationsState,
  _getOptionScrollPosition,
  defaultRippleAnimationConfig,
  provideNativeDateAdapter,
  setLines
};
