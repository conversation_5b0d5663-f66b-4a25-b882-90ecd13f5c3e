* {
  box-sizing: border-box;
}

body {
  font-family: 'Roboto', sans-serif;
  background: #e8ecef;
  margin: 0;
  padding: 20px;
  color: #1a202c;
}

.form-container {
  max-width: 1200px;
  margin: 20px auto;
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

h2 {
  text-align: center;
  color: #003087;
  margin-bottom: 10px;
  font-weight: 700;
  font-size: 24px;
}

.note {
  text-align: right;
  font-size: 14px;
  color: #dc2626;
  margin-bottom: 20px;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px 30px;
}

label {
  font-weight: 500;
  margin-bottom: 8px;
  display: block;
  color: #1a202c;
  font-size: 15px;
}

input,
select,
textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: #f9fafb;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

input:focus,
select:focus,
textarea:focus {
  border-color: #003087;
  box-shadow: 0 0 0 3px rgba(0, 48, 135, 0.1);
  outline: none;
  background: white;
}

/* Updated Dropdown Styling */
select {
  appearance: none; /* Remove default browser styling */
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%231a202c' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 12px;
  padding-right: 30px; /* Space for the custom arrow */
  cursor: pointer;
}

select:hover {
  border-color: #003087;
  background-color: #ffffff;
}

select:focus {
  border-color: #003087;
  box-shadow: 0 0 0 3px rgba(0, 48, 135, 0.1);
  background-color: #ffffff;
}

textarea {
  min-height: 100px;
  resize: vertical;
}

.section-title {
  grid-column: span 2;
  font-size: 18px;
  font-weight: 700;
  margin: 20px 0 10px;
  color: #003087;
  border-left: 5px solid #ffd700;
  padding-left: 12px;
  background: #f8fafc;
  padding: 8px 12px;
}

/* Updated Area of Interest (Radio Group) Styling */
.radio-group {
  display: flex;
  gap: 20px;
  margin-top: 8px;
  background: #f8fafc;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #d1d5db;
}

.radio-group label {
  font-weight: 400;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.radio-group input[type="radio"] {
  appearance: none;
  width: 16px;
  height: 16px;
  border: 2px solid #003087;
  border-radius: 50%;
  background: #ffffff;
  position: relative;
  cursor: pointer;
}

.radio-group input[type="radio"]:checked::after {
  content: '';
  width: 8px;
  height: 8px;
  background: #003087;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.radio-group input[type="radio"]:hover {
  border-color: #002266;
}

.radio-group input[type="radio"]:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 48, 135, 0.1);
}

.form-actions {
  grid-column: span 2;
  text-align: center;
  margin-top: 30px;
}

.btn {
  padding: 12px 30px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-weight: 500;
  font-size: 15px;
  transition: background-color 0.3s ease, transform 0.1s ease;
}

.btn-cancel {
  background: #e5e7eb;
  color: #1a202c;
  margin-right: 15px;
}

.btn-cancel:hover {
  background: #d1d5db;
  transform: translateY(-1px);
}

.btn-submit {
  background: #003087;
  color: white;
}

.btn-submit:hover {
  background: #002266;
  transform: translateY(-1px);
}

.error-message {
  color: #dc2626;
  font-size: 13px;
  margin-top: 4px;
}

.footer {
  background: #003087;
  color: white;
  text-align: center;
  padding: 15px;
  margin-top: 20px;
  font-size: 14px;
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }

  .form-container {
    padding: 20px;
  }

  .radio-group {
    flex-direction: column;
    gap: 10px;
  }
}