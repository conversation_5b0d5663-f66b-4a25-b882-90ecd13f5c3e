/* Modern Data Table Styles */
:root {
  --table-bg: #ffffff;
  --table-text: #1f2937;
  --table-border: #e5e7eb;
  --table-header-bg: #f9fafb;
  --table-header-text: #4b5563;
  --table-row-hover: #f3f4f6;
  --table-row-selected: rgba(59, 130, 246, 0.08);
  --table-row-stripe: #f9fafb;
  --table-empty-text: #9ca3af;
  
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --secondary-color: #6b7280;
  --secondary-hover: #4b5563;
  --danger-color: #ef4444;
  --danger-hover: #dc2626;
  --success-color: #10b981;
  --success-hover: #059669;
  
  --input-bg: #ffffff;
  --input-border: #d1d5db;
  --input-border-focus: #3b82f6;
  --input-shadow-focus: rgba(59, 130, 246, 0.2);
  --input-text: #1f2937;
  --input-placeholder: #9ca3af;
  
  --btn-primary-bg: #3b82f6;
  --btn-primary-text: #ffffff;
  --btn-primary-hover: #2563eb;
  --btn-secondary-bg: #f3f4f6;
  --btn-secondary-text: #4b5563;
  --btn-secondary-border: #d1d5db;
  --btn-secondary-hover: #e5e7eb;
  --btn-danger-bg: #ef4444;
  --btn-danger-text: #ffffff;
  --btn-danger-hover: #dc2626;
  --btn-ghost-bg: transparent;
  --btn-ghost-text: #4b5563;
  --btn-ghost-hover: #f3f4f6;
  --btn-text-color: #3b82f6;
  --btn-text-hover: #2563eb;

  --checkbox-bg: #ffffff;
  --checkbox-border: #d1d5db;
  --checkbox-checked-bg: #3b82f6;
  --checkbox-checked-border: #3b82f6;
  
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  --radius-sm: 0.125rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Container */
.modern-data-table-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  background-color: var(--table-bg);
  box-shadow: var(--shadow-sm);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  
  &.rounded {
    border-radius: var(--radius-lg);
    overflow: hidden;
  }
}

/* Header */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background-color: var(--table-bg);
  border-bottom: 1px solid var(--table-border);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.selection-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--table-header-text);
  font-size: 0.875rem;
}

/* Search */
.search-section {
  position: relative;
  width: 250px;
  transition: width var(--transition-normal);
  
  &.focused {
    width: 300px;
  }
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.25rem;
  height: 1.25rem;
  color: var(--input-placeholder);
  pointer-events: none;
}

.search-input {
  width: 100%;
  padding: 0.65rem 2.5rem 0.65rem 2.5rem;
  background-color: var(--input-bg);
  border: 1px solid var(--input-border);
  border-radius: var(--radius-md);
  color: var(--input-text);
  font-size: 0.9rem;
  transition: all var(--transition-normal);
  
  &:focus {
    outline: none;
    border-color: var(--input-border-focus);
    box-shadow: 0 0 0 3px var(--input-shadow-focus);
  }
  
  &::placeholder {
    color: var(--input-placeholder);
  }
}

.clear-search {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.25rem;
  height: 1.25rem;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: var(--input-placeholder);
  cursor: pointer;
  opacity: 0.7;
  transition: opacity var(--transition-fast);
  
  &:hover {
    opacity: 1;
  }
  
  svg {
    width: 0.875rem;
    height: 0.875rem;
  }
}

/* Table */
.table-wrapper {
  position: relative;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.modern-data-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  color: var(--table-text);
  table-layout: auto;
  
  &.sticky-header thead tr {
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 1px 0 var(--table-border);
  }
  
  &.bordered {
    border: 1px solid var(--table-border);
  }
  
  &.compact th,
  &.compact td {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}

/* Table Head */
.modern-data-table thead {
  background-color: var(--table-header-bg);
}

.modern-data-table th {
  padding: 0.85rem 1rem;
  text-align: left;
  font-weight: 600;
  color: var(--table-header-text);
  white-space: nowrap;
  background-color: var(--table-header-bg);
  border-bottom: 1px solid var(--table-border);
  position: relative;
  transition: background-color var(--transition-fast);
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  
  &.align-center {
    text-align: center;
  }
  
  &.align-right {
    text-align: right;
  }
  
  &.sortable {
    cursor: pointer;
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.025);
    }
  }
}

.column-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sort-indicator {
  display: inline-flex;
  align-items: center;
}

.sort-icon {
  width: 1rem;
  height: 1rem;
  opacity: 0.5;
  transition: opacity var(--transition-fast), transform var(--transition-fast);
  
  &.active {
    opacity: 1;
    color: var(--primary-color);
  }
}

/* Table Body */
.modern-data-table tbody tr {
  transition: all var(--transition-fast);
  
  &:hover {
    background-color: var(--table-row-hover);
  }
  
  &.selected {
    background-color: var(--table-row-selected) !important;
  }
}

.modern-data-table.hover tbody tr:hover {
  background-color: var(--table-row-hover);
}

.modern-data-table.striped tbody tr:nth-child(odd) {
  background-color: var(--table-row-stripe);
}

.modern-data-table td {
  padding: 1rem;
  vertical-align: middle;
  border-bottom: 1px solid var(--table-border);
  transition: all var(--transition-fast);
  
  &.align-center {
    text-align: center;
  }
  
  &.align-right {
    text-align: right;
  }
}

.cell-content {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Checkbox */
.checkbox-wrapper {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  width: 18px;
  height: 18px;
}

.checkbox-wrapper input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.checkbox-custom {
  position: absolute;
  top: 0;
  left: 0;
  width: 18px;
  height: 18px;
  background-color: var(--checkbox-bg);
  border: 2px solid var(--checkbox-border);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.checkbox-wrapper input[type="checkbox"]:checked ~ .checkbox-custom {
  background-color: var(--checkbox-checked-bg);
  border-color: var(--checkbox-checked-border);
}

.checkbox-wrapper input[type="checkbox"]:checked ~ .checkbox-custom::after {
  content: "";
  position: absolute;
  display: block;
  left: 5px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkbox-wrapper input[type="checkbox"]:indeterminate ~ .checkbox-custom {
  background-color: var(--checkbox-checked-bg);
  border-color: var(--checkbox-checked-border);
}

.checkbox-wrapper input[type="checkbox"]:indeterminate ~ .checkbox-custom::after {
  content: "";
  position: absolute;
  display: block;
  left: 3px;
  top: 7px;
  width: 10px;
  height: 2px;
  background: white;
}

.checkbox-wrapper input[type="checkbox"]:focus ~ .checkbox-custom {
  box-shadow: 0 0 0 3px var(--input-shadow-focus);
}

/* Empty State */
.empty-row td {
  padding: 3rem 1rem !important;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: var(--table-empty-text);
}

.empty-icon {
  width: 3rem;
  height: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state p {
  margin: 0;
  font-size: 1rem;
}

/* Actions */
.actions-cell {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: flex-start;
}

.actions-column {
  width: 1%;
  white-space: nowrap;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1;
  border-radius: var(--radius-md);
  border: 1px solid transparent;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.btn-primary {
  background-color: var(--btn-primary-bg);
  color: var(--btn-primary-text);
  
  &:hover:not(:disabled) {
    background-color: var(--btn-primary-hover);
  }
}

.btn-secondary {
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
  border-color: var(--btn-secondary-border);
  
  &:hover:not(:disabled) {
    background-color: var(--btn-secondary-hover);
  }
}

.btn-danger {
  background-color: var(--btn-danger-bg);
  color: var(--btn-danger-text);
  
  &:hover:not(:disabled) {
    background-color: var(--btn-danger-hover);
  }
}

.btn-ghost {
  background-color: var(--btn-ghost-bg);
  color: var(--btn-ghost-text);
  
  &:hover:not(:disabled) {
    background-color: var(--btn-ghost-hover);
  }
}

.btn-text {
  background-color: transparent;
  color: var(--btn-text-color);
  border: none;
  padding: 0.25rem 0.5rem;
  
  &:hover:not(:disabled) {
    color: var(--btn-text-hover);
    background-color: rgba(59, 130, 246, 0.05);
  }
}

.btn-small {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
}

.btn-medium {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.btn-large {
  padding: 0.625rem 1.25rem;
  font-size: 0.925rem;
}

/* Pagination */
.table-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background-color: var(--table-bg);
  border-top: 1px solid var(--table-border);
}

.pagination-info {
  color: var(--table-header-text);
  font-size: 0.875rem;
}

.selection-counter {
  font-weight: 500;
  color: var(--primary-color);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  padding: 0;
  border-radius: var(--radius-md);
  background-color: transparent;
  color: var(--table-text);
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  
  svg {
    width: 1.25rem;
    height: 1.25rem;
  }
  
  &:hover:not(:disabled) {
    background-color: var(--btn-ghost-hover);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.page-number {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2rem;
  height: 2rem;
  padding: 0 0.5rem;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  background-color: transparent;
  color: var(--table-text);
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  
  &:hover:not(.active) {
    background-color: var(--btn-ghost-hover);
  }
  
  &.active {
    background-color: var(--primary-color);
    color: white;
  }
}

.page-ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  color: var(--table-header-text);
}

/* Badges */
.badge-true, .badge-false {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: var(--radius-md);
}

.badge-true {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.badge-false {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

/* Responsive styles */
@media (max-width: 640px) {
  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .search-section, 
  .search-section.focused {
    width: 100%;
  }
  
  .table-footer {
    flex-direction: column;
    gap: 1rem;
  }
  
  .pagination-controls {
    width: 100%;
    justify-content: center;
  }
}