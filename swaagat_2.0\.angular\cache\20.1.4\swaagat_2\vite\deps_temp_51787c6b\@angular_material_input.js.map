{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/input.mjs"], "sourcesContent": ["import { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { Platform, getSupportedInputTypes } from '@angular/cdk/platform';\nimport { AutofillMonitor, TextFieldModule } from '@angular/cdk/text-field';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, NgZone, Renderer2, isSignal, effect, booleanAttribute, Directive, Input, NgModule } from '@angular/core';\nimport { _IdGenerator } from '@angular/cdk/a11y';\nimport { NgControl, Validators, NgForm, FormGroupDirective } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { M as MAT_INPUT_VALUE_ACCESSOR } from './input-value-accessor-D1GvPuqO.mjs';\nimport { h as MAT_FORM_FIELD, k as MatFormFieldControl } from './form-field-D9B5IUZf.mjs';\nexport { b as <PERSON><PERSON><PERSON><PERSON>, j as <PERSON><PERSON><PERSON><PERSON><PERSON>, c as <PERSON><PERSON><PERSON>, M as <PERSON><PERSON><PERSON><PERSON>, e as <PERSON><PERSON>refix, g as <PERSON><PERSON><PERSON><PERSON> } from './form-field-D9B5IUZf.mjs';\nimport { E as ErrorStateMatcher } from './error-options-DCNQlTOA.mjs';\nimport { _ as _ErrorStateTracker } from './error-state-Dtb1IHM-.mjs';\nimport { M as MatFormFieldModule } from './module-Dj5gfeAg.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/bidi';\nimport '@angular/common';\nimport 'rxjs/operators';\nimport '@angular/cdk/observers/private';\nimport './animation-ChQ1vjiF.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/observers';\n\n/** @docs-private */\nfunction getMatInputUnsupportedTypeError(type) {\n  return Error(`Input type \"${type}\" isn't supported by matInput.`);\n}\n\n// Invalid input type. Using one of these will throw an MatInputUnsupportedTypeError.\nconst MAT_INPUT_INVALID_TYPES = ['button', 'checkbox', 'file', 'hidden', 'image', 'radio', 'range', 'reset', 'submit'];\n/** Injection token that can be used to provide the default options for the input. */\nconst MAT_INPUT_CONFIG = new InjectionToken('MAT_INPUT_CONFIG');\nclass MatInput {\n  _elementRef = inject(ElementRef);\n  _platform = inject(Platform);\n  ngControl = inject(NgControl, {\n    optional: true,\n    self: true\n  });\n  _autofillMonitor = inject(AutofillMonitor);\n  _ngZone = inject(NgZone);\n  _formField = inject(MAT_FORM_FIELD, {\n    optional: true\n  });\n  _renderer = inject(Renderer2);\n  _uid = inject(_IdGenerator).getId('mat-input-');\n  _previousNativeValue;\n  _inputValueAccessor;\n  _signalBasedValueAccessor;\n  _previousPlaceholder;\n  _errorStateTracker;\n  _config = inject(MAT_INPUT_CONFIG, {\n    optional: true\n  });\n  _cleanupIosKeyup;\n  _cleanupWebkitWheel;\n  /** Whether the component is being rendered on the server. */\n  _isServer;\n  /** Whether the component is a native html select. */\n  _isNativeSelect;\n  /** Whether the component is a textarea. */\n  _isTextarea;\n  /** Whether the input is inside of a form field. */\n  _isInFormField;\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  focused = false;\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  stateChanges = new Subject();\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  controlType = 'mat-input';\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  autofilled = false;\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n    // Browsers may not fire the blur event if the input is disabled too quickly.\n    // Reset from here to ensure that the element doesn't become stuck.\n    if (this.focused) {\n      this.focused = false;\n      this.stateChanges.next();\n    }\n  }\n  _disabled = false;\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get id() {\n    return this._id;\n  }\n  set id(value) {\n    this._id = value || this._uid;\n  }\n  _id;\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  placeholder;\n  /**\n   * Name of the input.\n   * @docs-private\n   */\n  name;\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get required() {\n    return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n  }\n  set required(value) {\n    this._required = coerceBooleanProperty(value);\n  }\n  _required;\n  /** Input type of the element. */\n  get type() {\n    return this._type;\n  }\n  set type(value) {\n    this._type = value || 'text';\n    this._validateType();\n    // When using Angular inputs, developers are no longer able to set the properties on the native\n    // input element. To ensure that bindings for `type` work, we need to sync the setter\n    // with the native property. Textarea elements don't support the type property or attribute.\n    if (!this._isTextarea && getSupportedInputTypes().has(this._type)) {\n      this._elementRef.nativeElement.type = this._type;\n    }\n  }\n  _type = 'text';\n  /** An object used to control when error messages are shown. */\n  get errorStateMatcher() {\n    return this._errorStateTracker.matcher;\n  }\n  set errorStateMatcher(value) {\n    this._errorStateTracker.matcher = value;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  userAriaDescribedBy;\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get value() {\n    return this._signalBasedValueAccessor ? this._signalBasedValueAccessor.value() : this._inputValueAccessor.value;\n  }\n  set value(value) {\n    if (value !== this.value) {\n      if (this._signalBasedValueAccessor) {\n        this._signalBasedValueAccessor.value.set(value);\n      } else {\n        this._inputValueAccessor.value = value;\n      }\n      this.stateChanges.next();\n    }\n  }\n  /** Whether the element is readonly. */\n  get readonly() {\n    return this._readonly;\n  }\n  set readonly(value) {\n    this._readonly = coerceBooleanProperty(value);\n  }\n  _readonly = false;\n  /** Whether the input should remain interactive when it is disabled. */\n  disabledInteractive;\n  /** Whether the input is in an error state. */\n  get errorState() {\n    return this._errorStateTracker.errorState;\n  }\n  set errorState(value) {\n    this._errorStateTracker.errorState = value;\n  }\n  _neverEmptyInputTypes = ['date', 'datetime', 'datetime-local', 'month', 'time', 'week'].filter(t => getSupportedInputTypes().has(t));\n  constructor() {\n    const parentForm = inject(NgForm, {\n      optional: true\n    });\n    const parentFormGroup = inject(FormGroupDirective, {\n      optional: true\n    });\n    const defaultErrorStateMatcher = inject(ErrorStateMatcher);\n    const accessor = inject(MAT_INPUT_VALUE_ACCESSOR, {\n      optional: true,\n      self: true\n    });\n    const element = this._elementRef.nativeElement;\n    const nodeName = element.nodeName.toLowerCase();\n    if (accessor) {\n      if (isSignal(accessor.value)) {\n        this._signalBasedValueAccessor = accessor;\n      } else {\n        this._inputValueAccessor = accessor;\n      }\n    } else {\n      // If no input value accessor was explicitly specified, use the element as the input value\n      // accessor.\n      this._inputValueAccessor = element;\n    }\n    this._previousNativeValue = this.value;\n    // Force setter to be called in case id was not specified.\n    this.id = this.id;\n    // On some versions of iOS the caret gets stuck in the wrong place when holding down the delete\n    // key. In order to get around this we need to \"jiggle\" the caret loose. Since this bug only\n    // exists on iOS, we only bother to install the listener on iOS.\n    if (this._platform.IOS) {\n      this._ngZone.runOutsideAngular(() => {\n        this._cleanupIosKeyup = this._renderer.listen(element, 'keyup', this._iOSKeyupListener);\n      });\n    }\n    this._errorStateTracker = new _ErrorStateTracker(defaultErrorStateMatcher, this.ngControl, parentFormGroup, parentForm, this.stateChanges);\n    this._isServer = !this._platform.isBrowser;\n    this._isNativeSelect = nodeName === 'select';\n    this._isTextarea = nodeName === 'textarea';\n    this._isInFormField = !!this._formField;\n    this.disabledInteractive = this._config?.disabledInteractive || false;\n    if (this._isNativeSelect) {\n      this.controlType = element.multiple ? 'mat-native-select-multiple' : 'mat-native-select';\n    }\n    if (this._signalBasedValueAccessor) {\n      effect(() => {\n        // Read the value so the effect can register the dependency.\n        this._signalBasedValueAccessor.value();\n        this.stateChanges.next();\n      });\n    }\n  }\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      this._autofillMonitor.monitor(this._elementRef.nativeElement).subscribe(event => {\n        this.autofilled = event.isAutofilled;\n        this.stateChanges.next();\n      });\n    }\n  }\n  ngOnChanges() {\n    this.stateChanges.next();\n  }\n  ngOnDestroy() {\n    this.stateChanges.complete();\n    if (this._platform.isBrowser) {\n      this._autofillMonitor.stopMonitoring(this._elementRef.nativeElement);\n    }\n    this._cleanupIosKeyup?.();\n    this._cleanupWebkitWheel?.();\n  }\n  ngDoCheck() {\n    if (this.ngControl) {\n      // We need to re-evaluate this on every change detection cycle, because there are some\n      // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n      // that whatever logic is in here has to be super lean or we risk destroying the performance.\n      this.updateErrorState();\n      // Since the input isn't a `ControlValueAccessor`, we don't have a good way of knowing when\n      // the disabled state has changed. We can't use the `ngControl.statusChanges`, because it\n      // won't fire if the input is disabled with `emitEvents = false`, despite the input becoming\n      // disabled.\n      if (this.ngControl.disabled !== null && this.ngControl.disabled !== this.disabled) {\n        this.disabled = this.ngControl.disabled;\n        this.stateChanges.next();\n      }\n    }\n    // We need to dirty-check the native element's value, because there are some cases where\n    // we won't be notified when it changes (e.g. the consumer isn't using forms or they're\n    // updating the value using `emitEvent: false`).\n    this._dirtyCheckNativeValue();\n    // We need to dirty-check and set the placeholder attribute ourselves, because whether it's\n    // present or not depends on a query which is prone to \"changed after checked\" errors.\n    this._dirtyCheckPlaceholder();\n  }\n  /** Focuses the input. */\n  focus(options) {\n    this._elementRef.nativeElement.focus(options);\n  }\n  /** Refreshes the error state of the input. */\n  updateErrorState() {\n    this._errorStateTracker.updateErrorState();\n  }\n  /** Callback for the cases where the focused state of the input changes. */\n  _focusChanged(isFocused) {\n    if (isFocused === this.focused) {\n      return;\n    }\n    if (!this._isNativeSelect && isFocused && this.disabled && this.disabledInteractive) {\n      const element = this._elementRef.nativeElement;\n      // Focusing an input that has text will cause all the text to be selected. Clear it since\n      // the user won't be able to change it. This is based on the internal implementation.\n      if (element.type === 'number') {\n        // setSelectionRange doesn't work on number inputs so it needs to be set briefly to text.\n        element.type = 'text';\n        element.setSelectionRange(0, 0);\n        element.type = 'number';\n      } else {\n        element.setSelectionRange(0, 0);\n      }\n    }\n    this.focused = isFocused;\n    this.stateChanges.next();\n  }\n  _onInput() {\n    // This is a noop function and is used to let Angular know whenever the value changes.\n    // Angular will run a new change detection each time the `input` event has been dispatched.\n    // It's necessary that Angular recognizes the value change, because when floatingLabel\n    // is set to false and Angular forms aren't used, the placeholder won't recognize the\n    // value changes and will not disappear.\n    // Listening to the input event wouldn't be necessary when the input is using the\n    // FormsModule or ReactiveFormsModule, because Angular forms also listens to input events.\n  }\n  /** Does some manual dirty checking on the native input `value` property. */\n  _dirtyCheckNativeValue() {\n    const newValue = this._elementRef.nativeElement.value;\n    if (this._previousNativeValue !== newValue) {\n      this._previousNativeValue = newValue;\n      this.stateChanges.next();\n    }\n  }\n  /** Does some manual dirty checking on the native input `placeholder` attribute. */\n  _dirtyCheckPlaceholder() {\n    const placeholder = this._getPlaceholder();\n    if (placeholder !== this._previousPlaceholder) {\n      const element = this._elementRef.nativeElement;\n      this._previousPlaceholder = placeholder;\n      placeholder ? element.setAttribute('placeholder', placeholder) : element.removeAttribute('placeholder');\n    }\n  }\n  /** Gets the current placeholder of the form field. */\n  _getPlaceholder() {\n    return this.placeholder || null;\n  }\n  /** Make sure the input is a supported type. */\n  _validateType() {\n    if (MAT_INPUT_INVALID_TYPES.indexOf(this._type) > -1 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatInputUnsupportedTypeError(this._type);\n    }\n  }\n  /** Checks whether the input type is one of the types that are never empty. */\n  _isNeverEmpty() {\n    return this._neverEmptyInputTypes.indexOf(this._type) > -1;\n  }\n  /** Checks whether the input is invalid based on the native validation. */\n  _isBadInput() {\n    // The `validity` property won't be present on platform-server.\n    let validity = this._elementRef.nativeElement.validity;\n    return validity && validity.badInput;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get empty() {\n    return !this._isNeverEmpty() && !this._elementRef.nativeElement.value && !this._isBadInput() && !this.autofilled;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get shouldLabelFloat() {\n    if (this._isNativeSelect) {\n      // For a single-selection `<select>`, the label should float when the selected option has\n      // a non-empty display value. For a `<select multiple>`, the label *always* floats to avoid\n      // overlapping the label with the options.\n      const selectElement = this._elementRef.nativeElement;\n      const firstOption = selectElement.options[0];\n      // On most browsers the `selectedIndex` will always be 0, however on IE and Edge it'll be\n      // -1 if the `value` is set to something, that isn't in the list of options, at a later point.\n      return this.focused || selectElement.multiple || !this.empty || !!(selectElement.selectedIndex > -1 && firstOption && firstOption.label);\n    } else {\n      return this.focused && !this.disabled || !this.empty;\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get describedByIds() {\n    const element = this._elementRef.nativeElement;\n    const existingDescribedBy = element.getAttribute('aria-describedby');\n    return existingDescribedBy?.split(' ') || [];\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  setDescribedByIds(ids) {\n    const element = this._elementRef.nativeElement;\n    if (ids.length) {\n      element.setAttribute('aria-describedby', ids.join(' '));\n    } else {\n      element.removeAttribute('aria-describedby');\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  onContainerClick() {\n    // Do not re-focus the input element if the element is already focused. Otherwise it can happen\n    // that someone clicks on a time input and the cursor resets to the \"hours\" field while the\n    // \"minutes\" field was actually clicked. See: https://github.com/angular/components/issues/12849\n    if (!this.focused) {\n      this.focus();\n    }\n  }\n  /** Whether the form control is a native select that is displayed inline. */\n  _isInlineSelect() {\n    const element = this._elementRef.nativeElement;\n    return this._isNativeSelect && (element.multiple || element.size > 1);\n  }\n  _iOSKeyupListener = event => {\n    const el = event.target;\n    // Note: We specifically check for 0, rather than `!el.selectionStart`, because the two\n    // indicate different things. If the value is 0, it means that the caret is at the start\n    // of the input, whereas a value of `null` means that the input doesn't support\n    // manipulating the selection range. Inputs that don't support setting the selection range\n    // will throw an error so we want to avoid calling `setSelectionRange` on them. See:\n    // https://html.spec.whatwg.org/multipage/input.html#do-not-apply\n    if (!el.value && el.selectionStart === 0 && el.selectionEnd === 0) {\n      // Note: Just setting `0, 0` doesn't fix the issue. Setting\n      // `1, 1` fixes it for the first time that you type text and\n      // then hold delete. Toggling to `1, 1` and then back to\n      // `0, 0` seems to completely fix it.\n      el.setSelectionRange(1, 1);\n      el.setSelectionRange(0, 0);\n    }\n  };\n  /** Gets the value to set on the `readonly` attribute. */\n  _getReadonlyAttribute() {\n    if (this._isNativeSelect) {\n      return null;\n    }\n    if (this.readonly || this.disabled && this.disabledInteractive) {\n      return 'true';\n    }\n    return null;\n  }\n  static ɵfac = function MatInput_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatInput)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatInput,\n    selectors: [[\"input\", \"matInput\", \"\"], [\"textarea\", \"matInput\", \"\"], [\"select\", \"matNativeControl\", \"\"], [\"input\", \"matNativeControl\", \"\"], [\"textarea\", \"matNativeControl\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-input-element\"],\n    hostVars: 21,\n    hostBindings: function MatInput_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"focus\", function MatInput_focus_HostBindingHandler() {\n          return ctx._focusChanged(true);\n        })(\"blur\", function MatInput_blur_HostBindingHandler() {\n          return ctx._focusChanged(false);\n        })(\"input\", function MatInput_input_HostBindingHandler() {\n          return ctx._onInput();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵdomProperty(\"id\", ctx.id)(\"disabled\", ctx.disabled && !ctx.disabledInteractive)(\"required\", ctx.required);\n        i0.ɵɵattribute(\"name\", ctx.name || null)(\"readonly\", ctx._getReadonlyAttribute())(\"aria-disabled\", ctx.disabled && ctx.disabledInteractive ? \"true\" : null)(\"aria-invalid\", ctx.empty && ctx.required ? null : ctx.errorState)(\"aria-required\", ctx.required)(\"id\", ctx.id);\n        i0.ɵɵclassProp(\"mat-input-server\", ctx._isServer)(\"mat-mdc-form-field-textarea-control\", ctx._isInFormField && ctx._isTextarea)(\"mat-mdc-form-field-input-control\", ctx._isInFormField)(\"mat-mdc-input-disabled-interactive\", ctx.disabledInteractive)(\"mdc-text-field__input\", ctx._isInFormField)(\"mat-mdc-native-select-inline\", ctx._isInlineSelect());\n      }\n    },\n    inputs: {\n      disabled: \"disabled\",\n      id: \"id\",\n      placeholder: \"placeholder\",\n      name: \"name\",\n      required: \"required\",\n      type: \"type\",\n      errorStateMatcher: \"errorStateMatcher\",\n      userAriaDescribedBy: [0, \"aria-describedby\", \"userAriaDescribedBy\"],\n      value: \"value\",\n      readonly: \"readonly\",\n      disabledInteractive: [2, \"disabledInteractive\", \"disabledInteractive\", booleanAttribute]\n    },\n    exportAs: [\"matInput\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MatFormFieldControl,\n      useExisting: MatInput\n    }]), i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatInput, [{\n    type: Directive,\n    args: [{\n      selector: `input[matInput], textarea[matInput], select[matNativeControl],\n      input[matNativeControl], textarea[matNativeControl]`,\n      exportAs: 'matInput',\n      host: {\n        'class': 'mat-mdc-input-element',\n        // The BaseMatInput parent class adds `mat-input-element`, `mat-form-field-control` and\n        // `mat-form-field-autofill-control` to the CSS class list, but this should not be added for\n        // this MDC equivalent input.\n        '[class.mat-input-server]': '_isServer',\n        '[class.mat-mdc-form-field-textarea-control]': '_isInFormField && _isTextarea',\n        '[class.mat-mdc-form-field-input-control]': '_isInFormField',\n        '[class.mat-mdc-input-disabled-interactive]': 'disabledInteractive',\n        '[class.mdc-text-field__input]': '_isInFormField',\n        '[class.mat-mdc-native-select-inline]': '_isInlineSelect()',\n        // Native input properties that are overwritten by Angular inputs need to be synced with\n        // the native input element. Otherwise property bindings for those don't work.\n        '[id]': 'id',\n        '[disabled]': 'disabled && !disabledInteractive',\n        '[required]': 'required',\n        '[attr.name]': 'name || null',\n        '[attr.readonly]': '_getReadonlyAttribute()',\n        '[attr.aria-disabled]': 'disabled && disabledInteractive ? \"true\" : null',\n        // Only mark the input as invalid for assistive technology if it has a value since the\n        // state usually overlaps with `aria-required` when the input is empty and can be redundant.\n        '[attr.aria-invalid]': '(empty && required) ? null : errorState',\n        '[attr.aria-required]': 'required',\n        // Native input properties that are overwritten by Angular inputs need to be synced with\n        // the native input element. Otherwise property bindings for those don't work.\n        '[attr.id]': 'id',\n        '(focus)': '_focusChanged(true)',\n        '(blur)': '_focusChanged(false)',\n        '(input)': '_onInput()'\n      },\n      providers: [{\n        provide: MatFormFieldControl,\n        useExisting: MatInput\n      }]\n    }]\n  }], () => [], {\n    disabled: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    required: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    errorStateMatcher: [{\n      type: Input\n    }],\n    userAriaDescribedBy: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    value: [{\n      type: Input\n    }],\n    readonly: [{\n      type: Input\n    }],\n    disabledInteractive: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass MatInputModule {\n  static ɵfac = function MatInputModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatInputModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatInputModule,\n    imports: [MatCommonModule, MatFormFieldModule, MatInput],\n    exports: [MatInput, MatFormFieldModule, TextFieldModule, MatCommonModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, MatFormFieldModule, MatFormFieldModule, TextFieldModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatInputModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatFormFieldModule, MatInput],\n      exports: [MatInput, MatFormFieldModule, TextFieldModule, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MAT_INPUT_CONFIG, MAT_INPUT_VALUE_ACCESSOR, MatInput, MatInputModule, getMatInputUnsupportedTypeError };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,SAAS,gCAAgC,MAAM;AAC7C,SAAO,MAAM,eAAe,IAAI,gCAAgC;AAClE;AAGA,IAAM,0BAA0B,CAAC,UAAU,YAAY,QAAQ,UAAU,SAAS,SAAS,SAAS,SAAS,QAAQ;AAErH,IAAM,mBAAmB,IAAI,eAAe,kBAAkB;AAC9D,IAAM,WAAN,MAAM,UAAS;AAAA,EACb,cAAc,OAAO,UAAU;AAAA,EAC/B,YAAY,OAAO,QAAQ;AAAA,EAC3B,YAAY,OAAO,WAAW;AAAA,IAC5B,UAAU;AAAA,IACV,MAAM;AAAA,EACR,CAAC;AAAA,EACD,mBAAmB,OAAO,eAAe;AAAA,EACzC,UAAU,OAAO,MAAM;AAAA,EACvB,aAAa,OAAO,gBAAgB;AAAA,IAClC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,OAAO,SAAS;AAAA,EAC5B,OAAO,OAAO,YAAY,EAAE,MAAM,YAAY;AAAA,EAC9C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAU,OAAO,kBAAkB;AAAA,IACjC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKV,eAAe,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3B,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,sBAAsB,KAAK;AAG5C,QAAI,KAAK,SAAS;AAChB,WAAK,UAAU;AACf,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA,EACA,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKZ,IAAI,KAAK;AACP,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,GAAG,OAAO;AACZ,SAAK,MAAM,SAAS,KAAK;AAAA,EAC3B;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,KAAK,WAAW,SAAS,aAAa,WAAW,QAAQ,KAAK;AAAA,EACzF;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,sBAAsB,KAAK;AAAA,EAC9C;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,OAAO;AACd,SAAK,QAAQ,SAAS;AACtB,SAAK,cAAc;AAInB,QAAI,CAAC,KAAK,eAAe,uBAAuB,EAAE,IAAI,KAAK,KAAK,GAAG;AACjE,WAAK,YAAY,cAAc,OAAO,KAAK;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,QAAQ;AAAA;AAAA,EAER,IAAI,oBAAoB;AACtB,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA,EACA,IAAI,kBAAkB,OAAO;AAC3B,SAAK,mBAAmB,UAAU;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,QAAQ;AACV,WAAO,KAAK,4BAA4B,KAAK,0BAA0B,MAAM,IAAI,KAAK,oBAAoB;AAAA,EAC5G;AAAA,EACA,IAAI,MAAM,OAAO;AACf,QAAI,UAAU,KAAK,OAAO;AACxB,UAAI,KAAK,2BAA2B;AAClC,aAAK,0BAA0B,MAAM,IAAI,KAAK;AAAA,MAChD,OAAO;AACL,aAAK,oBAAoB,QAAQ;AAAA,MACnC;AACA,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,sBAAsB,KAAK;AAAA,EAC9C;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA,EACA,wBAAwB,CAAC,QAAQ,YAAY,kBAAkB,SAAS,QAAQ,MAAM,EAAE,OAAO,OAAK,uBAAuB,EAAE,IAAI,CAAC,CAAC;AAAA,EACnI,cAAc;AACZ,UAAM,aAAa,OAAO,QAAQ;AAAA,MAChC,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,kBAAkB,OAAO,oBAAoB;AAAA,MACjD,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,2BAA2B,OAAO,iBAAiB;AACzD,UAAM,WAAW,OAAO,0BAA0B;AAAA,MAChD,UAAU;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AACD,UAAM,UAAU,KAAK,YAAY;AACjC,UAAM,WAAW,QAAQ,SAAS,YAAY;AAC9C,QAAI,UAAU;AACZ,UAAI,SAAS,SAAS,KAAK,GAAG;AAC5B,aAAK,4BAA4B;AAAA,MACnC,OAAO;AACL,aAAK,sBAAsB;AAAA,MAC7B;AAAA,IACF,OAAO;AAGL,WAAK,sBAAsB;AAAA,IAC7B;AACA,SAAK,uBAAuB,KAAK;AAEjC,SAAK,KAAK,KAAK;AAIf,QAAI,KAAK,UAAU,KAAK;AACtB,WAAK,QAAQ,kBAAkB,MAAM;AACnC,aAAK,mBAAmB,KAAK,UAAU,OAAO,SAAS,SAAS,KAAK,iBAAiB;AAAA,MACxF,CAAC;AAAA,IACH;AACA,SAAK,qBAAqB,IAAI,mBAAmB,0BAA0B,KAAK,WAAW,iBAAiB,YAAY,KAAK,YAAY;AACzI,SAAK,YAAY,CAAC,KAAK,UAAU;AACjC,SAAK,kBAAkB,aAAa;AACpC,SAAK,cAAc,aAAa;AAChC,SAAK,iBAAiB,CAAC,CAAC,KAAK;AAC7B,SAAK,sBAAsB,KAAK,SAAS,uBAAuB;AAChE,QAAI,KAAK,iBAAiB;AACxB,WAAK,cAAc,QAAQ,WAAW,+BAA+B;AAAA,IACvE;AACA,QAAI,KAAK,2BAA2B;AAClC,aAAO,MAAM;AAEX,aAAK,0BAA0B,MAAM;AACrC,aAAK,aAAa,KAAK;AAAA,MACzB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,UAAU,WAAW;AAC5B,WAAK,iBAAiB,QAAQ,KAAK,YAAY,aAAa,EAAE,UAAU,WAAS;AAC/E,aAAK,aAAa,MAAM;AACxB,aAAK,aAAa,KAAK;AAAA,MACzB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,SAAS;AAC3B,QAAI,KAAK,UAAU,WAAW;AAC5B,WAAK,iBAAiB,eAAe,KAAK,YAAY,aAAa;AAAA,IACrE;AACA,SAAK,mBAAmB;AACxB,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,YAAY;AACV,QAAI,KAAK,WAAW;AAIlB,WAAK,iBAAiB;AAKtB,UAAI,KAAK,UAAU,aAAa,QAAQ,KAAK,UAAU,aAAa,KAAK,UAAU;AACjF,aAAK,WAAW,KAAK,UAAU;AAC/B,aAAK,aAAa,KAAK;AAAA,MACzB;AAAA,IACF;AAIA,SAAK,uBAAuB;AAG5B,SAAK,uBAAuB;AAAA,EAC9B;AAAA;AAAA,EAEA,MAAM,SAAS;AACb,SAAK,YAAY,cAAc,MAAM,OAAO;AAAA,EAC9C;AAAA;AAAA,EAEA,mBAAmB;AACjB,SAAK,mBAAmB,iBAAiB;AAAA,EAC3C;AAAA;AAAA,EAEA,cAAc,WAAW;AACvB,QAAI,cAAc,KAAK,SAAS;AAC9B;AAAA,IACF;AACA,QAAI,CAAC,KAAK,mBAAmB,aAAa,KAAK,YAAY,KAAK,qBAAqB;AACnF,YAAM,UAAU,KAAK,YAAY;AAGjC,UAAI,QAAQ,SAAS,UAAU;AAE7B,gBAAQ,OAAO;AACf,gBAAQ,kBAAkB,GAAG,CAAC;AAC9B,gBAAQ,OAAO;AAAA,MACjB,OAAO;AACL,gBAAQ,kBAAkB,GAAG,CAAC;AAAA,MAChC;AAAA,IACF;AACA,SAAK,UAAU;AACf,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA,EACA,WAAW;AAAA,EAQX;AAAA;AAAA,EAEA,yBAAyB;AACvB,UAAM,WAAW,KAAK,YAAY,cAAc;AAChD,QAAI,KAAK,yBAAyB,UAAU;AAC1C,WAAK,uBAAuB;AAC5B,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA;AAAA,EAEA,yBAAyB;AACvB,UAAM,cAAc,KAAK,gBAAgB;AACzC,QAAI,gBAAgB,KAAK,sBAAsB;AAC7C,YAAM,UAAU,KAAK,YAAY;AACjC,WAAK,uBAAuB;AAC5B,oBAAc,QAAQ,aAAa,eAAe,WAAW,IAAI,QAAQ,gBAAgB,aAAa;AAAA,IACxG;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB;AAChB,WAAO,KAAK,eAAe;AAAA,EAC7B;AAAA;AAAA,EAEA,gBAAgB;AACd,QAAI,wBAAwB,QAAQ,KAAK,KAAK,IAAI,OAAO,OAAO,cAAc,eAAe,YAAY;AACvG,YAAM,gCAAgC,KAAK,KAAK;AAAA,IAClD;AAAA,EACF;AAAA;AAAA,EAEA,gBAAgB;AACd,WAAO,KAAK,sBAAsB,QAAQ,KAAK,KAAK,IAAI;AAAA,EAC1D;AAAA;AAAA,EAEA,cAAc;AAEZ,QAAI,WAAW,KAAK,YAAY,cAAc;AAC9C,WAAO,YAAY,SAAS;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,QAAQ;AACV,WAAO,CAAC,KAAK,cAAc,KAAK,CAAC,KAAK,YAAY,cAAc,SAAS,CAAC,KAAK,YAAY,KAAK,CAAC,KAAK;AAAA,EACxG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,mBAAmB;AACrB,QAAI,KAAK,iBAAiB;AAIxB,YAAM,gBAAgB,KAAK,YAAY;AACvC,YAAM,cAAc,cAAc,QAAQ,CAAC;AAG3C,aAAO,KAAK,WAAW,cAAc,YAAY,CAAC,KAAK,SAAS,CAAC,EAAE,cAAc,gBAAgB,MAAM,eAAe,YAAY;AAAA,IACpI,OAAO;AACL,aAAO,KAAK,WAAW,CAAC,KAAK,YAAY,CAAC,KAAK;AAAA,IACjD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,iBAAiB;AACnB,UAAM,UAAU,KAAK,YAAY;AACjC,UAAM,sBAAsB,QAAQ,aAAa,kBAAkB;AACnE,WAAO,qBAAqB,MAAM,GAAG,KAAK,CAAC;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,KAAK;AACrB,UAAM,UAAU,KAAK,YAAY;AACjC,QAAI,IAAI,QAAQ;AACd,cAAQ,aAAa,oBAAoB,IAAI,KAAK,GAAG,CAAC;AAAA,IACxD,OAAO;AACL,cAAQ,gBAAgB,kBAAkB;AAAA,IAC5C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AAIjB,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB;AAChB,UAAM,UAAU,KAAK,YAAY;AACjC,WAAO,KAAK,oBAAoB,QAAQ,YAAY,QAAQ,OAAO;AAAA,EACrE;AAAA,EACA,oBAAoB,WAAS;AAC3B,UAAM,KAAK,MAAM;AAOjB,QAAI,CAAC,GAAG,SAAS,GAAG,mBAAmB,KAAK,GAAG,iBAAiB,GAAG;AAKjE,SAAG,kBAAkB,GAAG,CAAC;AACzB,SAAG,kBAAkB,GAAG,CAAC;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA,EAEA,wBAAwB;AACtB,QAAI,KAAK,iBAAiB;AACxB,aAAO;AAAA,IACT;AACA,QAAI,KAAK,YAAY,KAAK,YAAY,KAAK,qBAAqB;AAC9D,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,iBAAiB,mBAAmB;AACzD,WAAO,KAAK,qBAAqB,WAAU;AAAA,EAC7C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,YAAY,EAAE,GAAG,CAAC,YAAY,YAAY,EAAE,GAAG,CAAC,UAAU,oBAAoB,EAAE,GAAG,CAAC,SAAS,oBAAoB,EAAE,GAAG,CAAC,YAAY,oBAAoB,EAAE,CAAC;AAAA,IAChL,WAAW,CAAC,GAAG,uBAAuB;AAAA,IACtC,UAAU;AAAA,IACV,cAAc,SAAS,sBAAsB,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,oCAAoC;AAClE,iBAAO,IAAI,cAAc,IAAI;AAAA,QAC/B,CAAC,EAAE,QAAQ,SAAS,mCAAmC;AACrD,iBAAO,IAAI,cAAc,KAAK;AAAA,QAChC,CAAC,EAAE,SAAS,SAAS,oCAAoC;AACvD,iBAAO,IAAI,SAAS;AAAA,QACtB,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,MAAM,IAAI,EAAE,EAAE,YAAY,IAAI,YAAY,CAAC,IAAI,mBAAmB,EAAE,YAAY,IAAI,QAAQ;AAC7G,QAAG,YAAY,QAAQ,IAAI,QAAQ,IAAI,EAAE,YAAY,IAAI,sBAAsB,CAAC,EAAE,iBAAiB,IAAI,YAAY,IAAI,sBAAsB,SAAS,IAAI,EAAE,gBAAgB,IAAI,SAAS,IAAI,WAAW,OAAO,IAAI,UAAU,EAAE,iBAAiB,IAAI,QAAQ,EAAE,MAAM,IAAI,EAAE;AAC1Q,QAAG,YAAY,oBAAoB,IAAI,SAAS,EAAE,uCAAuC,IAAI,kBAAkB,IAAI,WAAW,EAAE,oCAAoC,IAAI,cAAc,EAAE,sCAAsC,IAAI,mBAAmB,EAAE,yBAAyB,IAAI,cAAc,EAAE,gCAAgC,IAAI,gBAAgB,CAAC;AAAA,MAC3V;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,UAAU;AAAA,MACV,IAAI;AAAA,MACJ,aAAa;AAAA,MACb,MAAM;AAAA,MACN,UAAU;AAAA,MACV,MAAM;AAAA,MACN,mBAAmB;AAAA,MACnB,qBAAqB,CAAC,GAAG,oBAAoB,qBAAqB;AAAA,MAClE,OAAO;AAAA,MACP,UAAU;AAAA,MACV,qBAAqB,CAAC,GAAG,uBAAuB,uBAAuB,gBAAgB;AAAA,IACzF;AAAA,IACA,UAAU,CAAC,UAAU;AAAA,IACrB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,oBAAoB;AAAA,EAC9B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA;AAAA,MAEV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA;AAAA;AAAA;AAAA,QAIT,4BAA4B;AAAA,QAC5B,+CAA+C;AAAA,QAC/C,4CAA4C;AAAA,QAC5C,8CAA8C;AAAA,QAC9C,iCAAiC;AAAA,QACjC,wCAAwC;AAAA;AAAA;AAAA,QAGxC,QAAQ;AAAA,QACR,cAAc;AAAA,QACd,cAAc;AAAA,QACd,eAAe;AAAA,QACf,mBAAmB;AAAA,QACnB,wBAAwB;AAAA;AAAA;AAAA,QAGxB,uBAAuB;AAAA,QACvB,wBAAwB;AAAA;AAAA;AAAA,QAGxB,aAAa;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,MACb;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,oBAAoB,QAAQ;AAAA,IACvD,SAAS,CAAC,UAAU,oBAAoB,iBAAiB,eAAe;AAAA,EAC1E,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,iBAAiB,oBAAoB,oBAAoB,iBAAiB,eAAe;AAAA,EACrG,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,oBAAoB,QAAQ;AAAA,MACvD,SAAS,CAAC,UAAU,oBAAoB,iBAAiB,eAAe;AAAA,IAC1E,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}