.admin-services-container {
    padding: 2rem;
    background: #f4f6f9;
    min-height: 100vh;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;

    h1 {
        margin: 0;
        font-size: 1.8rem;
        font-weight: 600;
        color: #333;
    }

    button {
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 6px;

        mat-icon {
            font-size: 20px;
        }
    }
}

.custom-table {
    width: 100%;
    border-radius: 12px;
    overflow: hidden;

    th,
    td {
        padding: 12px;
        text-align: left;
        vertical-align: middle;
    }

    th:first-child,
    td:first-child {
        width: 50px;
        text-align: center;
        font-weight: 600;
    }

    th {
        background: #1976d2;
        color: white;
        font-weight: 500;
        font-size: 0.95rem;
        padding: 12px;
    }

    td {
        padding: 12px;
        font-size: 0.9rem;
        color: #444;
    }

    .service-icon {
        font-size: 20px;
        margin-right: 6px;
        color: #1976d2;
        vertical-align: middle;
    }

    .actions-cell {
        display: flex;
        gap: 8px;
        justify-content: center;
        min-width: 180px;

        button {
            background: #f8f9fa;
            border-radius: 8px;

            mat-icon {
                font-size: 20px;
            }

            &:hover {
                background: #e3f2fd;
            }
        }
    }

    th.mat-header-cell:nth-child(7),
    td.questionnaire-cell {
        max-width: 320px;
        text-align: center;
    }

    .questionnaire-cell {
        white-space: nowrap;
    }

    .center-header {
        text-align: center !important;
        vertical-align: middle !important;
    }

    .center-cell {
        text-align: center !important;
        vertical-align: middle !important;
    }

    .questionnaire-cell button {
        margin: 0 4px;
        border-radius: 50%;
        transition: background 0.2s ease, transform 0.15s ease;

        mat-icon {
            font-size: 22px;
        }

        &:hover {
            transform: scale(1.1);
        }
    }

    .questionnaire-cell button[color="primary"] {
        background: #e3f2fd;
    }

    .questionnaire-cell button[color="accent"] {
        background: #f3e5f5;
    }

    .questionnaire-cell button[color="warn"] {
        background: #ffebee;
    }

    .row-hover {
        transition: background 0.3s ease;

        &:hover {
            background: #e3f2fd;
        }
    }
}

.submit-btn {
    background-color: #1976d2 !important;
    color: #fff !important;
}