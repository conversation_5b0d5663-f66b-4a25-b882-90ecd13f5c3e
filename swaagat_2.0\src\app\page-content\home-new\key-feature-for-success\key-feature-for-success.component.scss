@import url('https://fonts.googleapis.com/css2?family=Merriweather:wght@700&family=Open+Sans:wght@400;600&display=swap');

body {
  margin: 0;
  font-family: 'Open Sans', sans-serif;
  overflow-x: hidden;
}

.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.from-gray-50 {
  --tw-gradient-from: #f9fafb;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(249, 250, 251, 0));
}

.via-white {
  --tw-gradient-stops: var(--tw-gradient-from), #ffffff, var(--tw-gradient-to, rgba(255, 255, 255, 0));
}

.to-blue-50 {
  --tw-gradient-to: #eff6ff;
}

.relative {
  position: relative;
}

.overflow-hidden {
  overflow: hidden;
}

.absolute {
  position: absolute;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.-top-40 {
  top: -10rem;
}

.-right-40 {
  right: -10rem;
}

.w-80 {
  width: 20rem;
}

.h-80 {
  height: 20rem;
}

.from-orange-200\/30 {
  --tw-gradient-from: rgba(254, 215, 170, 0.3);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(254, 215, 170, 0));
}

.to-red-200\/30 {
  --tw-gradient-to: rgba(254, 202, 202, 0.3);
}

.rounded-full {
  border-radius: 9999px;
}

.blur-3xl {
  filter: blur(64px);
}

.-bottom-40 {
  bottom: -10rem;
}

.-left-40 {
  left: -10rem;
}

.from-blue-200\/30 {
  --tw-gradient-from: rgba(191, 219, 254, 0.3);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(191, 219, 254, 0));
}

.to-green-200\/30 {
  --tw-gradient-to: rgba(187, 247, 208, 0.3);
}

.max-w-7xl {
  max-width: 80rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.sm\:px-6 {
  @media (min-width: 640px) {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

.lg\:px-8 {
  @media (min-width: 1024px) {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.z-10 {
  z-index: 10;
}

.text-center {
  text-align: center;
}

.mb-16 {
  margin-bottom: 4rem;
}

.inline-flex {
  display: inline-flex;
}

.items-center {
  align-items: center;
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  margin-left: calc(0.5rem * var(--tw-space-x-reverse));
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-orange-100 {
  --tw-gradient-from: #ffedd5;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(255, 237, 213, 0));
}

.to-blue-100 {
  --tw-gradient-to: #dbeafe;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.text-orange-500 {
  color: #f97316;
}

.text-gray-700 {
  color: #374151;
}

.font-medium {
  font-weight: 500;
}

.font-merriweather {
  font-family: 'Merriweather', serif;
}

.font-bold {
  font-weight: 700;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.sm\:text-4xl {
  @media (min-width: 640px) {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

.text-gray-900 {
  color: #111827;
}

.mb-4 {
  margin-bottom: 1rem;
}

.font-open-sans {
  font-family: 'Open Sans', sans-serif;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-gray-600 {
  color: #4b5563;
}

.max-w-3xl {
  max-width: 48rem;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.md\:grid-cols-3 {
  @media (min-width: 768px) {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

.gap-8 {
  gap: 2rem;
}

.group {
  position: relative;
}

.bg-white {
  background-color: #ffffff;
}

.rounded-3xl {
  border-radius: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.hover\:shadow-2xl:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.duration-500 {
  transition-duration: 500ms;
}

.border {
  border-width: 1px;
}

.border-gray-100 {
  border-color: #f3f4f6;
}

.from-orange-500 {
  --tw-gradient-from: #f97316;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(249, 115, 22, 0));
}

.to-red-500 {
  --tw-gradient-to: #ef4444;
}

.from-orange-500\/20 {
  --tw-gradient-from: rgba(249, 115, 22, 0.2);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(249, 115, 22, 0));
}

.to-red-500\/20 {
  --tw-gradient-to: rgba(239, 68, 68, 0.2);
}

.from-green-500 {
  --tw-gradient-from: #22c55e;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(34, 197, 94, 0));
}

.to-emerald-500 {
  --tw-gradient-to: #10b981;
}

.from-green-500\/20 {
  --tw-gradient-from: rgba(34, 197, 94, 0.2);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(34, 197, 94, 0));
}

.to-emerald-500\/20 {
  --tw-gradient-to: rgba(16, 185, 129, 0.2);
}

.from-blue-500 {
  --tw-gradient-from: #3b82f6;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(59, 130, 246, 0));
}

.to-cyan-500 {
  --tw-gradient-to: #06b6d4;
}

.from-blue-500\/20 {
  --tw-gradient-from: rgba(59, 130, 246, 0.2);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(59, 130, 246, 0));
}

.to-cyan-500\/20 {
  --tw-gradient-to: rgba(6, 182, 212, 0.2);
}

.opacity-0 {
  opacity: 0;
}

.group-hover\:opacity-10:hover {
  opacity: 0.1;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.-top-4 {
  top: -1rem;
}

.-right-4 {
  right: -1rem;
}

.w-24 {
  width: 6rem;
}

.h-24 {
  height: 6rem;
}

.from-white\/50 {
  --tw-gradient-from: rgba(255, 255, 255, 0.5);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(255, 255, 255, 0));
}

.to-transparent {
  --tw-gradient-to: transparent;
}

.blur-xl {
  filter: blur(16px);
}

.group-hover\:scale-150:hover {
  transform: scale(1.5);
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.w-20 {
  width: 5rem;
}

.h-20 {
  height: 5rem;
}

.rounded-2xl {
  border-radius: 1rem;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.bg-white\/20 {
  background-color: rgba(255, 255, 255, 0.2);
}

.group-hover\:opacity-100:hover {
  opacity: 1;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.group-hover\:text-orange-600:hover {
  color: #ea580c;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.leading-relaxed {
  line-height: 1.625;
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.opacity-70 {
  opacity: 0.7;
}

.max-h-20 {
  max-height: 5rem;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-green-500 {
  color: #22c55e;
}

.justify-between {
  justify-content: space-between;
}

.bg-orange-50 {
  background-color: #fff7ed;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.p-3 {
  padding: 0.75rem;
}

.flex-1 {
  flex: 1 1 0%;
}

.mr-4 {
  margin-right: 1rem;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.bg-clip-text {
  -webkit-background-clip: text;
  background-clip: text;
}

.text-transparent {
  color: transparent;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.bg-green-50 {
  background-color: #f0fdf4;
}

.bg-blue-50 {
  background-color: #eff6ff;
}

.w-full {
  width: 100%;
}

.font-semibold {
  font-weight: 600;
}

.text-orange-600 {
  color: #ea580c;
}

.group-hover\:translate-x-2:hover {
  transform: translateX(0.5rem);
}

.bg-orange-100 {
  background-color: #ffedd5;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  margin-left: calc(0.5rem * var(--tw-space-x-reverse));
}

.group-hover\:translate-x-1:hover {
  transform: translateX(0.25rem);
}

.duration-200 {
  transition-duration: 200ms;
}

.cursor-pointer {
  cursor: pointer;
}

.gap-6 {
  gap: 1.5rem;
}

.bg-white\/80 {
  background-color: rgba(255, 255, 255, 0.8);
}

.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

.p-6 {
  padding: 1.5rem;
}

.border-gray-200 {
  border-color: #e5e7eb;
}

.hover\:border-orange-300:hover {
  border-color: #fdba74;
}

.w-12 {
  width: 3rem;
}

.h-12 {
  height: 3rem;
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  margin-left: calc(1rem * var(--tw-space-x-reverse));
}

.from-blue-900 {
  --tw-gradient-from: #1e3a8a;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(30, 58, 138, 0));
}

.via-blue-800 {
  --tw-gradient-stops: var(--tw-gradient-from), #1e40af, var(--tw-gradient-to, rgba(30, 64, 175, 0));
}

.to-green-800 {
  --tw-gradient-to: #065f46;
}

.lg\:p-12 {
  @media (min-width: 1024px) {
    padding: 3rem;
  }
}

.opacity-10 {
  opacity: 0.1;
}

.top-4 {
  top: 1rem;
}

.right-4 {
  right: 1rem;
}

.w-32 {
  width: 8rem;
}

.h-32 {
  height: 8rem;
}

.from-orange-400\/20 {
  --tw-gradient-from: rgba(251, 191, 36, 0.2);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(251, 191, 36, 0));
}

.blur-2xl {
  filter: blur(40px);
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.bottom-4 {
  bottom: 1rem;
}

.left-4 {
  left: 1rem;
}

.lg\:text-3xl {
  @media (min-width: 1024px) {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}

.text-blue-100 {
  color: #dbeafe;
}

.mb-8 {
  margin-bottom: 2rem;
}

.max-w-2xl {
  max-width: 42rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.flex-col {
  flex-direction: column;
}

.sm\:flex-row {
  @media (min-width: 640px) {
    flex-direction: row;
  }
}

.gap-4 {
  gap: 1rem;
}

.from-orange-500 {
  --tw-gradient-from: #f97316;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(249, 115, 22, 0));
}

.to-orange-600 {
  --tw-gradient-to: #ea580c;
}

.hover\:from-orange-600:hover {
  --tw-gradient-from: #ea580c;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(234, 88, 12, 0));
}

.hover\:to-orange-700:hover {
  --tw-gradient-to: #c2410c;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.shadow-2xl {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.hover\:shadow-orange-500\/25:hover {
  box-shadow: 0 25px 50px -12px rgba(249, 115, 22, 0.25);
}

.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
  margin-left: calc(0.75rem * var(--tw-space-x-reverse));
}

.bg-white\/10 {
  background-color: rgba(255, 255, 255, 0.1);
}

.border-white\/20 {
  border-color: rgba(255, 255, 255, 0.2);
}

.hover\:bg-white\/20:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.hover\:scale-105:hover {
  transform: scale(1.05);
}

.-skew-x-12 {
  transform: skewX(-12deg);
}

.-translate-x-full {
  transform: translateX(-100%);
}

.group-hover\:translate-x-full:hover {
  transform: translateX(100%);
}