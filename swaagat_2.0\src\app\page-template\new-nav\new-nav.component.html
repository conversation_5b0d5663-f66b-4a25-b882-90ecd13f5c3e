<nav class="main-navigation">
  <div class="pattern-overlay">
    <div class="pattern-bg"></div>
  </div>
  <div class="bg-element-1"></div>

  <div class="nav-container" [class.hidden]="isMobileMenuOpen">
    <div
      *ngFor="let item of navigationItems; let i = index"
      class="dropdown-container"
      [class.dropdown-open]="item.dropdownOpen"
      (mouseenter)="onNavItemHover(item, i)"
      (mouseleave)="onNavItemLeave()"
    >
      <button 
        class="nav-button"
        [class.active]="item.isActive"
        (click)="onNavItemClick(item, i)"
        [routerLink]="[item.path]"
        [attr.aria-expanded]="item.hasDropdown ? item.dropdownOpen : null"
        [attr.aria-haspopup]="item.hasDropdown"
      >
        <span>{{ item.label }}</span>
        <svg *ngIf="item.hasDropdown" class="chevron" [class]="getChevronClass(item)" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="m6 9 6 6 6-6"></path>
        </svg>
        <div class="nav-underline"></div>
      </button>

      <div
        *ngIf="item.hasDropdown && item.dropdownOpen"
        style="position: absolute; top: 100%; left: 50%; transform: translateX(-50%); background: #ffffff; min-width: 200px; min-height: 100px; z-index: 1000; overflow: visible; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15); border-radius: 8px;"
        (mouseenter)="onDropdownHover()"
        (mouseleave)="onDropdownLeave()"
      >
        <div style="padding: 8px 0; overflow: visible;">
          <a 
            *ngFor="let childItem of item.children"
            class="dropdown-item"
            [routerLink]="[childItem.path]"
            (click)="onDropdownItemClick(i, childItem)"
            [attr.aria-label]="childItem.label"
          >
            {{ childItem.label }}
          </a>
        </div>
      </div>
    </div>
  </div>

  <button 
    class="mobile-menu-btn" 
    aria-label="Toggle menu"
    (click)="toggleMobileMenu()"
    [class.menu-open]="isMobileMenuOpen"
  >
    <div class="hamburger-icon">
      <span class="hamburger-line" [class.line-1-active]="isMobileMenuOpen"></span>
      <span class="hamburger-line" [class.line-2-active]="isMobileMenuOpen"></span>
      <span class="hamburger-line" [class.line-3-active]="isMobileMenuOpen"></span>
    </div>
  </button>

  <div 
    class="mobile-menu-overlay" 
    *ngIf="isMobileMenuOpen"
  >
    <div class="mobile-menu-content">
      <div 
        *ngFor="let item of navigationItems; let i = index"
        class="mobile-menu-section"
      >
        <button 
          class="mobile-nav-button"
          [class.active]="item.isActive"
          [class.has-dropdown]="item.hasDropdown"
          [routerLink]="[item.path]"
          (click)="onNavItemClick(item, i)"
        >
          <span>{{ item.label }}</span>
          <svg *ngIf="item.hasDropdown" class="mobile-chevron" [class]="getChevronClass(item)" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="m6 9 6 6 6-6"></path>
          </svg>
        </button>
        <div 
          *ngIf="item.hasDropdown && item.dropdownOpen" 
          class="mobile-dropdown-items"
        >
          <a 
            *ngFor="let childItem of item.children"
            class="mobile-dropdown-item"
            [routerLink]="[childItem.path]"
            (click)="onDropdownItemClick(i, childItem)"
          >
            {{ childItem.label }}
          </a>
        </div>
      </div>
    </div>
  </div>
</nav>