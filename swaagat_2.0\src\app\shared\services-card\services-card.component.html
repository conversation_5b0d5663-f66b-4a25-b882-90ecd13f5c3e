<section class="services-section">
  <div class="header">
    <h2>Government Services</h2>
    <p class="subtitle">Digital services for Tripura citizens</p>
  </div>

  <!-- Compact Grid of services -->
  <div class="services-grid">
    <div
      class="service-card"
      *ngFor="let service of visibleServices; let i = index"
      [attr.data-index]="i"
    >
      <div
        class="icon-container"
        [style.background-color]="getBackgroundColorForIndex(i)"
      >
        <div
          class="icon"
          [style.color]="getIconColorForIndex(i)"
          [innerHTML]="service.icon"
        ></div>
      </div>
      <h3 class="service-name">{{ service.name }}</h3>
    </div>
  </div>

  <!-- Load More Button -->
  <div class="load-more-section" *ngIf="hasMoreServices">
    <button class="load-more-btn" (click)="loadMore()">
      <span>Show All Services</span>
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <polyline points="6,9 12,15 18,9"></polyline>
      </svg>
    </button>
  </div>
</section>