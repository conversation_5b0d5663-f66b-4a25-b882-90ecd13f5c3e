.about-section-modern {
            padding: 5rem 1.5rem;
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            color: #111827;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            gap: 10vh;
            align-items: center;
            justify-content: center;
        }

        .section-title {
          text-align: center;
          font-size: 2.8rem;
          font-weight: 700;
          color: #1a1a2e;
          margin-bottom: 1.5rem;
          background: linear-gradient(90deg, #e563f1, #f65c9f);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          letter-spacing: -0.5px;
        }

        .about-wrapper {
            max-width: 90vw;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr;
            gap: 5rem;
            align-items: center;
        }

        @media (min-width: 992px) {
            .about-wrapper {
                grid-template-columns: 1fr 1fr;
                gap: 2rem;
            }
        }

        /* Tagline */
        .about-tagline {
            display: inline-block;
            font-size: 0.875rem;
            font-weight: 600;
            color: #7c3aed;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 0.75rem;
            padding: 0.25rem 0.75rem;
            background: #f3e8ff;
            border-radius: 20px;
        }

        /* Title */
        .about-title {
            font-size: 2.25rem;
            font-weight: 800;
            line-height: 1.2;
            margin-bottom: 1.25rem;
            color: #111827;
        }

        .highlight {
            background: linear-gradient(90deg, #7c3aed 0%, #c026d3 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Description */
        .about-description {
            font-size: 1.125rem;
            color: #4b5563;
            line-height: 1.7;
            margin-bottom: 2rem;
            max-width: 520px;
        }

        /* Stats Grid */
        .about-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .stat-item strong {
            display: block;
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
        }

        .stat-item small {
            display: block;
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 400;
            margin-top: 0.25rem;
        }

        /* CTA Buttons */
        .about-cta {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .btn-primary,
        .btn-outline {
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 1rem;
            text-align: center;
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background: linear-gradient(90deg, #7c3aed, #c026d3);
            color: white;
            box-shadow: 0 4px 15px rgba(124, 58, 237, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(124, 58, 237, 0.4);
        }

        .btn-outline {
            background: rgba(255, 255, 255, 0.9);
            color: #374151;
            border: 2px solid #d1d5db;
        }

        .btn-outline:hover {
            border-color: #7c3aed;
            color: #7c3aed;
            transform: translateY(-2px);
            background: rgba(255, 255, 255, 1);
        }

        /* Visual Container */
        .about-visual {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 1rem;
            width: 100%;
            max-width: 100%;
        }

        .about-visual img {
            width: 100%;
            max-width: 600px;
            height: auto;
            filter: drop-shadow(0 20px 30px rgba(0, 0, 0, 0.1));
            transition: transform 0.4s ease;
            object-fit: contain;
        }

        .about-visual img:hover {
            transform: translateY(-6px) rotate(2deg);
        }

        /* Content Container */
        .about-content {
            width: 100%;
            max-width: 100%;
        }

        /* Responsive adjustments */
        @media (max-width: 991px) {
            .about-visual {
                order: -1;
            }

            .about-title {
                font-size: 2rem;
            }

            .about-section-modern {
                background-attachment: scroll;
            }
        }

        @media (max-width: 768px) {
            .about-stats {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .about-cta {
                flex-direction: column;
            }

            .btn-primary,
            .btn-outline {
                width: 100%;
            }
        }