<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Swaagat2</title>
    <base href="/" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="icon" type="image/x-icon" href="favicon.ico" />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&amp;display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/icon?family=Material+Icons"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"
    />
    <link rel="manifest" href="manifest.webmanifest" />
    <link rel="manifest" href="manifest.webmanifest" />
    <link rel="manifest" href="manifest.webmanifest" />
    <link rel="manifest" href="manifest.webmanifest" />
  </head>
  <body class="mat-typography">
    <app-root></app-root>
    <noscript
      >Please enable JavaScript to continue using this application.</noscript
    >

    <!-- Google Translate Script -->
    <script type="text/javascript">
      function googleTranslateElementInit() {
        new google.translate.TranslateElement(
          {
            pageLanguage: "en", // default language
            includedLanguages:
              "hi,bn,te,mr,ta,ur,gu,kn,ml,pa,or,as,mai,ne,ks,sd,sa,doi,mni,bo,brx,sat,kok",
            layout: google.translate.TranslateElement.InlineLayout.SIMPLE,
          },
          "google_translate_element"
        );
      }
    </script>

    <script
      type="text/javascript"
      src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"
    ></script>
  </body>
</html>
