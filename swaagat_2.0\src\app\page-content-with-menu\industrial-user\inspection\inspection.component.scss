// inspection.component.scss

.inspection-container {
  margin-top: 10vh;
  padding: 0 20px 20px 20px;

  width: 100%;
  box-sizing: border-box;
}

.inspection-content {
  background-color: #ffffff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.inspection-form {
  width: 100%;
}

// --- Filter Section ---
.filter-section {
  margin-top: 20px;
}

.filter-row {
  display: grid;

  grid-template-columns: repeat(10, 1fr);
  gap: 15px;
  align-items: end;
}

// Assign grid columns to filter items
.date-from {
  grid-column: span 2;
}
.date-to {
  grid-column: span 2;
}
.department {
  grid-column: span 4;
}
.actions {
  grid-column: span 4;

  display: flex;
  flex-direction: column;
}

.filter-label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #333;

  min-height: 20px;
}

.blank-label {
  visibility: hidden;
}

.button-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

// --- Divider ---
.divider {
  margin: 20px 0;
  border: 0;
  height: 1px;
  background-color: #dee2e6;
}

// --- List Section ---
.list-section {
  margin-top: 20px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-text {
  font-size: 1.2em;
  font-weight: bold;
  color: #007bff;
}

.header-actions {
  .request-button {
  }
}

// --- Tabs and Table ---
.tabs-container {
}

.table-wrapper {
  padding: 5px;
}

// --- Responsive Adjustments (Example) ---
@media (max-width: 768px) {
  .filter-row {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .date-from,
  .date-to,
  .department,
  .actions {
    grid-column: span 1;
  }

  .actions {
    align-items: flex-start;
    .button-group {
      flex-direction: column;
      width: 100%;
      .search-button {
        margin-left: 0;
        margin-top: 8px;
      }
    }
  }

  .list-header {
    flex-direction: column;
    align-items: flex-start;
    .header-actions {
      width: 100%;
      margin-top: 10px;
    }
  }
}
