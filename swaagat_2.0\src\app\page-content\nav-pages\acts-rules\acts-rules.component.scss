@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;500;600;700&display=swap');

:host {
  --primary-blue: #3B82F6;
  --primary-blue-light: #60A5FA;
  --primary-blue-dark: #1E40AF;
  --secondary-purple: #8B5CF6;
  --secondary-purple-light: #A78BFA;
}

.acts-rules-container {
  font-family: 'Open Sans', sans-serif;
  line-height: 1.6;
  color: #333333;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  min-width: 95vw;
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  .container {
    margin: 0 auto;
    padding: 2rem;
  }

  .main-title-section {
    text-align: center;
    margin-bottom: 3rem;
    padding: 1.5rem 1rem;
    background: linear-gradient(135deg, #1E40AF 0%, #3B82F6 100%);
    border-radius: 0.75rem;
    color: white;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }

    .main-title {
      font-family: 'Roboto', sans-serif;
      font-size: clamp(1.5rem, 5vw, 2.5rem);
      font-weight: 700;
      position: relative;
      z-index: 1;
      margin-bottom: 0.5rem;
    }

    .main-subtitle {
      font-size: clamp(0.875rem, 2vw, 1.125rem);
      font-weight: 500;
      opacity: 0.9;
      position: relative;
      z-index: 1;
      max-width: 31.25rem;
      margin: 0 auto;
    }
  }

  .section {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 0.25rem 1.25rem rgba(0,0,0,0.05);
    margin-bottom: 2rem;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
      transform: translateY(-0.125rem);
      box-shadow: 0 0.5rem 1.5625rem rgba(0,0,0,0.1);
    }

    .section-header {
      background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
      color: white;
      padding: 1.5rem 2rem;
      font-size: clamp(1rem, 2vw, 1.3rem);
      font-weight: 400;
      font-family: 'Roboto', sans-serif;
      display: flex;
      align-items: center;
      gap: 0.5rem;

      &.policies {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      }

      &.rti {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
      }

      .icon {
        font-size: 1.5rem;
        width: 1.5rem;
        height: 1.5rem;
        color: white;
      }
    }

    .section-content {
      padding: 2rem;
    }

    .document-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(18.75rem, 1fr));
      gap: 1rem;
    }

    .document-card {
      border: 0.125rem solid #e5e7eb;
      border-radius: 0.5rem;
      padding: 1.5rem;
      transition: all 0.3s ease;
      cursor: pointer;
      position: relative;
      background: #f9fafb;
      display: flex;
      flex-direction: column;

      .document-icon {
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
        width: 2.5rem;
        height: 2.5rem;

        .icon {
          font-size: 1.5rem;
          color: white;
        }
      }

      .document-title {
        font-family: 'Roboto', sans-serif;
        font-weight: 500;
        color: #1e3a8a;
        margin-bottom: 0.5rem;
        line-height: 1.4;
      }

      .document-meta {
        font-size: 0.9rem;
        color: #64748b;
        align-items: center;
        gap: 0.5rem;
        margin-top: 0.1rem;

        .badge {
          padding: 0.25rem 0.75rem;
          border-radius: 1.25rem;
          font-size: 0.8rem;
          font-weight: 400;

          &.blue {
            background: #cbf2f8;
            color: #15637c;
          }

          &.orange {
            background: #fef3c7;
            color: #b45309;
          }

          &.green {
            background: #d1fae5;
            color: #065f46;
          }

          &.red {
            background: #f9d8d8;
            color: #eb3535;
          }

          &.pink {
            background: #ffe2fd;
            color: #a32ba1;
          }
        }
      }
    }

    &.swaagat {
      .document-card {
        &:hover {
          border-color: #3b82f6;
          background: white;
          transform: translateY(-0.0625rem);
          box-shadow: 0 0.25rem 0.75rem rgba(59, 130, 246, 0.15);
        }

        .document-icon {
          background: linear-gradient(135deg, #60a5fa 0%, #2563eb 100%);
        }
      }
    }

    &.policies {
      .document-card {
        &:hover {
          border-color: #10b981;
          background: white;
          transform: translateY(-0.0625rem);
          box-shadow: 0 0.25rem 0.75rem rgba(16, 185, 129, 0.15);
        }

        .document-icon {
          background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
      }
    }

    &.rti {
      .document-card {
        &:hover {
          border-color: #8b5cf6;
          background: white;
          transform: translateY(-0.0625rem);
          box-shadow: 0 0.25rem 0.75rem rgba(139, 92, 246, 0.15);
        }

        .document-icon {
          background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }
      }
    }
  }

  .department-orders {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 0.25rem 1.25rem rgba(0,0,0,0.05);
    padding: 2rem;
    margin-top: 2rem;
    text-align: center;

    h2 {
      font-family: 'Roboto', sans-serif;
      color: #1e3a8a;
      margin-bottom: 1rem;
      font-weight: 400;
    }

    .coming-soon {
      color: #64748b;
      font-style: italic;
      font-weight: 300;
    }
  }

  .no-results {
    display: none;
    text-align: center;
    color: #64748b;
    padding: 2rem;
    font-style: italic;
  }

  @media (max-width: 768px) {
    .container {
      padding: 1rem;
    }
    
    .main-title-section {
      padding: 2.5rem 1.5rem;
      margin-bottom: 2.5rem;

      .main-title {
        font-size: clamp(1.5rem, 5vw, 2rem);
      }
    }
    
    .section-content {
      padding: 1rem;
    }
    
    .document-grid {
      grid-template-columns: 1fr;
    }
    
    .document-card {
      padding: 1rem;

      .document-icon {
        width: 2.25rem;
        height: 2.25rem;

        .icon {
          font-size: 1.25rem;
        }
      }
    }
  }
}