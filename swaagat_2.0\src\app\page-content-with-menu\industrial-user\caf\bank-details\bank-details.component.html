<div>
  <form [formGroup]="bankDetailsForm" (ngSubmit)="onSubmit()" novalidate>

    <div class="grid-container">
      <app-ilogi-input
        formControlName="accountHolderName"
        fieldLabel="Account Holder Name"
        [mandatory]="true"
        placeholder="Enter Account Holder Name"
      ></app-ilogi-input>

      <app-ilogi-input
        formControlName="accountNumber"
        fieldLabel="Account Number"
        [mandatory]="true"
        placeholder="Enter Account Number"
      ></app-ilogi-input>

      <app-ilogi-input
        formControlName="bankName"
        fieldLabel="Bank Name"
        [mandatory]="true"
        placeholder="Enter Bank Name"
      ></app-ilogi-input>

      <app-ilogi-input
        formControlName="branchName"
        fieldLabel="Branch Name"
        [mandatory]="true"
        placeholder="Enter Branch Name"
      ></app-ilogi-input>

      <app-ilogi-input
        formControlName="ifscCode"
        fieldLabel="IFSC Code"
        [mandatory]="true"
        placeholder="Enter IFSC Code (e.g., SBIN00001234)"
      ></app-ilogi-input>

      <app-ilogi-select
        formControlName="accountType"
        fieldLabel="Account Type"
        [selectOptions]="AccountTypes"
        placeholder="Select Account Type"
        [mandatory]="true"
      ></app-ilogi-select>
    </div>

    <!-- Submit Buttons -->
    <div class="form-actions mt-4">
      <button type="button" class="btn btn-primary" (click)="saveAsDraft()">
        Save As Draft
      </button>
      <button type="submit" class="btn btn-success">
        Submit
      </button>
    </div>

  </form>
</div>