.action-area {
    padding-left: 5px;
    padding-right: 5px;
    padding-left: 5px;
}

.main-container {
    padding: 1.4em 0em 0.2em 0em;
}

.nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active {
    color: #FFFFFF;
    background-color: #03578D;
}

.nav-tabs .nav-link.active {
    border-color: #03578D;
}
.nav-tabs .nav-link {
    border-color: #F0F0F0;
    color: #121212;
}
.nav-tabs {
    border-bottom: 2px solid #F28B30;
}
.nav-tabs .nav-item {
    margin-bottom: 0px;
}
.top-space {
    margin-top: 10px;
}
.no-padding-first-last .col-md-4:last-child , .no-padding-first-last .col-md-6:last-child , .no-padding-first-last .col-md-12:last-child {
    padding-right: 0px;
}
.no-padding-first-last .col-md-4:first-child , .no-padding-first-last .col-md-6:first-child , .no-padding-first-last .col-md-12:first-child {
    padding-left: 0px;
}
.form-group-margin {
    margin: 10px 0px;
}
.save-draft-btn {
    background-color: #7AA5BF;
    color: #FFFFFF;
    font-size: 1.3em;
}
.save-btn {
    background-color: #718E5A;
    color: #FFFFFF;
    font-size: 1.3em;
}
// .mat-datepicker-toggle {
//     position: absolute;
//     bottom: 10px;
//     right: 5px;
// }

.invalid-input {
    bottom: auto !important;
    top: 4px !important;
}
.second-input-error {
    right: -33px !important;
}
#invalid-input-CommissioningDate {
    top:-7px!important;
}
.form-group {
    margin-bottom: 0rem !important;
}
.mat-datepicker-toggle {
    position: absolute;
    bottom: 0px;
    right: 0px;
}


.enterprise-form {
  display: grid;
  grid-template-columns: repeat(2, 1fr); // 👈 3 equal columns
//   gap: 1.5rem; // space between fields
column-gap: 1.5rem;
row-gap: 2rem;

  // Make form-actions full width (buttons row ke niche)
  .form-actions {
    grid-column: 1 / -1; // spans across all 3 columns
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
  }

  // Optional: responsive (2 cols on tablet, 1 col on mobile)
  @media (max-width: 992px) {
    grid-template-columns: repeat(2, 1fr);
  }
  @media (max-width: 576px) {
    grid-template-columns: 1fr;
  }
}

.btn-primary {
  background-color: #007bff;
  border: none;
  padding: 0.375rem 1rem;
  font-size: 0.85rem;
  border-radius: 6px;
  font-weight: 500;

  &:hover:not(:disabled) {
    background-color: #0069d9;
  }

  &:disabled {
    background-color: #6c757d;
    opacity: 0.6;
  }
}
.btn-success {
  border: none;
  padding: 0.375rem 1rem;
  font-size: 0.85rem;
  border-radius: 6px;
  font-weight: 500;



  &:disabled {
    background-color: #6c757d;
    opacity: 0.6;
  }
}