/* === PRIMENG TABLE WHITE THEME STYLING === */
/* Clean white theme with all hover effects completely disabled */

/* Main table container - White theme */
::ng-deep .p-datatable {
  background: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 16px !important;
  overflow: hidden !important;
  color: #374151 !important;
}

/* Table header styling */
::ng-deep .p-datatable .p-datatable-header {
  background: #f9fafb !important;
  border-bottom: 1px solid #e5e7eb !important;
  color: #000000 !important;
  padding: 1rem !important;
}

/* Column headers */
::ng-deep .p-datatable .p-datatable-thead > tr > th {
  background: #f3f4f6 !important;
  color: #000000 !important;
  border-bottom: 1px solid #e5e7eb !important;
  border-right: 1px solid #e5e7eb !important;
  padding: 1rem 0.75rem !important;
  font-weight: 600 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* Remove last column border */
::ng-deep .p-datatable .p-datatable-thead > tr > th:last-child {
  border-right: none !important;
}

/* Table body rows */
::ng-deep .p-datatable .p-datatable-tbody > tr {
  background: #ffffff !important;
  color: #000000 !important;
  border-bottom: 1px solid #e5e7eb !important;
  transition: none !important;
}

/* Alternate row colors for better readability */
::ng-deep .p-datatable .p-datatable-tbody > tr:nth-child(even) {
  background: #ffffff !important;
  color: #000000 !important;
}

/* Table cells - REMOVED FLEX DISPLAY TO PREVENT DATA ISSUES */
::ng-deep .p-datatable .p-datatable-tbody > tr > td {
  background: transparent !important;
  color: #000000 !important;
  border-right: 1px solid #e5e7eb !important;
  border-bottom: 1px solid #e5e7eb !important;
  padding: 0.875rem 0.75rem !important;
  vertical-align: middle !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  position: relative !important;
}

/* Last column - keep bottom border but remove right border */
::ng-deep .p-datatable .p-datatable-tbody > tr > td:last-child {
  border-right: none !important;
  border-bottom: 1px solid #e5e7eb !important;
}

/* === ELEMENT WRAPPER STYLING === */
/* Wrapper for elements that need proper spacing */
::ng-deep .p-datatable .p-datatable-tbody > tr > td .element-wrapper {
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  flex-wrap: nowrap !important;
}

/* === STATUS BADGE STYLING === */
::ng-deep .p-datatable .p-datatable-tbody > tr > td .status-badge-wrapper {
  display: inline-block !important;
  vertical-align: middle !important;
}

::ng-deep .p-datatable .p-datatable-tbody > tr > td .p-badge {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 80px !important;
  font-weight: 500 !important;
  padding: 0.25rem 0.75rem !important;
  border-radius: 12px !important;
  flex-shrink: 0 !important;
}

/* Status badge colors */
::ng-deep .p-datatable .p-badge.p-badge-success {
  background-color: #10b981 !important;
  color: #ffffff !important;
}

::ng-deep .p-datatable .p-badge.p-badge-danger {
  background-color: #ef4444 !important;
  color: #ffffff !important;
}

::ng-deep .p-datatable .p-badge.p-badge-warn {
  background-color: #f59e0b !important;
  color: #ffffff !important;
}

::ng-deep .p-datatable .p-badge.p-badge-info {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
}

/* === PAYMENT TAG STYLING === */
::ng-deep .p-datatable .p-datatable-tbody > tr > td .payment-tag-wrapper {
  display: inline-block !important;
  vertical-align: middle !important;
}

::ng-deep .p-datatable .p-datatable-tbody > tr > td .p-tag {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 100px !important;
  font-weight: 500 !important;
  padding: 0.25rem 0.75rem !important;
  border-radius: 12px !important;
  flex-shrink: 0 !important;
}

/* Payment tag colors */
::ng-deep .p-datatable .p-tag.p-tag-success {
  background-color: #10b981 !important;
  color: #ffffff !important;
}

::ng-deep .p-datatable .p-tag.p-tag-danger {
  background-color: #ef4444 !important;
  color: #ffffff !important;
}

::ng-deep .p-datatable .p-tag.p-tag-warn {
  background-color: #f59e0b !important;
  color: #ffffff !important;
}

::ng-deep .p-datatable .p-tag.p-tag-info {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
}

/* === ACTION BUTTON STYLING === */
::ng-deep .p-datatable .p-datatable-tbody > tr > td .action-cell-wrapper {
  position: relative !important;
  display: inline-block !important;
  vertical-align: middle !important;
}

::ng-deep .p-datatable .p-datatable-tbody > tr > td .action-cell-content {
  position: relative !important;
  display: inline-flex !important;
  justify-content: center !important;
  align-items: center !important;
}

::ng-deep .p-datatable .p-datatable-tbody > tr > td .action-button {
  color: #6b7280 !important;
  background: transparent !important;
  border: 1px solid #d1d5db !important;
  width: 32px !important;
  height: 32px !important;
  min-width: 32px !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 50% !important;
  cursor: pointer !important;
  transition: none !important;
  box-shadow: none !important;
  font-size: 14px !important;
}

::ng-deep .p-datatable .p-datatable-tbody > tr > td .action-button:hover {
  background: #f3f4f6 !important;
  border-color: #9ca3af !important;
  color: #1f2937 !important;
  transform: none !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
  transition: none !important;
}

::ng-deep .p-datatable .p-datatable-tbody > tr > td .action-button:active {
  background: #e5e7eb !important;
  border-color: #6b7280 !important;
  transform: none !important;
  transition: none !important;
}

/* Menu positioning - BLACK BACKGROUND WITH WHITE TEXT */
::ng-deep .p-menu {
  min-width: 150px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #374151 !important;
  border-radius: 8px !important;
  z-index: 1100 !important;
  max-width: 90vw !important;
  max-height: 80vh !important;
  overflow: auto !important;
  background-color: #000000 !important;
  color: #ffffff !important;
}

::ng-deep .p-menu .p-menuitem-link {
  padding: 0.75rem 1rem !important;
  transition: none !important;
  color: #ffffff !important;
  background-color: #000000 !important;
}

::ng-deep .p-menu .p-menuitem-link:hover {
  background: #333333 !important;
  color: #ffffff !important;
  transform: none !important;
  transition: none !important;
}

::ng-deep .p-menu .p-menuitem-icon {
  margin-right: 0.5rem !important;
  color: #ffffff !important;
}

::ng-deep .p-menu .p-menuitem-text {
  color: #ffffff !important;
}

/* === COMPLETELY DISABLE ALL HOVER EFFECTS === */
/* Remove row hover effects */
::ng-deep .p-datatable .p-datatable-tbody > tr:hover {
  background: inherit !important;
  transform: none !important;
  box-shadow: none !important;
  transition: none !important;
}

/* Disable hover on paginator buttons */
::ng-deep .p-paginator .p-paginator-page:hover:not(.p-highlight),
::ng-deep .p-paginator .p-paginator-next:hover:not(.p-disabled),
::ng-deep .p-paginator .p-paginator-prev:hover:not(.p-disabled),
::ng-deep .p-paginator .p-paginator-first:hover:not(.p-disabled),
::ng-deep .p-paginator .p-paginator-last:hover:not(.p-disabled) {
  background: #ffffff !important;
  border-color: #e5e7eb !important;
  color: #374151 !important;
  transform: none !important;
  box-shadow: none !important;
  transition: none !important;
}

/* Action buttons - remove hover effects */
::ng-deep .p-datatable .p-button:hover,
::ng-deep .p-datatable .p-button.p-button-sm:hover {
  background: #ffffff !important;
  border-color: #e5e7eb !important;
  color: #374151 !important;
  transform: none !important;
  box-shadow: none !important;
  transition: none !important;
}

/* Sortable headers - remove hover effects */
::ng-deep .p-datatable .p-sortable-column {
  cursor: pointer !important;
  user-select: none !important;
}

::ng-deep .p-datatable .p-sortable-column:hover {
  background: inherit !important;
  transform: none !important;
  transition: none !important;
}

::ng-deep .p-datatable .p-sortable-column:hover .p-sortable-column-icon {
  color: #6b7280 !important;
  transform: none !important;
  transition: none !important;
}

/* Remove focus/hover from inputs */
::ng-deep .p-datatable .p-inputtext:focus {
  transform: none !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  transition: none !important;
}

::ng-deep .p-datatable .p-inputtext:hover {
  border-color: #d1d5db !important;
  transform: none !important;
  box-shadow: none !important;
  transition: none !important;
}

/* Ensure no pseudo-elements create unwanted effects */
::ng-deep .p-datatable .p-button::before,
::ng-deep .p-datatable .p-button::after,
::ng-deep .p-paginator .p-paginator-page::before,
::ng-deep .p-paginator .p-paginator-page::after {
  display: none !important;
}

/* Paginator styling - white theme */
::ng-deep .p-paginator {
  background: #f9fafb !important;
  border-top: 1px solid #e5e7eb !important;
  border-radius: 0 0 16px 16px !important;
  padding: 0.75rem 1rem !important;
}

/* Paginator buttons */
::ng-deep .p-paginator .p-paginator-page,
::ng-deep .p-paginator .p-paginator-next,
::ng-deep .p-paginator .p-paginator-prev,
::ng-deep .p-paginator .p-paginator-first,
::ng-deep .p-paginator .p-paginator-last {
  background: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  color: #374151 !important;
  transition: none !important;
}

/* Active paginator page */
::ng-deep .p-paginator .p-paginator-page.p-highlight {
  background: #3b82f6 !important;
  border-color: #3b82f6 !important;
  color: #ffffff !important;
}

/* Disabled paginator buttons */
::ng-deep .p-paginator .p-paginator-next.p-disabled,
::ng-deep .p-paginator .p-paginator-prev.p-disabled,
::ng-deep .p-paginator .p-paginator-first.p-disabled,
::ng-deep .p-paginator .p-paginator-last.p-disabled {
  background: #f3f4f6 !important;
  border-color: #e5e7eb !important;
  color: #9ca3af !important;
  cursor: not-allowed !important;
}

/* Responsive improvements */
@media screen and (max-width: 768px) {
  ::ng-deep .p-datatable .p-datatable-tbody > tr > td .p-badge,
  ::ng-deep .p-datatable .p-datatable-tbody > tr > td .p-tag {
    min-width: 60px !important;
    font-size: 0.75rem !important;
    padding: 0.125rem 0.5rem !important;
  }

  ::ng-deep .p-datatable .p-datatable-tbody > tr > td .action-button {
    width: 28px !important;
    height: 28px !important;
    min-width: 28px !important;
  }
}
/* Ensure the action button displays the 3-dot icon */
::ng-deep .p-datatable .p-datatable-tbody > tr > td .action-button {
  color: #6b7280 !important;
  background: transparent !important;
  border: 1px solid #d1d5db !important;
  width: 32px !important;
  height: 32px !important;
  min-width: 32px !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 50% !important;
  cursor: pointer !important;
  transition: none !important;
  box-shadow: none !important;
  font-size: 14px !important;

  /* Ensure the icon is visible */
  i.pi {
    font-size: 14px !important;
    color: inherit !important;
  }
}

/* Hover and active states for the action button */
::ng-deep .p-datatable .p-datatable-tbody > tr > td .action-button:hover {
  background: #f3f4f6 !important;
  border-color: #9ca3af !important;
  color: #1f2937 !important;
  transform: none !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
  transition: none !important;
}

::ng-deep .p-datatable .p-datatable-tbody > tr > td .action-button:active {
  background: #e5e7eb !important;
  border-color: #6b7280 !important;
  transform: none !important;
  transition: none !important;
}

/* === STATUS BADGE STYLING === */
::ng-deep .p-datatable .p-datatable-tbody > tr > td .p-badge {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 80px !important;
  font-weight: 500 !important;
  padding: 0.25rem 0.75rem !important;
  border-radius: 12px !important;
  flex-shrink: 0 !important;
  text-align: center !important;
}

/* Status badge colors */
::ng-deep .p-datatable .p-badge.p-badge-success {
  background-color: #10b981 !important;
  color: #ffffff !important;
}

::ng-deep .p-datatable .p-badge.p-badge-danger {
  background-color: #ef4444 !important;
  color: #ffffff !important;
}

::ng-deep .p-datatable .p-badge.p-badge-warn {
  background-color: #f59e0b !important;
  color: #ffffff !important;
}

::ng-deep .p-datatable .p-badge.p-badge-info {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
}

/* === PAYMENT TAG STYLING === */
::ng-deep .p-datatable .p-datatable-tbody > tr > td .p-tag {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 100px !important;
  font-weight: 500 !important;
  padding: 0.25rem 0.75rem !important;
  border-radius: 12px !important;
  flex-shrink: 0 !important;
  text-align: center !important;
}

/* Payment tag colors */
::ng-deep .p-datatable .p-tag.p-tag-success {
  background-color: #10b981 !important;
  color: #ffffff !important;
}

::ng-deep .p-datatable .p-tag.p-tag-danger {
  background-color: #ef4444 !important;
  color: #ffffff !important;
}

::ng-deep .p-datatable .p-tag.p-tag-warn {
  background-color: #f59e0b !important;
  color: #ffffff !important;
}

::ng-deep .p-datatable .p-tag.p-tag-info {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
}
