{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/checkbox.mjs"], "sourcesContent": ["import { _IdGenerator } from '@angular/cdk/a11y';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, ChangeDetectorRef, NgZone, EventEmitter, HostAttributeToken, signal, booleanAttribute, numberAttribute, forwardRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Output, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS } from '@angular/forms';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _MatInternalFormField } from './internal-form-field-D5iFxU6d.mjs';\nimport { _ as _animationsDisabled } from './animation-ChQ1vjiF.mjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { M as MatRipple } from './ripple-BMyyyLz2.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/bidi';\n\n/** Injection token to be used to override the default options for `mat-checkbox`. */\nconst _c0 = [\"input\"];\nconst _c1 = [\"label\"];\nconst _c2 = [\"*\"];\nconst MAT_CHECKBOX_DEFAULT_OPTIONS = new InjectionToken('mat-checkbox-default-options', {\n  providedIn: 'root',\n  factory: MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    color: 'accent',\n    clickAction: 'check-indeterminate',\n    disabledInteractive: false\n  };\n}\n\n/**\n * Represents the different states that require custom transitions between them.\n * @docs-private\n */\nvar TransitionCheckState;\n(function (TransitionCheckState) {\n  /** The initial state of the component before any user interaction. */\n  TransitionCheckState[TransitionCheckState[\"Init\"] = 0] = \"Init\";\n  /** The state representing the component when it's becoming checked. */\n  TransitionCheckState[TransitionCheckState[\"Checked\"] = 1] = \"Checked\";\n  /** The state representing the component when it's becoming unchecked. */\n  TransitionCheckState[TransitionCheckState[\"Unchecked\"] = 2] = \"Unchecked\";\n  /** The state representing the component when it's becoming indeterminate. */\n  TransitionCheckState[TransitionCheckState[\"Indeterminate\"] = 3] = \"Indeterminate\";\n})(TransitionCheckState || (TransitionCheckState = {}));\n/** Change event object emitted by checkbox. */\nclass MatCheckboxChange {\n  /** The source checkbox of the event. */\n  source;\n  /** The new `checked` value of the checkbox. */\n  checked;\n}\n// Default checkbox configuration.\nconst defaults = MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY();\nclass MatCheckbox {\n  _elementRef = inject(ElementRef);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _ngZone = inject(NgZone);\n  _animationsDisabled = _animationsDisabled();\n  _options = inject(MAT_CHECKBOX_DEFAULT_OPTIONS, {\n    optional: true\n  });\n  /** Focuses the checkbox. */\n  focus() {\n    this._inputElement.nativeElement.focus();\n  }\n  /** Creates the change event that will be emitted by the checkbox. */\n  _createChangeEvent(isChecked) {\n    const event = new MatCheckboxChange();\n    event.source = this;\n    event.checked = isChecked;\n    return event;\n  }\n  /** Gets the element on which to add the animation CSS classes. */\n  _getAnimationTargetElement() {\n    return this._inputElement?.nativeElement;\n  }\n  /** CSS classes to add when transitioning between the different checkbox states. */\n  _animationClasses = {\n    uncheckedToChecked: 'mdc-checkbox--anim-unchecked-checked',\n    uncheckedToIndeterminate: 'mdc-checkbox--anim-unchecked-indeterminate',\n    checkedToUnchecked: 'mdc-checkbox--anim-checked-unchecked',\n    checkedToIndeterminate: 'mdc-checkbox--anim-checked-indeterminate',\n    indeterminateToChecked: 'mdc-checkbox--anim-indeterminate-checked',\n    indeterminateToUnchecked: 'mdc-checkbox--anim-indeterminate-unchecked'\n  };\n  /**\n   * Attached to the aria-label attribute of the host element. In most cases, aria-labelledby will\n   * take precedence so this may be omitted.\n   */\n  ariaLabel = '';\n  /**\n   * Users can specify the `aria-labelledby` attribute which will be forwarded to the input element\n   */\n  ariaLabelledby = null;\n  /** The 'aria-describedby' attribute is read after the element's label and field type. */\n  ariaDescribedby;\n  /**\n   * Users can specify the `aria-expanded` attribute which will be forwarded to the input element\n   */\n  ariaExpanded;\n  /**\n   * Users can specify the `aria-controls` attribute which will be forwarded to the input element\n   */\n  ariaControls;\n  /** Users can specify the `aria-owns` attribute which will be forwarded to the input element */\n  ariaOwns;\n  _uniqueId;\n  /** A unique id for the checkbox input. If none is supplied, it will be auto-generated. */\n  id;\n  /** Returns the unique id for the visual hidden input. */\n  get inputId() {\n    return `${this.id || this._uniqueId}-input`;\n  }\n  /** Whether the checkbox is required. */\n  required;\n  /** Whether the label should appear after or before the checkbox. Defaults to 'after' */\n  labelPosition = 'after';\n  /** Name value will be applied to the input element if present */\n  name = null;\n  /** Event emitted when the checkbox's `checked` value changes. */\n  change = new EventEmitter();\n  /** Event emitted when the checkbox's `indeterminate` value changes. */\n  indeterminateChange = new EventEmitter();\n  /** The value attribute of the native input element */\n  value;\n  /** Whether the checkbox has a ripple. */\n  disableRipple;\n  /** The native `<input type=\"checkbox\">` element */\n  _inputElement;\n  /** The native `<label>` element */\n  _labelElement;\n  /** Tabindex for the checkbox. */\n  tabIndex;\n  // TODO(crisbeto): this should be a ThemePalette, but some internal apps were abusing\n  // the lack of type checking previously and assigning random strings.\n  /**\n   * Theme color of the checkbox. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/checkbox/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color;\n  /** Whether the checkbox should remain interactive when it is disabled. */\n  disabledInteractive;\n  /**\n   * Called when the checkbox is blurred. Needed to properly implement ControlValueAccessor.\n   * @docs-private\n   */\n  _onTouched = () => {};\n  _currentAnimationClass = '';\n  _currentCheckState = TransitionCheckState.Init;\n  _controlValueAccessorChangeFn = () => {};\n  _validatorChangeFn = () => {};\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const tabIndex = inject(new HostAttributeToken('tabindex'), {\n      optional: true\n    });\n    this._options = this._options || defaults;\n    this.color = this._options.color || defaults.color;\n    this.tabIndex = tabIndex == null ? 0 : parseInt(tabIndex) || 0;\n    this.id = this._uniqueId = inject(_IdGenerator).getId('mat-mdc-checkbox-');\n    this.disabledInteractive = this._options?.disabledInteractive ?? false;\n  }\n  ngOnChanges(changes) {\n    if (changes['required']) {\n      this._validatorChangeFn();\n    }\n  }\n  ngAfterViewInit() {\n    this._syncIndeterminate(this.indeterminate);\n  }\n  /** Whether the checkbox is checked. */\n  get checked() {\n    return this._checked;\n  }\n  set checked(value) {\n    if (value != this.checked) {\n      this._checked = value;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  _checked = false;\n  /** Whether the checkbox is disabled. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    if (value !== this.disabled) {\n      this._disabled = value;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  _disabled = false;\n  /**\n   * Whether the checkbox is indeterminate. This is also known as \"mixed\" mode and can be used to\n   * represent a checkbox with three states, e.g. a checkbox that represents a nested list of\n   * checkable items. Note that whenever checkbox is manually clicked, indeterminate is immediately\n   * set to false.\n   */\n  get indeterminate() {\n    return this._indeterminate();\n  }\n  set indeterminate(value) {\n    const changed = value != this._indeterminate();\n    this._indeterminate.set(value);\n    if (changed) {\n      if (value) {\n        this._transitionCheckState(TransitionCheckState.Indeterminate);\n      } else {\n        this._transitionCheckState(this.checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked);\n      }\n      this.indeterminateChange.emit(value);\n    }\n    this._syncIndeterminate(value);\n  }\n  _indeterminate = signal(false);\n  _isRippleDisabled() {\n    return this.disableRipple || this.disabled;\n  }\n  /** Method being called whenever the label text changes. */\n  _onLabelTextChange() {\n    // Since the event of the `cdkObserveContent` directive runs outside of the zone, the checkbox\n    // component will be only marked for check, but no actual change detection runs automatically.\n    // Instead of going back into the zone in order to trigger a change detection which causes\n    // *all* components to be checked (if explicitly marked or not using OnPush), we only trigger\n    // an explicit change detection for the checkbox view and its children.\n    this._changeDetectorRef.detectChanges();\n  }\n  // Implemented as part of ControlValueAccessor.\n  writeValue(value) {\n    this.checked = !!value;\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnChange(fn) {\n    this._controlValueAccessorChangeFn = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  // Implemented as a part of Validator.\n  validate(control) {\n    return this.required && control.value !== true ? {\n      'required': true\n    } : null;\n  }\n  // Implemented as a part of Validator.\n  registerOnValidatorChange(fn) {\n    this._validatorChangeFn = fn;\n  }\n  _transitionCheckState(newState) {\n    let oldState = this._currentCheckState;\n    let element = this._getAnimationTargetElement();\n    if (oldState === newState || !element) {\n      return;\n    }\n    if (this._currentAnimationClass) {\n      element.classList.remove(this._currentAnimationClass);\n    }\n    this._currentAnimationClass = this._getAnimationClassForCheckStateTransition(oldState, newState);\n    this._currentCheckState = newState;\n    if (this._currentAnimationClass.length > 0) {\n      element.classList.add(this._currentAnimationClass);\n      // Remove the animation class to avoid animation when the checkbox is moved between containers\n      const animationClass = this._currentAnimationClass;\n      this._ngZone.runOutsideAngular(() => {\n        setTimeout(() => {\n          element.classList.remove(animationClass);\n        }, 1000);\n      });\n    }\n  }\n  _emitChangeEvent() {\n    this._controlValueAccessorChangeFn(this.checked);\n    this.change.emit(this._createChangeEvent(this.checked));\n    // Assigning the value again here is redundant, but we have to do it in case it was\n    // changed inside the `change` listener which will cause the input to be out of sync.\n    if (this._inputElement) {\n      this._inputElement.nativeElement.checked = this.checked;\n    }\n  }\n  /** Toggles the `checked` state of the checkbox. */\n  toggle() {\n    this.checked = !this.checked;\n    this._controlValueAccessorChangeFn(this.checked);\n  }\n  _handleInputClick() {\n    const clickAction = this._options?.clickAction;\n    // If resetIndeterminate is false, and the current state is indeterminate, do nothing on click\n    if (!this.disabled && clickAction !== 'noop') {\n      // When user manually click on the checkbox, `indeterminate` is set to false.\n      if (this.indeterminate && clickAction !== 'check') {\n        Promise.resolve().then(() => {\n          this._indeterminate.set(false);\n          this.indeterminateChange.emit(false);\n        });\n      }\n      this._checked = !this._checked;\n      this._transitionCheckState(this._checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked);\n      // Emit our custom change event if the native input emitted one.\n      // It is important to only emit it, if the native input triggered one, because\n      // we don't want to trigger a change event, when the `checked` variable changes for example.\n      this._emitChangeEvent();\n    } else if (this.disabled && this.disabledInteractive || !this.disabled && clickAction === 'noop') {\n      // Reset native input when clicked with noop. The native checkbox becomes checked after\n      // click, reset it to be align with `checked` value of `mat-checkbox`.\n      this._inputElement.nativeElement.checked = this.checked;\n      this._inputElement.nativeElement.indeterminate = this.indeterminate;\n    }\n  }\n  _onInteractionEvent(event) {\n    // We always have to stop propagation on the change event.\n    // Otherwise the change event, from the input element, will bubble up and\n    // emit its event object to the `change` output.\n    event.stopPropagation();\n  }\n  _onBlur() {\n    // When a focused element becomes disabled, the browser *immediately* fires a blur event.\n    // Angular does not expect events to be raised during change detection, so any state change\n    // (such as a form control's 'ng-touched') will cause a changed-after-checked error.\n    // See https://github.com/angular/angular/issues/17793. To work around this, we defer\n    // telling the form control it has been touched until the next tick.\n    Promise.resolve().then(() => {\n      this._onTouched();\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n  _getAnimationClassForCheckStateTransition(oldState, newState) {\n    // Don't transition if animations are disabled.\n    if (this._animationsDisabled) {\n      return '';\n    }\n    switch (oldState) {\n      case TransitionCheckState.Init:\n        // Handle edge case where user interacts with checkbox that does not have [(ngModel)] or\n        // [checked] bound to it.\n        if (newState === TransitionCheckState.Checked) {\n          return this._animationClasses.uncheckedToChecked;\n        } else if (newState == TransitionCheckState.Indeterminate) {\n          return this._checked ? this._animationClasses.checkedToIndeterminate : this._animationClasses.uncheckedToIndeterminate;\n        }\n        break;\n      case TransitionCheckState.Unchecked:\n        return newState === TransitionCheckState.Checked ? this._animationClasses.uncheckedToChecked : this._animationClasses.uncheckedToIndeterminate;\n      case TransitionCheckState.Checked:\n        return newState === TransitionCheckState.Unchecked ? this._animationClasses.checkedToUnchecked : this._animationClasses.checkedToIndeterminate;\n      case TransitionCheckState.Indeterminate:\n        return newState === TransitionCheckState.Checked ? this._animationClasses.indeterminateToChecked : this._animationClasses.indeterminateToUnchecked;\n    }\n    return '';\n  }\n  /**\n   * Syncs the indeterminate value with the checkbox DOM node.\n   *\n   * We sync `indeterminate` directly on the DOM node, because in Ivy the check for whether a\n   * property is supported on an element boils down to `if (propName in element)`. Domino's\n   * HTMLInputElement doesn't have an `indeterminate` property so Ivy will warn during\n   * server-side rendering.\n   */\n  _syncIndeterminate(value) {\n    const nativeCheckbox = this._inputElement;\n    if (nativeCheckbox) {\n      nativeCheckbox.nativeElement.indeterminate = value;\n    }\n  }\n  _onInputClick() {\n    this._handleInputClick();\n  }\n  _onTouchTargetClick() {\n    this._handleInputClick();\n    if (!this.disabled) {\n      // Normally the input should be focused already, but if the click\n      // comes from the touch target, then we might have to focus it ourselves.\n      this._inputElement.nativeElement.focus();\n    }\n  }\n  /**\n   *  Prevent click events that come from the `<label/>` element from bubbling. This prevents the\n   *  click handler on the host from triggering twice when clicking on the `<label/>` element. After\n   *  the click event on the `<label/>` propagates, the browsers dispatches click on the associated\n   *  `<input/>`. By preventing clicks on the label by bubbling, we ensure only one click event\n   *  bubbles when the label is clicked.\n   */\n  _preventBubblingFromLabel(event) {\n    if (!!event.target && this._labelElement.nativeElement.contains(event.target)) {\n      event.stopPropagation();\n    }\n  }\n  static ɵfac = function MatCheckbox_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCheckbox)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatCheckbox,\n    selectors: [[\"mat-checkbox\"]],\n    viewQuery: function MatCheckbox_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inputElement = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._labelElement = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-mdc-checkbox\"],\n    hostVars: 16,\n    hostBindings: function MatCheckbox_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵdomProperty(\"id\", ctx.id);\n        i0.ɵɵattribute(\"tabindex\", null)(\"aria-label\", null)(\"aria-labelledby\", null);\n        i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"mat-accent\");\n        i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationsDisabled)(\"mdc-checkbox--disabled\", ctx.disabled)(\"mat-mdc-checkbox-disabled\", ctx.disabled)(\"mat-mdc-checkbox-checked\", ctx.checked)(\"mat-mdc-checkbox-disabled-interactive\", ctx.disabledInteractive);\n      }\n    },\n    inputs: {\n      ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n      ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n      ariaDescribedby: [0, \"aria-describedby\", \"ariaDescribedby\"],\n      ariaExpanded: [2, \"aria-expanded\", \"ariaExpanded\", booleanAttribute],\n      ariaControls: [0, \"aria-controls\", \"ariaControls\"],\n      ariaOwns: [0, \"aria-owns\", \"ariaOwns\"],\n      id: \"id\",\n      required: [2, \"required\", \"required\", booleanAttribute],\n      labelPosition: \"labelPosition\",\n      name: \"name\",\n      value: \"value\",\n      disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n      tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? undefined : numberAttribute(value)],\n      color: \"color\",\n      disabledInteractive: [2, \"disabledInteractive\", \"disabledInteractive\", booleanAttribute],\n      checked: [2, \"checked\", \"checked\", booleanAttribute],\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      indeterminate: [2, \"indeterminate\", \"indeterminate\", booleanAttribute]\n    },\n    outputs: {\n      change: \"change\",\n      indeterminateChange: \"indeterminateChange\"\n    },\n    exportAs: [\"matCheckbox\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => MatCheckbox),\n      multi: true\n    }, {\n      provide: NG_VALIDATORS,\n      useExisting: MatCheckbox,\n      multi: true\n    }]), i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c2,\n    decls: 15,\n    vars: 23,\n    consts: [[\"checkbox\", \"\"], [\"input\", \"\"], [\"label\", \"\"], [\"mat-internal-form-field\", \"\", 3, \"click\", \"labelPosition\"], [1, \"mdc-checkbox\"], [1, \"mat-mdc-checkbox-touch-target\", 3, \"click\"], [\"type\", \"checkbox\", 1, \"mdc-checkbox__native-control\", 3, \"blur\", \"click\", \"change\", \"checked\", \"indeterminate\", \"disabled\", \"id\", \"required\", \"tabIndex\"], [1, \"mdc-checkbox__ripple\"], [1, \"mdc-checkbox__background\"], [\"focusable\", \"false\", \"viewBox\", \"0 0 24 24\", \"aria-hidden\", \"true\", 1, \"mdc-checkbox__checkmark\"], [\"fill\", \"none\", \"d\", \"M1.73,12.91 8.1,19.28 22.79,4.59\", 1, \"mdc-checkbox__checkmark-path\"], [1, \"mdc-checkbox__mixedmark\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-checkbox-ripple\", \"mat-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\", \"matRippleCentered\"], [1, \"mdc-label\", 3, \"for\"]],\n    template: function MatCheckbox_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 3);\n        i0.ɵɵlistener(\"click\", function MatCheckbox_Template_div_click_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._preventBubblingFromLabel($event));\n        });\n        i0.ɵɵelementStart(1, \"div\", 4, 0)(3, \"div\", 5);\n        i0.ɵɵlistener(\"click\", function MatCheckbox_Template_div_click_3_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._onTouchTargetClick());\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"input\", 6, 1);\n        i0.ɵɵlistener(\"blur\", function MatCheckbox_Template_input_blur_4_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._onBlur());\n        })(\"click\", function MatCheckbox_Template_input_click_4_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._onInputClick());\n        })(\"change\", function MatCheckbox_Template_input_change_4_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._onInteractionEvent($event));\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(6, \"div\", 7);\n        i0.ɵɵelementStart(7, \"div\", 8);\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(8, \"svg\", 9);\n        i0.ɵɵelement(9, \"path\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelement(10, \"div\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(11, \"div\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"label\", 13, 2);\n        i0.ɵɵprojection(14);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        const checkbox_r2 = i0.ɵɵreference(2);\n        i0.ɵɵproperty(\"labelPosition\", ctx.labelPosition);\n        i0.ɵɵadvance(4);\n        i0.ɵɵclassProp(\"mdc-checkbox--selected\", ctx.checked);\n        i0.ɵɵproperty(\"checked\", ctx.checked)(\"indeterminate\", ctx.indeterminate)(\"disabled\", ctx.disabled && !ctx.disabledInteractive)(\"id\", ctx.inputId)(\"required\", ctx.required)(\"tabIndex\", ctx.disabled && !ctx.disabledInteractive ? -1 : ctx.tabIndex);\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel || null)(\"aria-labelledby\", ctx.ariaLabelledby)(\"aria-describedby\", ctx.ariaDescribedby)(\"aria-checked\", ctx.indeterminate ? \"mixed\" : null)(\"aria-controls\", ctx.ariaControls)(\"aria-disabled\", ctx.disabled && ctx.disabledInteractive ? true : null)(\"aria-expanded\", ctx.ariaExpanded)(\"aria-owns\", ctx.ariaOwns)(\"name\", ctx.name)(\"value\", ctx.value);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"matRippleTrigger\", checkbox_r2)(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled)(\"matRippleCentered\", true);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"for\", ctx.inputId);\n      }\n    },\n    dependencies: [MatRipple, _MatInternalFormField],\n    styles: [\".mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom;padding:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2);margin:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox:hover>.mdc-checkbox__ripple{opacity:var(--mat-checkbox-unselected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mat-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:hover>.mat-mdc-checkbox-ripple>.mat-ripple-element{background-color:var(--mat-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-unselected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mat-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-unselected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mat-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mat-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mat-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-selected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mat-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control+.mdc-checkbox__ripple{background-color:var(--mat-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1;width:var(--mat-checkbox-state-layer-size, 40px);height:var(--mat-checkbox-state-layer-size, 40px);top:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2);right:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2);left:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox--disabled{cursor:default;pointer-events:none}@media(forced-colors: active){.mdc-checkbox--disabled{opacity:.5}}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1);-webkit-print-color-adjust:exact;color-adjust:exact;border-color:var(--mat-checkbox-unselected-icon-color, var(--mat-sys-on-surface-variant));top:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2);left:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2)}.mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled .mdc-checkbox__background{border-color:var(--mat-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox__native-control:disabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:disabled:indeterminate~.mdc-checkbox__background{background-color:var(--mat-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:checked)~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mat-checkbox-unselected-hover-icon-color, var(--mat-sys-on-surface));background-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-hover-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-hover-icon-color, var(--mat-sys-primary))}.mdc-checkbox__native-control:focus:focus:not(:checked)~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mat-checkbox-unselected-focus-icon-color, var(--mat-sys-on-surface))}.mdc-checkbox__native-control:focus:focus:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-focus-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-focus-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox:hover>.mdc-checkbox__native-control~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__background{border-color:var(--mat-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{background-color:var(--mat-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms cubic-bezier(0.4, 0, 0.6, 1);color:var(--mat-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:var(--mat-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}@media(forced-colors: active){.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);border-color:var(--mat-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled .mdc-checkbox__mixedmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__mixedmark{border-color:var(--mat-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark{transition:opacity 180ms cubic-bezier(0, 0, 0.2, 1),transform 180ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox .mat-internal-form-field{color:var(--mat-checkbox-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-checkbox-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-checkbox-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-checkbox-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-checkbox-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-checkbox-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive{pointer-events:auto}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive input{cursor:default}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default;color:var(--mat-checkbox-disabled-label-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-checkbox label:empty{display:none}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox .mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox .mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;left:50%;height:48px;width:48px;transform:translate(-50%, -50%);display:var(--mat-checkbox-touch-target-display, block)}.mat-mdc-checkbox .mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-focus-indicator::before{content:\\\"\\\"}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCheckbox, [{\n    type: Component,\n    args: [{\n      selector: 'mat-checkbox',\n      host: {\n        'class': 'mat-mdc-checkbox',\n        '[attr.tabindex]': 'null',\n        '[attr.aria-label]': 'null',\n        '[attr.aria-labelledby]': 'null',\n        '[class._mat-animation-noopable]': '_animationsDisabled',\n        '[class.mdc-checkbox--disabled]': 'disabled',\n        '[id]': 'id',\n        // Add classes that users can use to more easily target disabled or checked checkboxes.\n        '[class.mat-mdc-checkbox-disabled]': 'disabled',\n        '[class.mat-mdc-checkbox-checked]': 'checked',\n        '[class.mat-mdc-checkbox-disabled-interactive]': 'disabledInteractive',\n        '[class]': 'color ? \"mat-\" + color : \"mat-accent\"'\n      },\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => MatCheckbox),\n        multi: true\n      }, {\n        provide: NG_VALIDATORS,\n        useExisting: MatCheckbox,\n        multi: true\n      }],\n      exportAs: 'matCheckbox',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [MatRipple, _MatInternalFormField],\n      template: \"<div mat-internal-form-field [labelPosition]=\\\"labelPosition\\\" (click)=\\\"_preventBubblingFromLabel($event)\\\">\\n  <div #checkbox class=\\\"mdc-checkbox\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-checkbox-touch-target\\\" (click)=\\\"_onTouchTargetClick()\\\"></div>\\n    <input #input\\n           type=\\\"checkbox\\\"\\n           class=\\\"mdc-checkbox__native-control\\\"\\n           [class.mdc-checkbox--selected]=\\\"checked\\\"\\n           [attr.aria-label]=\\\"ariaLabel || null\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           [attr.aria-checked]=\\\"indeterminate ? 'mixed' : null\\\"\\n           [attr.aria-controls]=\\\"ariaControls\\\"\\n           [attr.aria-disabled]=\\\"disabled && disabledInteractive ? true : null\\\"\\n           [attr.aria-expanded]=\\\"ariaExpanded\\\"\\n           [attr.aria-owns]=\\\"ariaOwns\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [checked]=\\\"checked\\\"\\n           [indeterminate]=\\\"indeterminate\\\"\\n           [disabled]=\\\"disabled && !disabledInteractive\\\"\\n           [id]=\\\"inputId\\\"\\n           [required]=\\\"required\\\"\\n           [tabIndex]=\\\"disabled && !disabledInteractive ? -1 : tabIndex\\\"\\n           (blur)=\\\"_onBlur()\\\"\\n           (click)=\\\"_onInputClick()\\\"\\n           (change)=\\\"_onInteractionEvent($event)\\\"/>\\n    <div class=\\\"mdc-checkbox__ripple\\\"></div>\\n    <div class=\\\"mdc-checkbox__background\\\">\\n      <svg class=\\\"mdc-checkbox__checkmark\\\"\\n           focusable=\\\"false\\\"\\n           viewBox=\\\"0 0 24 24\\\"\\n           aria-hidden=\\\"true\\\">\\n        <path class=\\\"mdc-checkbox__checkmark-path\\\"\\n              fill=\\\"none\\\"\\n              d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\"/>\\n      </svg>\\n      <div class=\\\"mdc-checkbox__mixedmark\\\"></div>\\n    </div>\\n    <div class=\\\"mat-mdc-checkbox-ripple mat-focus-indicator\\\" mat-ripple\\n      [matRippleTrigger]=\\\"checkbox\\\"\\n      [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n      [matRippleCentered]=\\\"true\\\"></div>\\n  </div>\\n  <!--\\n    Avoid putting a click handler on the <label/> to fix duplicate navigation stop on Talk Back\\n    (#14385). Putting a click handler on the <label/> caused this bug because the browser produced\\n    an unnecessary accessibility tree node.\\n  -->\\n  <label class=\\\"mdc-label\\\" #label [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\",\n      styles: [\".mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom;padding:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2);margin:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox:hover>.mdc-checkbox__ripple{opacity:var(--mat-checkbox-unselected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mat-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:hover>.mat-mdc-checkbox-ripple>.mat-ripple-element{background-color:var(--mat-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-unselected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mat-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-unselected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mat-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mat-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mat-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-selected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mat-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control+.mdc-checkbox__ripple{background-color:var(--mat-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1;width:var(--mat-checkbox-state-layer-size, 40px);height:var(--mat-checkbox-state-layer-size, 40px);top:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2);right:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2);left:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox--disabled{cursor:default;pointer-events:none}@media(forced-colors: active){.mdc-checkbox--disabled{opacity:.5}}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1);-webkit-print-color-adjust:exact;color-adjust:exact;border-color:var(--mat-checkbox-unselected-icon-color, var(--mat-sys-on-surface-variant));top:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2);left:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2)}.mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled .mdc-checkbox__background{border-color:var(--mat-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox__native-control:disabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:disabled:indeterminate~.mdc-checkbox__background{background-color:var(--mat-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:checked)~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mat-checkbox-unselected-hover-icon-color, var(--mat-sys-on-surface));background-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-hover-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-hover-icon-color, var(--mat-sys-primary))}.mdc-checkbox__native-control:focus:focus:not(:checked)~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mat-checkbox-unselected-focus-icon-color, var(--mat-sys-on-surface))}.mdc-checkbox__native-control:focus:focus:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-focus-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-focus-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox:hover>.mdc-checkbox__native-control~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__background{border-color:var(--mat-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{background-color:var(--mat-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms cubic-bezier(0.4, 0, 0.6, 1);color:var(--mat-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:var(--mat-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}@media(forced-colors: active){.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);border-color:var(--mat-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled .mdc-checkbox__mixedmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__mixedmark{border-color:var(--mat-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark{transition:opacity 180ms cubic-bezier(0, 0, 0.2, 1),transform 180ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox .mat-internal-form-field{color:var(--mat-checkbox-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-checkbox-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-checkbox-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-checkbox-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-checkbox-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-checkbox-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive{pointer-events:auto}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive input{cursor:default}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default;color:var(--mat-checkbox-disabled-label-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-checkbox label:empty{display:none}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox .mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox .mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;left:50%;height:48px;width:48px;transform:translate(-50%, -50%);display:var(--mat-checkbox-touch-target-display, block)}.mat-mdc-checkbox .mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-focus-indicator::before{content:\\\"\\\"}\\n\"]\n    }]\n  }], () => [], {\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    ariaDescribedby: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    ariaExpanded: [{\n      type: Input,\n      args: [{\n        alias: 'aria-expanded',\n        transform: booleanAttribute\n      }]\n    }],\n    ariaControls: [{\n      type: Input,\n      args: ['aria-controls']\n    }],\n    ariaOwns: [{\n      type: Input,\n      args: ['aria-owns']\n    }],\n    id: [{\n      type: Input\n    }],\n    required: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    labelPosition: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    change: [{\n      type: Output\n    }],\n    indeterminateChange: [{\n      type: Output\n    }],\n    value: [{\n      type: Input\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    _inputElement: [{\n      type: ViewChild,\n      args: ['input']\n    }],\n    _labelElement: [{\n      type: ViewChild,\n      args: ['label']\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? undefined : numberAttribute(value)\n      }]\n    }],\n    color: [{\n      type: Input\n    }],\n    disabledInteractive: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    checked: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    indeterminate: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass MatCheckboxModule {\n  static ɵfac = function MatCheckboxModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCheckboxModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatCheckboxModule,\n    imports: [MatCheckbox, MatCommonModule],\n    exports: [MatCheckbox, MatCommonModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCheckbox, MatCommonModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCheckboxModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCheckbox, MatCommonModule],\n      exports: [MatCheckbox, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MAT_CHECKBOX_DEFAULT_OPTIONS, MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY, MatCheckbox, MatCheckboxChange, MatCheckboxModule, TransitionCheckState };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,IAAM,MAAM,CAAC,OAAO;AACpB,IAAM,MAAM,CAAC,OAAO;AACpB,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,+BAA+B,IAAI,eAAe,gCAAgC;AAAA,EACtF,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAMD,SAAS,uCAAuC;AAC9C,SAAO;AAAA,IACL,OAAO;AAAA,IACP,aAAa;AAAA,IACb,qBAAqB;AAAA,EACvB;AACF;AAMA,IAAI;AAAA,CACH,SAAUA,uBAAsB;AAE/B,EAAAA,sBAAqBA,sBAAqB,MAAM,IAAI,CAAC,IAAI;AAEzD,EAAAA,sBAAqBA,sBAAqB,SAAS,IAAI,CAAC,IAAI;AAE5D,EAAAA,sBAAqBA,sBAAqB,WAAW,IAAI,CAAC,IAAI;AAE9D,EAAAA,sBAAqBA,sBAAqB,eAAe,IAAI,CAAC,IAAI;AACpE,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AAEtD,IAAM,oBAAN,MAAwB;AAAA;AAAA,EAEtB;AAAA;AAAA,EAEA;AACF;AAEA,IAAM,WAAW,qCAAqC;AACtD,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,cAAc,OAAO,UAAU;AAAA,EAC/B,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,UAAU,OAAO,MAAM;AAAA,EACvB,sBAAsB,oBAAoB;AAAA,EAC1C,WAAW,OAAO,8BAA8B;AAAA,IAC9C,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED,QAAQ;AACN,SAAK,cAAc,cAAc,MAAM;AAAA,EACzC;AAAA;AAAA,EAEA,mBAAmB,WAAW;AAC5B,UAAM,QAAQ,IAAI,kBAAkB;AACpC,UAAM,SAAS;AACf,UAAM,UAAU;AAChB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,6BAA6B;AAC3B,WAAO,KAAK,eAAe;AAAA,EAC7B;AAAA;AAAA,EAEA,oBAAoB;AAAA,IAClB,oBAAoB;AAAA,IACpB,0BAA0B;AAAA,IAC1B,oBAAoB;AAAA,IACpB,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,0BAA0B;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AAAA;AAAA;AAAA;AAAA,EAIZ,iBAAiB;AAAA;AAAA,EAEjB;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,GAAG,KAAK,MAAM,KAAK,SAAS;AAAA,EACrC;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,gBAAgB;AAAA;AAAA,EAEhB,OAAO;AAAA;AAAA,EAEP,SAAS,IAAI,aAAa;AAAA;AAAA,EAE1B,sBAAsB,IAAI,aAAa;AAAA;AAAA,EAEvC;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,MAAM;AAAA,EAAC;AAAA,EACpB,yBAAyB;AAAA,EACzB,qBAAqB,qBAAqB;AAAA,EAC1C,gCAAgC,MAAM;AAAA,EAAC;AAAA,EACvC,qBAAqB,MAAM;AAAA,EAAC;AAAA,EAC5B,cAAc;AACZ,WAAO,sBAAsB,EAAE,KAAK,uBAAuB;AAC3D,UAAM,WAAW,OAAO,IAAI,mBAAmB,UAAU,GAAG;AAAA,MAC1D,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,WAAW,KAAK,YAAY;AACjC,SAAK,QAAQ,KAAK,SAAS,SAAS,SAAS;AAC7C,SAAK,WAAW,YAAY,OAAO,IAAI,SAAS,QAAQ,KAAK;AAC7D,SAAK,KAAK,KAAK,YAAY,OAAO,YAAY,EAAE,MAAM,mBAAmB;AACzE,SAAK,sBAAsB,KAAK,UAAU,uBAAuB;AAAA,EACnE;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,UAAU,GAAG;AACvB,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,SAAK,mBAAmB,KAAK,aAAa;AAAA,EAC5C;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,QAAI,SAAS,KAAK,SAAS;AACzB,WAAK,WAAW;AAChB,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAAA,EACF;AAAA,EACA,WAAW;AAAA;AAAA,EAEX,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,QAAI,UAAU,KAAK,UAAU;AAC3B,WAAK,YAAY;AACjB,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAAA,EACF;AAAA,EACA,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOZ,IAAI,gBAAgB;AAClB,WAAO,KAAK,eAAe;AAAA,EAC7B;AAAA,EACA,IAAI,cAAc,OAAO;AACvB,UAAM,UAAU,SAAS,KAAK,eAAe;AAC7C,SAAK,eAAe,IAAI,KAAK;AAC7B,QAAI,SAAS;AACX,UAAI,OAAO;AACT,aAAK,sBAAsB,qBAAqB,aAAa;AAAA,MAC/D,OAAO;AACL,aAAK,sBAAsB,KAAK,UAAU,qBAAqB,UAAU,qBAAqB,SAAS;AAAA,MACzG;AACA,WAAK,oBAAoB,KAAK,KAAK;AAAA,IACrC;AACA,SAAK,mBAAmB,KAAK;AAAA,EAC/B;AAAA,EACA,iBAAiB,OAAO,KAAK;AAAA,EAC7B,oBAAoB;AAClB,WAAO,KAAK,iBAAiB,KAAK;AAAA,EACpC;AAAA;AAAA,EAEA,qBAAqB;AAMnB,SAAK,mBAAmB,cAAc;AAAA,EACxC;AAAA;AAAA,EAEA,WAAW,OAAO;AAChB,SAAK,UAAU,CAAC,CAAC;AAAA,EACnB;AAAA;AAAA,EAEA,iBAAiB,IAAI;AACnB,SAAK,gCAAgC;AAAA,EACvC;AAAA;AAAA,EAEA,kBAAkB,IAAI;AACpB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA,EAEA,iBAAiB,YAAY;AAC3B,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,SAAS,SAAS;AAChB,WAAO,KAAK,YAAY,QAAQ,UAAU,OAAO;AAAA,MAC/C,YAAY;AAAA,IACd,IAAI;AAAA,EACN;AAAA;AAAA,EAEA,0BAA0B,IAAI;AAC5B,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,sBAAsB,UAAU;AAC9B,QAAI,WAAW,KAAK;AACpB,QAAI,UAAU,KAAK,2BAA2B;AAC9C,QAAI,aAAa,YAAY,CAAC,SAAS;AACrC;AAAA,IACF;AACA,QAAI,KAAK,wBAAwB;AAC/B,cAAQ,UAAU,OAAO,KAAK,sBAAsB;AAAA,IACtD;AACA,SAAK,yBAAyB,KAAK,0CAA0C,UAAU,QAAQ;AAC/F,SAAK,qBAAqB;AAC1B,QAAI,KAAK,uBAAuB,SAAS,GAAG;AAC1C,cAAQ,UAAU,IAAI,KAAK,sBAAsB;AAEjD,YAAM,iBAAiB,KAAK;AAC5B,WAAK,QAAQ,kBAAkB,MAAM;AACnC,mBAAW,MAAM;AACf,kBAAQ,UAAU,OAAO,cAAc;AAAA,QACzC,GAAG,GAAI;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,SAAK,8BAA8B,KAAK,OAAO;AAC/C,SAAK,OAAO,KAAK,KAAK,mBAAmB,KAAK,OAAO,CAAC;AAGtD,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,cAAc,UAAU,KAAK;AAAA,IAClD;AAAA,EACF;AAAA;AAAA,EAEA,SAAS;AACP,SAAK,UAAU,CAAC,KAAK;AACrB,SAAK,8BAA8B,KAAK,OAAO;AAAA,EACjD;AAAA,EACA,oBAAoB;AAClB,UAAM,cAAc,KAAK,UAAU;AAEnC,QAAI,CAAC,KAAK,YAAY,gBAAgB,QAAQ;AAE5C,UAAI,KAAK,iBAAiB,gBAAgB,SAAS;AACjD,gBAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,eAAK,eAAe,IAAI,KAAK;AAC7B,eAAK,oBAAoB,KAAK,KAAK;AAAA,QACrC,CAAC;AAAA,MACH;AACA,WAAK,WAAW,CAAC,KAAK;AACtB,WAAK,sBAAsB,KAAK,WAAW,qBAAqB,UAAU,qBAAqB,SAAS;AAIxG,WAAK,iBAAiB;AAAA,IACxB,WAAW,KAAK,YAAY,KAAK,uBAAuB,CAAC,KAAK,YAAY,gBAAgB,QAAQ;AAGhG,WAAK,cAAc,cAAc,UAAU,KAAK;AAChD,WAAK,cAAc,cAAc,gBAAgB,KAAK;AAAA,IACxD;AAAA,EACF;AAAA,EACA,oBAAoB,OAAO;AAIzB,UAAM,gBAAgB;AAAA,EACxB;AAAA,EACA,UAAU;AAMR,YAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,WAAK,WAAW;AAChB,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AAAA,EACH;AAAA,EACA,0CAA0C,UAAU,UAAU;AAE5D,QAAI,KAAK,qBAAqB;AAC5B,aAAO;AAAA,IACT;AACA,YAAQ,UAAU;AAAA,MAChB,KAAK,qBAAqB;AAGxB,YAAI,aAAa,qBAAqB,SAAS;AAC7C,iBAAO,KAAK,kBAAkB;AAAA,QAChC,WAAW,YAAY,qBAAqB,eAAe;AACzD,iBAAO,KAAK,WAAW,KAAK,kBAAkB,yBAAyB,KAAK,kBAAkB;AAAA,QAChG;AACA;AAAA,MACF,KAAK,qBAAqB;AACxB,eAAO,aAAa,qBAAqB,UAAU,KAAK,kBAAkB,qBAAqB,KAAK,kBAAkB;AAAA,MACxH,KAAK,qBAAqB;AACxB,eAAO,aAAa,qBAAqB,YAAY,KAAK,kBAAkB,qBAAqB,KAAK,kBAAkB;AAAA,MAC1H,KAAK,qBAAqB;AACxB,eAAO,aAAa,qBAAqB,UAAU,KAAK,kBAAkB,yBAAyB,KAAK,kBAAkB;AAAA,IAC9H;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,mBAAmB,OAAO;AACxB,UAAM,iBAAiB,KAAK;AAC5B,QAAI,gBAAgB;AAClB,qBAAe,cAAc,gBAAgB;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,sBAAsB;AACpB,SAAK,kBAAkB;AACvB,QAAI,CAAC,KAAK,UAAU;AAGlB,WAAK,cAAc,cAAc,MAAM;AAAA,IACzC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,0BAA0B,OAAO;AAC/B,QAAI,CAAC,CAAC,MAAM,UAAU,KAAK,cAAc,cAAc,SAAS,MAAM,MAAM,GAAG;AAC7E,YAAM,gBAAgB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,WAAW,SAAS,kBAAkB,IAAI,KAAK;AAC7C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AACpE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AAAA,MACtE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,kBAAkB;AAAA,IACjC,UAAU;AAAA,IACV,cAAc,SAAS,yBAAyB,IAAI,KAAK;AACvD,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,MAAM,IAAI,EAAE;AAC7B,QAAG,YAAY,YAAY,IAAI,EAAE,cAAc,IAAI,EAAE,mBAAmB,IAAI;AAC5E,QAAG,WAAW,IAAI,QAAQ,SAAS,IAAI,QAAQ,YAAY;AAC3D,QAAG,YAAY,2BAA2B,IAAI,mBAAmB,EAAE,0BAA0B,IAAI,QAAQ,EAAE,6BAA6B,IAAI,QAAQ,EAAE,4BAA4B,IAAI,OAAO,EAAE,yCAAyC,IAAI,mBAAmB;AAAA,MACjQ;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,WAAW,CAAC,GAAG,cAAc,WAAW;AAAA,MACxC,gBAAgB,CAAC,GAAG,mBAAmB,gBAAgB;AAAA,MACvD,iBAAiB,CAAC,GAAG,oBAAoB,iBAAiB;AAAA,MAC1D,cAAc,CAAC,GAAG,iBAAiB,gBAAgB,gBAAgB;AAAA,MACnE,cAAc,CAAC,GAAG,iBAAiB,cAAc;AAAA,MACjD,UAAU,CAAC,GAAG,aAAa,UAAU;AAAA,MACrC,IAAI;AAAA,MACJ,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,eAAe;AAAA,MACf,MAAM;AAAA,MACN,OAAO;AAAA,MACP,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,MACrE,UAAU,CAAC,GAAG,YAAY,YAAY,WAAS,SAAS,OAAO,SAAY,gBAAgB,KAAK,CAAC;AAAA,MACjG,OAAO;AAAA,MACP,qBAAqB,CAAC,GAAG,uBAAuB,uBAAuB,gBAAgB;AAAA,MACvF,SAAS,CAAC,GAAG,WAAW,WAAW,gBAAgB;AAAA,MACnD,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,IACvE;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,qBAAqB;AAAA,IACvB;AAAA,IACA,UAAU,CAAC,aAAa;AAAA,IACxB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa,WAAW,MAAM,YAAW;AAAA,MACzC,OAAO;AAAA,IACT,GAAG;AAAA,MACD,SAAS;AAAA,MACT,aAAa;AAAA,MACb,OAAO;AAAA,IACT,CAAC,CAAC,GAAM,oBAAoB;AAAA,IAC5B,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,YAAY,EAAE,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,2BAA2B,IAAI,GAAG,SAAS,eAAe,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG,iCAAiC,GAAG,OAAO,GAAG,CAAC,QAAQ,YAAY,GAAG,gCAAgC,GAAG,QAAQ,SAAS,UAAU,WAAW,iBAAiB,YAAY,MAAM,YAAY,UAAU,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,aAAa,SAAS,WAAW,aAAa,eAAe,QAAQ,GAAG,yBAAyB,GAAG,CAAC,QAAQ,QAAQ,KAAK,oCAAoC,GAAG,8BAA8B,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,cAAc,IAAI,GAAG,2BAA2B,uBAAuB,GAAG,oBAAoB,qBAAqB,mBAAmB,GAAG,CAAC,GAAG,aAAa,GAAG,KAAK,CAAC;AAAA,IAChyB,UAAU,SAAS,qBAAqB,IAAI,KAAK;AAC/C,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,WAAW,SAAS,SAAS,0CAA0C,QAAQ;AAChF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,0BAA0B,MAAM,CAAC;AAAA,QAC7D,CAAC;AACD,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;AAC7C,QAAG,WAAW,SAAS,SAAS,4CAA4C;AAC1E,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,oBAAoB,CAAC;AAAA,QACjD,CAAC;AACD,QAAG,aAAa;AAChB,QAAG,eAAe,GAAG,SAAS,GAAG,CAAC;AAClC,QAAG,WAAW,QAAQ,SAAS,6CAA6C;AAC1E,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,QAAQ,CAAC;AAAA,QACrC,CAAC,EAAE,SAAS,SAAS,8CAA8C;AACjE,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,cAAc,CAAC;AAAA,QAC3C,CAAC,EAAE,UAAU,SAAS,6CAA6C,QAAQ;AACzE,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,oBAAoB,MAAM,CAAC;AAAA,QACvD,CAAC;AACD,QAAG,aAAa;AAChB,QAAG,UAAU,GAAG,OAAO,CAAC;AACxB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,eAAe;AAClB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,QAAG,aAAa;AAChB,QAAG,gBAAgB;AACnB,QAAG,UAAU,IAAI,OAAO,EAAE;AAC1B,QAAG,aAAa;AAChB,QAAG,UAAU,IAAI,OAAO,EAAE;AAC1B,QAAG,aAAa;AAChB,QAAG,eAAe,IAAI,SAAS,IAAI,CAAC;AACpC,QAAG,aAAa,EAAE;AAClB,QAAG,aAAa,EAAE;AAAA,MACpB;AACA,UAAI,KAAK,GAAG;AACV,cAAM,cAAiB,YAAY,CAAC;AACpC,QAAG,WAAW,iBAAiB,IAAI,aAAa;AAChD,QAAG,UAAU,CAAC;AACd,QAAG,YAAY,0BAA0B,IAAI,OAAO;AACpD,QAAG,WAAW,WAAW,IAAI,OAAO,EAAE,iBAAiB,IAAI,aAAa,EAAE,YAAY,IAAI,YAAY,CAAC,IAAI,mBAAmB,EAAE,MAAM,IAAI,OAAO,EAAE,YAAY,IAAI,QAAQ,EAAE,YAAY,IAAI,YAAY,CAAC,IAAI,sBAAsB,KAAK,IAAI,QAAQ;AACrP,QAAG,YAAY,cAAc,IAAI,aAAa,IAAI,EAAE,mBAAmB,IAAI,cAAc,EAAE,oBAAoB,IAAI,eAAe,EAAE,gBAAgB,IAAI,gBAAgB,UAAU,IAAI,EAAE,iBAAiB,IAAI,YAAY,EAAE,iBAAiB,IAAI,YAAY,IAAI,sBAAsB,OAAO,IAAI,EAAE,iBAAiB,IAAI,YAAY,EAAE,aAAa,IAAI,QAAQ,EAAE,QAAQ,IAAI,IAAI,EAAE,SAAS,IAAI,KAAK;AACrY,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,oBAAoB,WAAW,EAAE,qBAAqB,IAAI,iBAAiB,IAAI,QAAQ,EAAE,qBAAqB,IAAI;AAChI,QAAG,UAAU;AACb,QAAG,WAAW,OAAO,IAAI,OAAO;AAAA,MAClC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,WAAW,qBAAqB;AAAA,IAC/C,QAAQ,CAAC,u6fAAy6f;AAAA,IACl7f,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,QACrB,0BAA0B;AAAA,QAC1B,mCAAmC;AAAA,QACnC,kCAAkC;AAAA,QAClC,QAAQ;AAAA;AAAA,QAER,qCAAqC;AAAA,QACrC,oCAAoC;AAAA,QACpC,iDAAiD;AAAA,QACjD,WAAW;AAAA,MACb;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,WAAW;AAAA,QACzC,OAAO;AAAA,MACT,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,QACb,OAAO;AAAA,MACT,CAAC;AAAA,MACD,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,WAAW,qBAAqB;AAAA,MAC1C,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,u6fAAy6f;AAAA,IACp7f,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW,WAAS,SAAS,OAAO,SAAY,gBAAgB,KAAK;AAAA,MACvE,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,aAAa,eAAe;AAAA,IACtC,SAAS,CAAC,aAAa,eAAe;AAAA,EACxC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,aAAa,iBAAiB,eAAe;AAAA,EACzD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,aAAa,eAAe;AAAA,MACtC,SAAS,CAAC,aAAa,eAAe;AAAA,IACxC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["TransitionCheckState"]}