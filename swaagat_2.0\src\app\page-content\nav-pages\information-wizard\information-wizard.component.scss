:root {
  --primary-color: #2563eb;
  --secondary-color: #f97316;
  --text-color: #1f2937;
  --border-color: #d1d5db;
  --background-color: #f8fafc;
  --white: #ffffff;
  --blue-900: #1e3a8a;
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.05);
  --transition: all 0.3s ease;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--background-color);
  margin: 0;
  padding: 2rem;
  line-height: 1.6;
  color: var(--text-color);
}

.container {
  max-width: 960px;
  margin: 0 auto;
  background: var(--white);
  padding: 2.5rem;
  border-radius: 1rem;
  box-shadow: var(--shadow);
  transition: var(--transition);
}

h1 {
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--blue-900);
  margin-bottom: 2rem;
  letter-spacing: -0.025em;
  text-align: center;
}

h2 {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1.75rem;
  letter-spacing: -0.02em;
}

.form-group {
  margin-bottom: 1.5rem;
}

.full-width {
  width: 100%;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--text-color);
  font-size: 0.95rem;
  letter-spacing: 0.01em;
}

.required {
  color: #dc2626;
  font-weight: 600;
}

select {
  width: 100%;
  padding: 0.875rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  font-size: 0.95rem;
  background: var(--white);
  color: var(--text-color); /* Set dropdown text color to black (text-color) */
  transition: var(--transition);
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.875rem center;
  background-size: 1.25rem;
}

select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

select:hover {
  border-color: var(--primary-color);
}

.buttons {
  display: flex;
  gap: 1rem;
  margin-top: 2.5rem;
  justify-content: flex-end;
}

button {
  padding: 0.875rem 2rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  min-width: 140px;
  background: var(--secondary-color); /* Apply background color by default for reset button */
  color: var(--white);
}

button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

button:active {
  transform: translateY(0);
}

.reset {
  background: var(--secondary-color); /* Ensure reset button has background color */
}

.reset:hover {
  background: #ea580c; /* Slightly darker shade for hover */
}

.search {
  background: var(--blue-900); /* Ensure search button has background color */
}

.search:hover {
  background: #1e40af; /* Slightly lighter shade for hover */
}

@media (max-width: 768px) {
  body {
    padding: 1.5rem;
  }

  .container {
    padding: 2rem;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .buttons {
    flex-direction: column;
    align-items: stretch;
  }

  button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  h1 {
    font-size: 1.875rem;
  }

  .container {
    padding: 1.5rem;
  }

  select, button {
    font-size: 0.9rem;
  }
}