/* Updated CSS for synchronized scrolling */
.dynamic-table-container {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  // background: white;
  border-radius: 12px;
  // box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden; /* This can clip dropdowns */
  margin: 0 auto 12px auto;
  color: #333;
  display: flex;
  flex-direction: column;
  max-height: 800px; /* Combined with overflow: hidden, this can cause clipping */
  position: relative; /* Establish positioning context */
}
table{
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

}
// table th{
//   font-size: 14px;
// }

.table-controls {
  border-radius: 12px;
  padding: 0 0 16px 0;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;

  .search-box {
    max-width: 400px;
    // margin: 0 auto;

    .search-input {
      width: 100%;
      padding: 10px 14px;
      border: 1px solid #ced4da;
      border-radius: 8px;
      font-size: 14px;
      outline: none;
      transition: border-color 0.2s ease;
      background-color: #fff;
      color: #000;

      &:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
      }
    }
  }
}

.table-wrapper {
  // border-radius: 12px;
  flex: 1;
  overflow: auto; /* This is the main cause of dropdown clipping if not using CDK overlay */
  position: relative;
  border-bottom: 1px solid #e9ecef;
  min-height: 0;

  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }
  &::-webkit-scrollbar-thumb {
    background: #adb5bd;
    border-radius: 4px;
  }
  &::-webkit-scrollbar-thumb:hover {
    background: #6c757d;
  }
}

.dynamic-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 800px;
  // background: white;

  th {
    background: #cfe2ff;
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    font-size: 0.85rem;
    color: #000;
    border-bottom: 2px solid #dee2e6;
    position: sticky;
    top: 0;
    z-index: 10;
    white-space: nowrap;
    min-width: 120px;
    box-sizing: border-box;

    &.sortable {
      cursor: pointer;
      user-select: none;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: rgb(7, 10, 83);
      }
    }
  }

  td {
    padding: 16px;
    font-size: 0.85rem;
    vertical-align: middle;
    border-bottom: 1px solid #e9ecef;
    white-space: nowrap;
    min-width: 120px;
    box-sizing: border-box;
    text-align: start;
  }

  tbody tr:nth-child(even) {
    background-color: #fcfcfc;
  }

  tbody tr:hover {
    background-color: #f8f9ff;
  }
}

.th-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.sort-icon {
  opacity: 0.5;
  transition: opacity 0.2s ease;
}

.sortable:hover .sort-icon {
  opacity: 1;
}

.action-dropdown {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;

  .btn-more {
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: background-color 0.2s;
    background: none;
    border: none;
    color: inherit;

    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
    }
  }
}

.dropdown-item {
}

.badge,
td .badge {
  padding: 4px 10px !important;
  border-radius: 20px !important;
  font-size: 11px !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  display: inline-block !important;
  min-width: 60px !important;
  text-align: center !important;
  white-space: nowrap !important;
}

.badge-success,
td .badge-success {
  background-color: #d1f7e5;
  color: #0a7a4a;
  text-align: center;
  border-radius: 12px;
}
.badge-danger,
td .badge-danger {
  background-color: #ffe8e8;
  color: #c00;
}
.badge-warning,
td .badge-warning {
  background-color: #fff3cd;
  color: #856404;
}
.badge-info,
td .badge-info {
  background-color: #e8f4fd;
  color: #0066cc;
}
.badge-caution,
td .badge-caution {
  background-color: #fff4e6;
  color: #b36a00;
}
.badge-submitted,
td .badge-submitted {
  background-color: #e2e2ff;
  color: #3b3bff;
}
.badge-unknown,
td .badge-unknown {
  background-color: #e9ecef;
  color: #495057;
}

.text-currency {
  color: #166534;
  font-weight: 600;
}

.text-muted {
  color: #888;
  font-style: italic;
}

.table-link {
  color: #007bff;
  text-decoration: none;
  font-weight: 500;
  white-space: nowrap;
  &:hover {
    text-decoration: underline;
  }
}

.table-file {
  color: #7c3aed;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  text-decoration: none;
  &:hover {
    text-decoration: underline;
  }
}

.no-data {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;

  .empty-state {
    svg {
      opacity: 0.6;
      margin-bottom: 8px;
    }
    p {
      margin: 0;
      font-size: 15px;
    }
  }
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  font-size: 14px;
  color: #495057;
  flex-shrink: 0;

  @media (max-width: 600px) {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
}

.pagination-info {
  color: #6c757d;
  strong {
    color: #333;
    font-weight: 600;
  }
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: nowrap;
}

.btn-pagination {
  padding: 10px 14px;
  font-size: 14px;
  border: 1px solid #dee2e6;
  background: white;
  border-radius: 8px;
  min-width: 110px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.2s ease;
  cursor: pointer;

  &.page {
    min-width: 40px;
  }

  &.active,
  &:hover:not(:disabled) {
    background: #007bff;
    color: white;
    border-color: #007bff;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .icon {
    font-size: 12px;
  }
  .label {
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .dynamic-table-container {
    margin: 12px;
    border-radius: 8px;
  }
  .table-controls {
    padding: 12px;
  }
  .pagination-container {
    padding: 12px;
    font-size: 13px;
  }
  .dynamic-table th,
  .dynamic-table td {
    padding: 10px 12px;
    font-size: 13px;
  }
}

.badge {
  display: inline-block;
  padding: 0.25em 0.6em;
  font-size: 0.8em;
  font-weight: 600;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  border-radius: 12px;
  background-color: #e0e0e0;
  color: #333;
  min-width: 60px;
}

.badge-success {
  background-color: #d1f7e3;
  color: #0a7a3d;
}
.badge-warning {
  background-color: #fff4d6;
  color: #926500;
}
.badge-danger {
  background-color: #ffe5e5;
  color: #a00;
}
.badge-primary {
  background-color: #e3f2fd;
  color: #0d47a1;
}
.badge-info {
  background-color: #e3f2fd;
  color: #0d47a1;
}
.badge-muted {
  background-color: #f5f5f5;
  color: #666;
}
.badge-default {
  background-color: #f0f0f0;
  color: #555;
}
.badge-accent {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.text-currency {
  font-family: "Courier New", monospace;
  font-weight: 600;
  color: #1b5e20;
}

.table-link {
  color: #1976d2;
  text-decoration: none;
  &:hover {
    text-decoration: underline;
  }
}

.table-file {
  color: #555;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}
// table.component.scss

.input-large-custom.wid-cus {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4px;
  min-height: 40px;
}

.badge {
  display: inline-block;
  padding: 0.25em 0.6em;
  font-size: 0.85em;
  font-weight: 600;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-success {
  background-color: #d1f7e3;
  color: #0a7a3d;
}
.badge-warning {
  background-color: #fff4d6;
  color: #926500;
}
.badge-danger {
  background-color: #ffe5e5;
  color: #c62828;
}
.badge-primary {
  background-color: #e3f2fd;
  color: #0d47a1;
}

// Make action/status cells look better
.text-center,
.status,
.payment {
  text-align: center;
}

.table-red-headers thead th.sortable:hover {
  background-color: #cfe2ff;
}

::ng-deep .mat-menu {
  background-color: white !important;
  border: 1px solid #ddd !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  min-width: 180px !important;
  font-size: 14px !important;
}

::ng-deep .mat-menu-item {
  padding: 10px 16px !important;
  color: #333 !important;
  transition: background-color 0.2s ease;
}

::ng-deep .mat-menu-item:hover {
  background-color: #f0f0f0 !important;
  color: #1a1a1a !important;
}