export const CompanyDetailsErrorMessages = {
  landtype1: {
    "required": "Land Type is required."
  },
  landtype2: {
    "required": "Land Type Text is required.",
    "isValidated": "', \", <, > are not allowed.",
  },
  cou: {
    "required": "Constitution of the Enterprise is required."
  },
  cname: {
    "required": "Enterprise Name is required."
  },
  pin: {
    "required": "Pin is required.",
    "minlength": "Pin must be of 6 digits.",
    "maxlength": "Pin must be of 6 digits.",
    "isValidated": "Pin should be numeric."
  },
  gp_ward: {
    "required": "GP/ Ward/ VC is required.",
    "isValidated": "', \", <, > are not allowed.",
  },
  post_office: {
    "required": "Post Office is required.",
    "isValidated": "', \", <, > are not allowed.",
  },
  area_building: {
    "required": "Habitation/ Area/ Building is required.",
    "isValidated": "', \", <, > are not allowed.",
  },
  address: {
    "required": "Enterprise's Registered Address is required.",
    "isValidated": "', \", <, > are not allowed.",
  },
  police_stn: {
    "required": "Police Station is required.",
    "isValidated": "', \", <, > are not allowed.",
  },
  district: {
    "required": "District is required."
  },
  subdiv: {
    "required": "Sub-division is required."
  },
  name: {
    "required": "Name is required."
  },
  desg: {
    "required": "Designation is required.",
    "isValidated": "', \", <, > are not allowed.",
  },
  aadhar_no: {
    "required": "Aadhar No is required.",
    "minlength": "Aadhar No must be of 12 character long.",
    "maxlength": "Aadhar No must be of 12 character long.",
    "isValidated": "Please enter numeric value only."
  },
  pan_no: {
    "required": "Pan No is required.",
    "minlength": "Pan No must be of 10 character long.",
    "maxlength": "Pan No No must be of 10 character long.",
    "isValidated": "Please enter Alpha Numeric value only."
  },
  mobile: {
    "required": "Mobile No is required."
  },
  email: {
    "required": "Email Id is required."
  },
  alt_mobile_no: {
    "required": "Alternate Mobile No is required.",
    "minlength": "Alternate Mobile No must be of 10 digits long.",
    "maxlength": "Alternate Mobile No must be of 10 digits long.",
    "isValidated": "Please enter numeric value only."
  },
  phone_no: {
    "required": "Phone No is required.",
    "minlength": "Phone No must be of 12 digits long.",
    "maxlength": "Phone No must be of 12 digits long.",
    "isValidated": "Please enter numeric value only."
  },
  proposal_for: {
    "required": "Proposal for is required."
  },
  proposed_date: {
    "required": "Proposed Date of Commissioning is required."
  },
}