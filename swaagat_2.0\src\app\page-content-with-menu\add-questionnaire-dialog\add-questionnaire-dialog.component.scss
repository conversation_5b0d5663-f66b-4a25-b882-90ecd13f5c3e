.form-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.form-field {
  min-width: 160px;
}

::ng-deep .mat-mdc-form-field {
  font-size: 13px !important;
  margin: 4px 0 !important;
}

::ng-deep .mat-mdc-text-field-wrapper {
  min-height: 38px !important;
  border-radius: 6px !important;
  background: #fafafa !important;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

::ng-deep .mat-mdc-input-element {
  font-size: 13px !important;
  padding: 2px 0 !important;
}

::ng-deep .mat-mdc-floating-label {
  font-size: 12px !important;
  color: #555 !important;
}

.service-info {
  margin: 8px 0;
  padding: 10px;
  background: linear-gradient(135deg, #f5f7fa, #edf0f5);
  border-radius: 6px;
  font-size: 13px;
  text-align: center;
  color: #333;
  font-weight: 500;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
  padding-bottom: 6px;
}

.dialog-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.validation-section {
  margin-top: 14px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: #fcfcfc;
}

.validation-section h3 {
  font-size: 14px;
  font-weight: 600;
  color: #444;
  margin-bottom: 10px;
}

.validation-row {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.minmax-field {
  flex: 1 1 80px;
}

.pattern-field {
  flex: 2 1 120px;
}

.error-field {
  flex: 3 1 160px;
}

@media (max-width: 768px) {
  .validation-row mat-form-field {
    flex: 1 1 100%;
  }
}

.preview-section {
  margin-top: 18px;
  padding: 14px;
  background: #fdfdfd;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.submit-btn {
  background-color: #1976d2;
  color: #fff;
}

.submit-btn:hover {
  background-color: #1565c0;
}

.snackbar-success {
  background: #4caf50 !important;
  color: #fff !important;
}

.snackbar-error {
  background: #f44336 !important;
  color: #fff !important;
}

.preview-section h3 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 10px;
  color: #222;
}

mat-error {
  font-size: 12px;
  color: #d32f2f !important;
}

::ng-deep .mat-mdc-select-panel {
  background: #fff !important;
}

.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.form-row {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.form-field {
  flex: 1;
}

.rule-field {
  flex: 1.2;
}

.minmax-field {
  flex: 0.5;
}

.pattern-field {
  flex: 1;
}

.error-field {
  flex: 2;
}

mat-form-field {
  font-size: 13px;
}

mat-dialog-actions button {
  min-width: 90px;
  font-size: 13px;
  border-radius: 6px !important;
}

@media (max-width: 992px) {
  .form-row {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 600px) {
  .form-row {
    grid-template-columns: 1fr;
  }
}