<!-- new-notification.component.html -->
<section class="news-ticker">
  <div class="bg-decorations">
    <div class="bg-decoration-1"></div>
    <div class="bg-decoration-2"></div>
    <div class="bg-decoration-3"></div>
  </div>
  
  <div class="container">
    <div class="ticker-content">
      <!-- Header -->
      <div class="ticker-header">
        <div class="bell-container">
          <svg class="bell-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path>
            <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"></path>
          </svg>
          <div class="notification-dot"></div>
          <div class="notification-dot-static"></div>
        </div>
        <div class="header-text">
          <h3>Latest Updates</h3>
          <div class="live-indicator">Live • Real-time</div>
        </div>
      </div>

      <!-- Ticker Container -->
      <div class="ticker-container">
        <div class="ticker-wrapper">
          <div class="ticker-track" [style.transform]="'translateX(-' + (currentIndex * 100) + '%)'">
            @for (news of newsItems; track news.text) {
              <div class="news-item">
                <div class="news-card" (click)="onCardClick(news)">
                  @if (news.isUrgent) {
                    <div class="urgent-badge-container">
                      <span class="urgent-badge">URGENT</span>
                      <div class="urgent-badge-glow"></div>
                    </div>
                  }
                <div class="category-container">
                  <div class="category-icon {{news.category.iconClass}}">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" [innerHTML]="news.category.iconSvg">
                    </svg>
                  </div>
                  <span class="category-badge {{news.category.badgeClass}}">{{news.category.badgeText}}</span>
                </div>
                <div class="news-content">
                  <p class="news-text">{{news.text}}</p>
                  <div class="news-date">
                    <svg class="calendar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M8 2v4"></path>
                      <path d="M16 2v4"></path>
                      <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                      <path d="M3 10h18"></path>
                    </svg>
                    <span class="date-text">{{news.date}}</span>
                  </div>
                </div>
                  <div class="external-link-container">
                    <button class="external-link-btn" (click)="onExternalLinkClick(news); $event.stopPropagation()">
                      <svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M15 3h6v6"></path>
                        <path d="M10 14 21 3"></path>
                        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            }
          </div>
        </div>
      </div>

      <!-- Controls -->
      <div class="ticker-controls">
        <div class="pagination-dots">
          @for (news of newsItems; track news.text; let i = $index) {
            <button class="dot" [class.active]="i === currentIndex" (click)="setIndex(i)" [attr.aria-label]="'Go to announcement ' + (i + 1)"></button>
          }
        </div>
        <button class="pause-btn" (click)="togglePause()" [attr.aria-label]="isPaused ? 'Play' : 'Pause'">
          @if (!isPaused) {
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"></path>
            </svg>
          }
          @if (isPaused) {
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <polygon points="5 3 19 12 5 21 5 3"></polygon>
            </svg>
          }
        </button>
      </div>
    </div>
  </div>
</section>
