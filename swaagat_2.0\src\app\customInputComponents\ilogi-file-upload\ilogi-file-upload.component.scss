.ilogi-file-upload {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 0.85rem;
  color: #333;

  .file-display {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 8px;
    background-color: #f8f9fa;
    border: 1px dashed #ccc;
    border-radius: 4px;
    max-width: 100%;
    box-sizing: border-box;

    .filename {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-weight: 500;
    }

    .remove-btn {
      background: #dc3545;
      color: white;
      border: none;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      font-size: 12px;
      cursor: pointer;
      transition: background 0.2s;

      &:hover:not(:disabled) {
        background: #c82333;
      }
    }
  }

  .upload-area {
    .file-input-wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 6px 8px;
      border: 1px dashed #ccc;
      border-radius: 4px;
      min-height: 32px;
      max-width: 100%;
      box-sizing: border-box;

      .placeholder-text {
        flex: 1;
        color: #6c757d;
        font-size: 0.85rem;
        padding-right: 8px;
      }

      .browse-btn {
        background: transparent;
        color: #007bff;
        border: 1px solid #007bff;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.85rem;
        cursor: pointer;
        transition: all 0.2s;

        &:hover:not(:disabled) {
          background: #e9f5ff;
          border-color: #0056b3;
          color: #0056b3;
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }

    .error-msg {
      margin-top: 6px;
      font-size: 0.75rem;
      color: #d32f2f;
    }
  }

  .hidden-input {
    position: absolute;
    left: -9999px;
    opacity: 0;
    pointer-events: none;
  }

  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }
}