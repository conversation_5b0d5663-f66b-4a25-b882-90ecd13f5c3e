// .form-group {
//   margin-bottom: 1.25rem;
//   position: relative;
// }

// label {
//   display: block;
//   margin-bottom: 0.5rem;
//   font-weight: 500;
//   color: var(--color-text-dark, #333);
// }

// .required-indicator {
//   color: var(--color-poppy-red, #dc3545);
//   margin-left: 0.25rem;
// }

// .readonly-display {
//   padding: 0.75rem 1rem;
//   background-color: var(--color-background-light, #f8f9fa);
//   border: 1px solid var(--color-border, #ced4da);
//   border-radius: 6px;
//   font-size: 1rem;
//   color: var(--color-text-dark, #333);
//   min-height: calc(1.5em + 0.75rem + 2px);
//   word-break: break-word;
// }

// .select-form-field {
//   width: 100%;

//   ::ng-deep .mat-mdc-text-field-wrapper {
//     border-radius: 6px;
//     box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
//   }

//   ::ng-deep .mat-mdc-form-field-focus-overlay {
//     border-radius: 6px;
//   }

//   &.is-invalid {
//     ::ng-deep .mat-mdc-text-field-wrapper {
//       border-color: var(--color-poppy-red, #dc3545) !important;
//     }
//   }

//   ::ng-deep .mat-mdc-form-field-infix {
//     padding-top: 0.75rem;
//     padding-bottom: 0.75rem;
//   }

//   ::ng-deep .mat-mdc-form-field-subscript-wrapper {
//     display: none; // Hide default subscript to use our custom error display
//   }
// }

// .invalid-input {
//   opacity: 0;
//   color: var(--color-poppy-red, #dc3545);
//   font-size: 0.875rem;
//   margin-top: 0.25rem;
//   transform: translateY(-4px);
//   transition: opacity 0.2s ease, transform 0.2s ease;

//   &.show-error {
//     opacity: 1;
//     transform: translateY(0);
//   }
// }

// .x-error-msg-text {
//   font-size: 0.875rem;
//   line-height: 1.5;
// }

// // Custom styling for select options
// ::ng-deep .mat-mdc-select-panel {
//   border-radius: 6px;
//   box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

//   .mat-mdc-option {
//     padding: 0.75rem 1rem;

//     &:hover {
//       background-color: var(--color-background-light, #f8f9fa);
//     }

//     &.mat-mdc-option-active {
//       background-color: var(--color-poppy-blue-light, #e6eeff);
//     }
//   }
// }


/* Make dropdown same size & look as inputs */
:host ::ng-deep .p-select {
  font-size: 0.85rem;
    padding: 7px 8px;

  width: 100%;
  background-color: #fff !important;
  color: #3b3b3b;
  border: 1px solid #b8b8b8;
//   border-radius: 4px;
}

/* Label text inside closed dropdown */
:host ::ng-deep .p-select-label {
  padding: 4px 8px;
  font-size: 0.8rem;
  color: #3b3b3b;
}

/* Placeholder style */
:host ::ng-deep .p-select-label.p-placeholder {
  font-size: 0.75rem;
  color: #999;
  font-weight: 500;

}

/* Dropdown focus state */
:host ::ng-deep .p-select.p-focus {
  border-color: #2C82AA;
  box-shadow: none;
  
}

/* Panel (opened dropdown) */
:host ::ng-deep .p-select-panel {
  background-color: transparent !important;
  color: #3b3b3b;
  border: 1px solid #b8b8b8;
}

/* Search input inside dropdown panel */
:host ::ng-deep .p-select-filter {
  background-color: #fff !important;
  color: #3b3b3b;
  border: 1px solid #b8b8b8;
  font-size: 0.8rem;
  height: 28px;
}


/* "No results found" message */
:host ::ng-deep .p-select-empty-message {
  color: #666 !important;
  background-color: #fff;
  font-size: 0.8rem;
}

/* Options style */
:host ::ng-deep .ng-star-inserted .p-select-option {
  font-size: 0.8rem;
  color: #000;
}

:host ::ng-deep .p-select-option:not(.p-select-option-selected):not(.p-disabled).p-focus{
  background-color: #2C82AA!important;
}

:host ::ng-deep .ng-star-inserted li:hover,
:host ::ng-deep .ng-star-inserted li span:hover {
 background-color: #2C82AA!important;
 color: #fff!important;
}

:host ::ng-deep .p-select-item.p-highlight {
//   background-color: #28a745 !important;
  color: #fff !important;
}

/* Make dropdown panel content background white */
:host ::ng-deep .p-select-panel,
:host ::ng-deep .p-select-items-wrapper {
  background-color: #fff !important;
}

/* Individual option background */
:host ::ng-deep  .p-select-overlay {
  background-color: #fff !important;
  border: 1px solid #b8b8b8;
}

:host ::ng-deep .p-select-item.p-highlight {
//   background-color: #28a745 !important;
  color: #fff !important;
}


:host ::ng-deep .p-floatlabel.p-floatlabel-on label {
  background-color: #fff !important;
  font-size: 0.75rem;
}

label{
    font-size: 0.75rem;
  color: #2C82AA;

}
.required-indicator {
  color: var(--color-poppy-red, #dc3545);
  margin-left: 0.25rem;
}

