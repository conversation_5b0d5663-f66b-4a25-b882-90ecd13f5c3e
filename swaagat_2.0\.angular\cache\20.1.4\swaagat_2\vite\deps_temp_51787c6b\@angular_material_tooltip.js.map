{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/tooltip.mjs"], "sourcesContent": ["export { d as MAT_TOOLTIP_DEFAULT_OPTIONS, c as MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY, M as MAT_TOOLTIP_SCROLL_STRATEGY, a as MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY, b as MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER, e as Mat<PERSON><PERSON>tip, h as MatTooltipModule, S as SCROLL_THROTTLE_MS, T as TOOLTIP_PANEL_CLASS, f as TooltipComponent, g as getMatTooltipInvalidPositionError } from './module-DVPFZEhf.mjs';\nimport '@angular/core';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/overlay';\nimport '@angular/cdk/scrolling';\nimport 'rxjs/operators';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/keycodes';\nimport '@angular/common';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/bidi';\nimport '@angular/cdk/portal';\nimport 'rxjs';\nimport './animation-ChQ1vjiF.mjs';\nimport '@angular/cdk/layout';\nimport './common-module-cKSwHniA.mjs';\n\n/**\n * Animations used by MatTooltip.\n * @docs-private\n * @deprecated No longer being used, to be removed.\n * @breaking-change 21.0.0\n */\nconst matTooltipAnimations = {\n    // Represents:\n    // trigger('state', [\n    //   state('initial, void, hidden', style({opacity: 0, transform: 'scale(0.8)'})),\n    //   state('visible', style({transform: 'scale(1)'})),\n    //   transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')),\n    //   transition('* => hidden', animate('75ms cubic-bezier(0.4, 0, 1, 1)')),\n    // ])\n    /** Animation that transitions a tooltip in and out. */\n    tooltipState: {\n        type: 7,\n        name: 'state',\n        definitions: [\n            {\n                type: 0,\n                name: 'initial, void, hidden',\n                styles: { type: 6, styles: { opacity: 0, transform: 'scale(0.8)' }, offset: null },\n            },\n            {\n                type: 0,\n                name: 'visible',\n                styles: { type: 6, styles: { transform: 'scale(1)' }, offset: null },\n            },\n            {\n                type: 1,\n                expr: '* => visible',\n                animation: { type: 4, styles: null, timings: '150ms cubic-bezier(0, 0, 0.2, 1)' },\n                options: null,\n            },\n            {\n                type: 1,\n                expr: '* => hidden',\n                animation: { type: 4, styles: null, timings: '75ms cubic-bezier(0.4, 0, 1, 1)' },\n                options: null,\n            },\n        ],\n        options: {},\n    },\n};\n\nexport { matTooltipAnimations };\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,IAAM,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASzB,cAAc;AAAA,IACV,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,MACT;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ,EAAE,MAAM,GAAG,QAAQ,EAAE,SAAS,GAAG,WAAW,aAAa,GAAG,QAAQ,KAAK;AAAA,MACrF;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ,EAAE,MAAM,GAAG,QAAQ,EAAE,WAAW,WAAW,GAAG,QAAQ,KAAK;AAAA,MACvE;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,WAAW,EAAE,MAAM,GAAG,QAAQ,MAAM,SAAS,mCAAmC;AAAA,QAChF,SAAS;AAAA,MACb;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,WAAW,EAAE,MAAM,GAAG,QAAQ,MAAM,SAAS,kCAAkC;AAAA,QAC/E,SAAS;AAAA,MACb;AAAA,IACJ;AAAA,IACA,SAAS,CAAC;AAAA,EACd;AACJ;", "names": []}