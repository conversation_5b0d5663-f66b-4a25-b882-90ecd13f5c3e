/* Modern Analytics Section */
.analytics-section-modern {
  padding: 5rem 1.5rem;
  background: #ffffff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  color: #111827;
  position: relative;
  overflow: hidden;
}

.analytics-wrapper {
  max-width: 1280px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

/* Subtle background decoration */
.analytics-section-modern::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 40%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(124, 58, 237, 0.05) 0%, rgba(255, 255, 255, 0) 70%);
  pointer-events: none;
  z-index: 0;
}

/* Header Section */
.analytics-header {
  text-align: center;
  max-width: 640px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.analytics-tagline {
  display: inline-block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #7c3aed;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 0.75rem;
  padding: 0.25rem 0.75rem;
  background: #f3e8ff;
  border-radius: 20px;
}

.analytics-title {
  font-size: 2.25rem;
  font-weight: 800;
  line-height: 1.2;
  margin-bottom: 1.25rem;
  color: #111827;
}

@media (min-width: 768px) {
  .analytics-title {
    font-size: 3rem;
  }
}

.highlight {
  background: linear-gradient(90deg, #7c3aed 0%, #c026d3 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.analytics-description {
  font-size: 1.125rem;
  color: #4b5563;
  line-height: 1.7;
  margin-bottom: 2rem;
}

/* KPI Cards Grid */
.kpi-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  position: relative;
  z-index: 1;
}

.kpi-card {
  background: #ffffff;
  border-radius: 1rem;
  padding: 1.5rem;
  border: 1px solid #f3f4f6;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.kpi-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #7c3aed, #c026d3);
  border-radius: 1rem 1rem 0 0;
}

.kpi-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  border-color: rgba(124, 58, 237, 0.2);
}

.kpi-card.color-purple::before { background: linear-gradient(90deg, #7c3aed, #c026d3); }
.kpi-card.color-blue::before { background: linear-gradient(90deg, #0284c7, #0ea5e9); }
.kpi-card.color-green::before { background: linear-gradient(90deg, #059669, #10b981); }
.kpi-card.color-orange::before { background: linear-gradient(90deg, #ea580c, #f97316); }

.kpi-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #f3e8ff, #e9d5ff);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  color: #7c3aed;
}

.kpi-content {
  display: flex;
  flex-direction: column;
}

.kpi-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #6b7280;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.kpi-value {
  font-size: 2rem;
  font-weight: 800;
  color: #111827;
  margin-bottom: 0.5rem;
}

.kpi-change {
  font-size: 0.875rem;
  font-weight: 500;
}

.trend-up { color: #059669; }
.trend-down { color: #dc2626; }

/* Summary Stats */
.summary-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin-top: 2rem;
  position: relative;
  z-index: 1;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.stat-item .stat-number {
  font-size: 1.5rem;
  font-weight: 800;
  color: #111827;
  display: block;
  background: linear-gradient(90deg, #7c3aed, #c026d3);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-item small {
  display: block;
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
  margin-top: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .analytics-section-modern {
    padding: 3rem 1rem;
  }

  .analytics-title {
    font-size: 2rem;
  }

  .kpi-cards-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .summary-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .kpi-value {
    font-size: 1.75rem;
  }
}