:host {
  font-size: 0.85rem;
}

.employer-photo {
  width: 100px;
  height: 128px;
  object-fit: cover;
}

.sub-field-text {
  font-size: 0.75rem;
  color: #666;
  margin-top: 4px;
}

th{
  background-color: #cfe2ff;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}

.table-wrapper {
  width: 100%;
  overflow-x: auto; /* horizontal scroll */
  overflow-y: hidden; /* vertical overflow nahi chahiye */
}

.table-wrapper table {
  min-width: 600px; /* ya jitna bhi required */
  border-collapse: collapse;
}


.table-container {
  width: 100%;
  overflow-x: auto; /* Horizontal scroll enable */
}

table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed; /* Important: Fix layout */
}

th, td {
  width: 200px;
  border: 1px solid #b8b8b8;
  text-align: center;
  padding: 8px;
  word-wrap: break-word; /* Text wrap */
}

.form-con{
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}


.btn-primary {
  background-color: #007bff;
  border: none;
  padding: 0.375rem 1rem;
  font-size: 0.85rem;
  border-radius: 6px;
  font-weight: 500;

  &:hover:not(:disabled) {
    background-color: #0069d9;
  }

  &:disabled {
    background-color: #6c757d;
    opacity: 0.6;
  }
}
.btn-success {
  border: none;
  padding: 0.375rem 1rem;
  font-size: 0.85rem;
  border-radius: 6px;
  font-weight: 500;



  &:disabled {
    background-color: #6c757d;
    opacity: 0.6;
  }
}