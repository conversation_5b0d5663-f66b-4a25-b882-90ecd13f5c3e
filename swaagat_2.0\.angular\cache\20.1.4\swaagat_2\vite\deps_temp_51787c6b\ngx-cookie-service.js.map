{"version": 3, "sources": ["../../../../../../node_modules/ngx-cookie-service/fesm2022/ngx-cookie-service.mjs"], "sourcesContent": ["import { isPlatformBrowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { DOCUMENT, PLATFORM_ID, Inject, Injectable } from '@angular/core';\nclass CookieService {\n  constructor(document,\n  // Get the `PLATFORM_ID` so we can check if we're in a browser.\n  platformId) {\n    this.document = document;\n    this.platformId = platformId;\n    this.documentIsAccessible = isPlatformBrowser(this.platformId);\n  }\n  /**\n   * Get cookie Regular Expression\n   *\n   * @param name Cookie name\n   * @returns property RegExp\n   *\n   * @author: <PERSON><PERSON>\n   * @since: 1.0.0\n   */\n  static getCookieRegExp(name) {\n    const escapedName = name.replace(/([[\\]{}()|=;+?,.*^$\\\\])/gi, '\\\\$1');\n    return new RegExp('(?:^' + escapedName + '|;\\\\s*' + escapedName + ')=(.*?)(?:;|$)', 'g');\n  }\n  /**\n   * Gets the decoded version of an encoded component of a Uniform Resource Identifier (URI).\n   *\n   * @param encodedURIComponent A value representing an encoded URI component.\n   *\n   * @returns The decoded version of an encoded component of a Uniform Resource Identifier (URI).\n   *\n   * @author: Stepan Suvorov\n   * @since: 1.0.0\n   */\n  static safeDecodeURIComponent(encodedURIComponent) {\n    try {\n      return decodeURIComponent(encodedURIComponent);\n    } catch {\n      // probably it is not uri encoded. return as is\n      return encodedURIComponent;\n    }\n  }\n  /**\n   * Return `true` if {@link Document} is accessible, otherwise return `false`\n   *\n   * @param name Cookie name\n   * @returns boolean - whether cookie with specified name exists\n   *\n   * @author: Stepan Suvorov\n   * @since: 1.0.0\n   */\n  check(name) {\n    if (!this.documentIsAccessible) {\n      return false;\n    }\n    name = encodeURIComponent(name);\n    const regExp = CookieService.getCookieRegExp(name);\n    return regExp.test(this.document.cookie);\n  }\n  /**\n   * Get cookies by name\n   *\n   * @param name Cookie name\n   * @returns property value\n   *\n   * @author: Stepan Suvorov\n   * @since: 1.0.0\n   */\n  get(name) {\n    if (this.check(name)) {\n      name = encodeURIComponent(name);\n      const regExp = CookieService.getCookieRegExp(name);\n      const result = regExp.exec(this.document.cookie);\n      return result?.[1] ? CookieService.safeDecodeURIComponent(result[1]) : '';\n    } else {\n      return '';\n    }\n  }\n  /**\n   * Get all cookies in JSON format\n   *\n   * @returns all the cookies in json\n   *\n   * @author: Stepan Suvorov\n   * @since: 1.0.0\n   */\n  getAll() {\n    if (!this.documentIsAccessible) {\n      return {};\n    }\n    const cookies = {};\n    const document = this.document;\n    if (document.cookie && document.cookie !== '') {\n      document.cookie.split(';').forEach(currentCookie => {\n        const [cookieName, cookieValue] = currentCookie.split('=');\n        cookies[CookieService.safeDecodeURIComponent(cookieName.replace(/^ /, ''))] = CookieService.safeDecodeURIComponent(cookieValue);\n      });\n    }\n    return cookies;\n  }\n  set(name, value, expiresOrOptions, path, domain, secure, sameSite, partitioned) {\n    if (!this.documentIsAccessible) {\n      return;\n    }\n    if (typeof expiresOrOptions === 'number' || expiresOrOptions instanceof Date || path || domain || secure || sameSite) {\n      const optionsBody = {\n        expires: expiresOrOptions,\n        path,\n        domain,\n        secure,\n        sameSite: sameSite || 'Lax',\n        partitioned\n      };\n      this.set(name, value, optionsBody);\n      return;\n    }\n    let cookieString = encodeURIComponent(name) + '=' + encodeURIComponent(value) + ';';\n    const options = expiresOrOptions ? expiresOrOptions : {};\n    if (options.expires) {\n      if (typeof options.expires === 'number') {\n        const dateExpires = new Date(new Date().getTime() + options.expires * 1000 * 60 * 60 * 24);\n        cookieString += 'expires=' + dateExpires.toUTCString() + ';';\n      } else {\n        cookieString += 'expires=' + options.expires.toUTCString() + ';';\n      }\n    }\n    if (options.path) {\n      cookieString += 'path=' + options.path + ';';\n    }\n    if (options.domain) {\n      cookieString += 'domain=' + options.domain + ';';\n    }\n    if (options.secure === false && options.sameSite === 'None') {\n      options.secure = true;\n      console.warn(`[ngx-cookie-service] Cookie ${name} was forced with secure flag because sameSite=None.` + `More details : https://github.com/stevermeister/ngx-cookie-service/issues/86#issuecomment-597720130`);\n    }\n    if (options.secure) {\n      cookieString += 'secure;';\n    }\n    if (!options.sameSite) {\n      options.sameSite = 'Lax';\n    }\n    cookieString += 'sameSite=' + options.sameSite + ';';\n    if (options.partitioned) {\n      cookieString += 'Partitioned;';\n    }\n    this.document.cookie = cookieString;\n  }\n  /**\n   * Delete cookie by name at given path and domain. If not path is not specified, cookie at '/' path will be deleted.\n   *\n   * @param name   Cookie name\n   * @param path   Cookie path\n   * @param domain Cookie domain\n   * @param secure Cookie secure flag\n   * @param sameSite Cookie sameSite flag - https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie/SameSite\n   *\n   * @author: Stepan Suvorov\n   * @since: 1.0.0\n   */\n  delete(name, path, domain, secure, sameSite = 'Lax') {\n    if (!this.documentIsAccessible) {\n      return;\n    }\n    const expiresDate = new Date('Thu, 01 Jan 1970 00:00:01 GMT');\n    this.set(name, '', {\n      expires: expiresDate,\n      path,\n      domain,\n      secure,\n      sameSite\n    });\n  }\n  /**\n   * Delete all cookies at given path and domain. If not path is not specified, all cookies at '/' path will be deleted.\n   *\n   * @param path   Cookie path\n   * @param domain Cookie domain\n   * @param secure Is the Cookie secure\n   * @param sameSite Is the cookie same site\n   *\n   * @author: Stepan Suvorov\n   * @since: 1.0.0\n   */\n  deleteAll(path, domain, secure, sameSite = 'Lax') {\n    if (!this.documentIsAccessible) {\n      return;\n    }\n    const cookies = this.getAll();\n    for (const cookieName in cookies) {\n      if (cookies.hasOwnProperty(cookieName)) {\n        this.delete(cookieName, path, domain, secure, sameSite);\n      }\n    }\n  }\n  static {\n    this.ɵfac = function CookieService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CookieService)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(PLATFORM_ID));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: CookieService,\n      factory: CookieService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CookieService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }], null);\n})();\n\n/*\n * Public API Surface of ngx-cookie-service\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CookieService };\n"], "mappings": ";;;;;;;;;;;;;;;;;AAGA,IAAM,iBAAN,MAAM,eAAc;AAAA,EAClB,YAAY,UAEZ,YAAY;AACV,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,uBAAuB,kBAAkB,KAAK,UAAU;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,gBAAgB,MAAM;AAC3B,UAAM,cAAc,KAAK,QAAQ,6BAA6B,MAAM;AACpE,WAAO,IAAI,OAAO,SAAS,cAAc,WAAW,cAAc,kBAAkB,GAAG;AAAA,EACzF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OAAO,uBAAuB,qBAAqB;AACjD,QAAI;AACF,aAAO,mBAAmB,mBAAmB;AAAA,IAC/C,QAAQ;AAEN,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,MAAM;AACV,QAAI,CAAC,KAAK,sBAAsB;AAC9B,aAAO;AAAA,IACT;AACA,WAAO,mBAAmB,IAAI;AAC9B,UAAM,SAAS,eAAc,gBAAgB,IAAI;AACjD,WAAO,OAAO,KAAK,KAAK,SAAS,MAAM;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,IAAI,MAAM;AACR,QAAI,KAAK,MAAM,IAAI,GAAG;AACpB,aAAO,mBAAmB,IAAI;AAC9B,YAAM,SAAS,eAAc,gBAAgB,IAAI;AACjD,YAAM,SAAS,OAAO,KAAK,KAAK,SAAS,MAAM;AAC/C,aAAO,SAAS,CAAC,IAAI,eAAc,uBAAuB,OAAO,CAAC,CAAC,IAAI;AAAA,IACzE,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAS;AACP,QAAI,CAAC,KAAK,sBAAsB;AAC9B,aAAO,CAAC;AAAA,IACV;AACA,UAAM,UAAU,CAAC;AACjB,UAAM,WAAW,KAAK;AACtB,QAAI,SAAS,UAAU,SAAS,WAAW,IAAI;AAC7C,eAAS,OAAO,MAAM,GAAG,EAAE,QAAQ,mBAAiB;AAClD,cAAM,CAAC,YAAY,WAAW,IAAI,cAAc,MAAM,GAAG;AACzD,gBAAQ,eAAc,uBAAuB,WAAW,QAAQ,MAAM,EAAE,CAAC,CAAC,IAAI,eAAc,uBAAuB,WAAW;AAAA,MAChI,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EACA,IAAI,MAAM,OAAO,kBAAkB,MAAM,QAAQ,QAAQ,UAAU,aAAa;AAC9E,QAAI,CAAC,KAAK,sBAAsB;AAC9B;AAAA,IACF;AACA,QAAI,OAAO,qBAAqB,YAAY,4BAA4B,QAAQ,QAAQ,UAAU,UAAU,UAAU;AACpH,YAAM,cAAc;AAAA,QAClB,SAAS;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAU,YAAY;AAAA,QACtB;AAAA,MACF;AACA,WAAK,IAAI,MAAM,OAAO,WAAW;AACjC;AAAA,IACF;AACA,QAAI,eAAe,mBAAmB,IAAI,IAAI,MAAM,mBAAmB,KAAK,IAAI;AAChF,UAAM,UAAU,mBAAmB,mBAAmB,CAAC;AACvD,QAAI,QAAQ,SAAS;AACnB,UAAI,OAAO,QAAQ,YAAY,UAAU;AACvC,cAAM,cAAc,IAAI,MAAK,oBAAI,KAAK,GAAE,QAAQ,IAAI,QAAQ,UAAU,MAAO,KAAK,KAAK,EAAE;AACzF,wBAAgB,aAAa,YAAY,YAAY,IAAI;AAAA,MAC3D,OAAO;AACL,wBAAgB,aAAa,QAAQ,QAAQ,YAAY,IAAI;AAAA,MAC/D;AAAA,IACF;AACA,QAAI,QAAQ,MAAM;AAChB,sBAAgB,UAAU,QAAQ,OAAO;AAAA,IAC3C;AACA,QAAI,QAAQ,QAAQ;AAClB,sBAAgB,YAAY,QAAQ,SAAS;AAAA,IAC/C;AACA,QAAI,QAAQ,WAAW,SAAS,QAAQ,aAAa,QAAQ;AAC3D,cAAQ,SAAS;AACjB,cAAQ,KAAK,+BAA+B,IAAI,wJAA6J;AAAA,IAC/M;AACA,QAAI,QAAQ,QAAQ;AAClB,sBAAgB;AAAA,IAClB;AACA,QAAI,CAAC,QAAQ,UAAU;AACrB,cAAQ,WAAW;AAAA,IACrB;AACA,oBAAgB,cAAc,QAAQ,WAAW;AACjD,QAAI,QAAQ,aAAa;AACvB,sBAAgB;AAAA,IAClB;AACA,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,OAAO,MAAM,MAAM,QAAQ,QAAQ,WAAW,OAAO;AACnD,QAAI,CAAC,KAAK,sBAAsB;AAC9B;AAAA,IACF;AACA,UAAM,cAAc,oBAAI,KAAK,+BAA+B;AAC5D,SAAK,IAAI,MAAM,IAAI;AAAA,MACjB,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,UAAU,MAAM,QAAQ,QAAQ,WAAW,OAAO;AAChD,QAAI,CAAC,KAAK,sBAAsB;AAC9B;AAAA,IACF;AACA,UAAM,UAAU,KAAK,OAAO;AAC5B,eAAW,cAAc,SAAS;AAChC,UAAI,QAAQ,eAAe,UAAU,GAAG;AACtC,aAAK,OAAO,YAAY,MAAM,QAAQ,QAAQ,QAAQ;AAAA,MACxD;AAAA,IACF;AAAA,EACF;AAaF;AAXI,eAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,SAAO,KAAK,qBAAqB,gBAAkB,SAAS,QAAQ,GAAM,SAAS,WAAW,CAAC;AACjG;AAGA,eAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,eAAc;AAAA,EACvB,YAAY;AACd,CAAC;AA1ML,IAAM,gBAAN;AAAA,CA6MC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;", "names": []}