@import url('https://fonts.googleapis.com/css2?family=Merriweather:wght@700&family=Open+Sans:wght@400;600&display=swap');

body {
  margin: 0;
  font-family: 'Open Sans', sans-serif;
  overflow-x: hidden;
}

.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.from-gray-50 {
  --tw-gradient-from: #f9fafb;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(249, 250, 251, 0));
}

.to-blue-50 {
  --tw-gradient-to: #eff6ff;
}

.relative {
  position: relative;
}

.overflow-hidden {
  overflow: hidden;
}

.absolute {
  position: absolute;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.-top-40 {
  top: -10rem;
}

.-right-40 {
  right: -10rem;
}

.w-80 {
  width: 20rem;
}

.h-80 {
  height: 20rem;
}

.from-orange-200\/30 {
  --tw-gradient-from: rgba(254, 215, 170, 0.3);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(254, 215, 170, 0));
}

.to-red-200\/30 {
  --tw-gradient-to: rgba(254, 202, 202, 0.3);
}

.rounded-full {
  border-radius: 9999px;
}

.blur-3xl {
  filter: blur(64px);
}

.-bottom-40 {
  bottom: -10rem;
}

.-left-40 {
  left: -10rem;
}

.from-blue-200\/30 {
  --tw-gradient-from: rgba(191, 219, 254, 0.3);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(191, 219, 254, 0));
}

.to-green-200\/30 {
  --tw-gradient-to: rgba(187, 247, 208, 0.3);
}

.max-w-7xl {
  max-width: 80rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.sm\:px-6 {
  @media (min-width: 640px) {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

.lg\:px-8 {
  @media (min-width: 1024px) {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.z-10 {
  z-index: 10;
}

.text-center {
  text-align: center;
}

.mb-16 {
  margin-bottom: 4rem;
}

.inline-flex {
  display: inline-flex;
}

.items-center {
  align-items: center;
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  margin-left: calc(0.5rem * var(--tw-space-x-reverse));
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-orange-100 {
  --tw-gradient-from: #ffedd5;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(255, 237, 213, 0));
}

.to-blue-100 {
  --tw-gradient-to: #dbeafe;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.text-orange-500 {
  color: #f97316;
}

.text-gray-700 {
  color: #374151;
}

.font-medium {
  font-weight: 500;
}

.font-merriweather {
  font-family: 'Merriweather', serif;
}

.font-bold {
  font-weight: 700;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.sm\:text-4xl {
  @media (min-width: 640px) {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

.text-gray-900 {
  color: #111827;
}

.mb-4 {
  margin-bottom: 1rem;
}

.font-open-sans {
  font-family: 'Open Sans', sans-serif;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-gray-600 {
  color: #4b5563;
}

.max-w-3xl {
  max-width: 48rem;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.md\:grid-cols-3 {
  @media (min-width: 768px) {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

.gap-8 {
  gap: 2rem;
}

.bg-white {
  background-color: #ffffff;
}

.rounded-3xl {
  border-radius: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.shadow-xl {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.border {
  border-width: 1px;
}

.border-gray-100 {
  border-color: #f3f4f6;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-700 {
  transition-duration: 700ms;
}

.transform {
  transform: translate(0, 0);
}

.opacity-100 {
  opacity: 1;
}

.translate-y-0 {
  transform: translateY(0);
}

.hover\:shadow-2xl:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.hover\:-translate-y-2:hover {
  transform: translateY(-0.5rem);
}

.w-20 {
  width: 5rem;
}

.h-20 {
  height: 5rem;
}

.from-green-500 {
  --tw-gradient-from: #22c55e;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(34, 197, 94, 0));
}

.to-emerald-500 {
  --tw-gradient-to: #10b981;
}

.rounded-2xl {
  border-radius: 1rem;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.group-hover\:scale-110:hover {
  transform: scale(1.1);
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.text-white {
  color: #ffffff;
}

.text-5xl {
  font-size: 3rem;
  line-height: 1;
}

.font-mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.tracking-tight {
  letter-spacing: -0.025em;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.font-semibold {
  font-weight: 600;
}

.text-gray-800 {
  color: #1f2937;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.from-blue-500 {
  --tw-gradient-from: #3b82f6;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(59, 130, 246, 0));
}

.to-cyan-500 {
  --tw-gradient-to: #06b6d4;
}

.from-purple-500 {
  --tw-gradient-from: #8b5cf6;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(139, 92, 246, 0));
}

.to-indigo-500 {
  --tw-gradient-to: #6366f1;
}

.mt-16 {
  margin-top: 4rem;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.from-orange-500 {
  --tw-gradient-from: #f97316;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(249, 115, 22, 0));
}

.to-orange-600 {
  --tw-gradient-to: #ea580c;
}

.hover\:from-orange-600:hover {
  --tw-gradient-from: #ea580c;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(234, 88, 12, 0));
}

.hover\:to-orange-700:hover {
  --tw-gradient-to: #c2410c;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.font-semibold {
  font-weight: 600;
}

.hover\:scale-105:hover {
  transform: scale(1.05);
}