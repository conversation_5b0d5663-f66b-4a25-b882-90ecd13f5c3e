.grid-container {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
 column-gap: 1.5rem;
row-gap: 1.5rem;

  @media (max-width: 1024px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}


.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}

.btn-primary {
  background-color: #007bff;
  border: none;
  padding: 0.375rem 1rem;
  font-size: 0.85rem;
  border-radius: 6px;
  font-weight: 500;

  &:hover:not(:disabled) {
    background-color: #0069d9;
  }

  &:disabled {
    background-color: #6c757d;
    opacity: 0.6;
  }
}
.btn-success {
  border: none;
  padding: 0.375rem 1rem;
  font-size: 0.85rem;
  border-radius: 6px;
  font-weight: 500;



  &:disabled {
    background-color: #6c757d;
    opacity: 0.6;
  }
}