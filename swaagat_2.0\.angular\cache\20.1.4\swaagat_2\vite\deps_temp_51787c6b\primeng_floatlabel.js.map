{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-floatlabel.mjs"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, Input, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { SharedModule } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { style } from '@primeuix/styles/floatlabel';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"*\"];\nconst theme = /*css*/`\n    ${style}\n\n    /* For PrimeNG */\n    .p-floatlabel:has(.ng-invalid.ng-dirty) label {\n        color: dt('floatlabel.invalid.color');\n    }\n`;\nconst classes = {\n  root: ({\n    instance\n  }) => ['p-floatlabel', {\n    'p-floatlabel-over': instance.variant === 'over',\n    'p-floatlabel-on': instance.variant === 'on',\n    'p-floatlabel-in': instance.variant === 'in'\n  }]\n};\nclass FloatLabelStyle extends BaseStyle {\n  name = 'floatlabel';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵFloatLabelStyle_BaseFactory;\n    return function FloatLabelStyle_Factory(__ngFactoryType__) {\n      return (ɵFloatLabelStyle_BaseFactory || (ɵFloatLabelStyle_BaseFactory = i0.ɵɵgetInheritedFactory(FloatLabelStyle)))(__ngFactoryType__ || FloatLabelStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FloatLabelStyle,\n    factory: FloatLabelStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FloatLabelStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * FloatLabel visually integrates a label with its form element.\n *\n * [Live Demo](https://www.primeng.org/floatlabel/)\n *\n * @module floatlabelstyle\n *\n */\nvar FloatLabelClasses;\n(function (FloatLabelClasses) {\n  /**\n   * Class name of the root element\n   */\n  FloatLabelClasses[\"root\"] = \"p-floatlabel\";\n})(FloatLabelClasses || (FloatLabelClasses = {}));\n\n/**\n * FloatLabel appears on top of the input field when focused.\n * @group Components\n */\nclass FloatLabel extends BaseComponent {\n  _componentStyle = inject(FloatLabelStyle);\n  /**\n   * Defines the positioning of the label relative to the input.\n   * @group Props\n   */\n  variant = 'over';\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵFloatLabel_BaseFactory;\n    return function FloatLabel_Factory(__ngFactoryType__) {\n      return (ɵFloatLabel_BaseFactory || (ɵFloatLabel_BaseFactory = i0.ɵɵgetInheritedFactory(FloatLabel)))(__ngFactoryType__ || FloatLabel);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: FloatLabel,\n    selectors: [[\"p-floatlabel\"], [\"p-floatLabel\"], [\"p-float-label\"]],\n    hostVars: 2,\n    hostBindings: function FloatLabel_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.cx(\"root\"));\n      }\n    },\n    inputs: {\n      variant: \"variant\"\n    },\n    features: [i0.ɵɵProvidersFeature([FloatLabelStyle]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function FloatLabel_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    dependencies: [CommonModule, SharedModule],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FloatLabel, [{\n    type: Component,\n    args: [{\n      selector: 'p-floatlabel, p-floatLabel, p-float-label',\n      standalone: true,\n      imports: [CommonModule, SharedModule],\n      template: ` <ng-content></ng-content> `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [FloatLabelStyle],\n      host: {\n        '[class]': \"cx('root')\"\n      }\n    }]\n  }], null, {\n    variant: [{\n      type: Input\n    }]\n  });\n})();\nclass FloatLabelModule {\n  static ɵfac = function FloatLabelModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FloatLabelModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: FloatLabelModule,\n    imports: [FloatLabel, SharedModule],\n    exports: [FloatLabel, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [FloatLabel, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FloatLabelModule, [{\n    type: NgModule,\n    args: [{\n      imports: [FloatLabel, SharedModule],\n      exports: [FloatLabel, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FloatLabel, FloatLabelClasses, FloatLabelModule, FloatLabelStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM;AAAA;AAAA,EAAe;AAAA,MACf,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOX,IAAM,UAAU;AAAA,EACd,MAAM,CAAC;AAAA,IACL;AAAA,EACF,MAAM,CAAC,gBAAgB;AAAA,IACrB,qBAAqB,SAAS,YAAY;AAAA,IAC1C,mBAAmB,SAAS,YAAY;AAAA,IACxC,mBAAmB,SAAS,YAAY;AAAA,EAC1C,CAAC;AACH;AACA,IAAM,kBAAN,MAAM,yBAAwB,UAAU;AAAA,EACtC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,wBAAwB,mBAAmB;AACzD,cAAQ,iCAAiC,+BAAkC,sBAAsB,gBAAe,IAAI,qBAAqB,gBAAe;AAAA,IAC1J;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,iBAAgB;AAAA,EAC3B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,oBAAmB;AAI5B,EAAAA,mBAAkB,MAAM,IAAI;AAC9B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAMhD,IAAM,aAAN,MAAM,oBAAmB,cAAc;AAAA,EACrC,kBAAkB,OAAO,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxC,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,mBAAmB,mBAAmB;AACpD,cAAQ,4BAA4B,0BAA6B,sBAAsB,WAAU,IAAI,qBAAqB,WAAU;AAAA,IACtI;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,GAAG,CAAC,cAAc,GAAG,CAAC,eAAe,CAAC;AAAA,IACjE,UAAU;AAAA,IACV,cAAc,SAAS,wBAAwB,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,GAAG,MAAM,CAAC;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,eAAe,CAAC,GAAM,0BAA0B;AAAA,IAClF,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,oBAAoB,IAAI,KAAK;AAC9C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAc,YAAY;AAAA,IACzC,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,YAAY;AAAA,MACpC,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,eAAe;AAAA,MAC3B,MAAM;AAAA,QACJ,WAAW;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,YAAY,YAAY;AAAA,IAClC,SAAS,CAAC,YAAY,YAAY;AAAA,EACpC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,YAAY,cAAc,YAAY;AAAA,EAClD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY,YAAY;AAAA,MAClC,SAAS,CAAC,YAAY,YAAY;AAAA,IACpC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["FloatLabelClasses"]}