@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap');

$primary-color: #1e3a8a; // Deep blue for a professional look
$accent-color: #3b82f6; // Bright blue for buttons and highlights
$background-color: #f1f5f9; // Light slate background
$card-background: linear-gradient(135deg, #ffffff, #f8fafc);
$text-color: #1f2937;
$muted-text: #6b7280;

.info-hub-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  font-family: 'Poppins', system-ui, sans-serif;
  background-color: $background-color;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center; // Center container content horizontally
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center; // Center header content
  text-align: center;
  margin-bottom: 3rem;
  width: 100%;

  h1 {
    font-size: 2.75rem;
    font-weight: 600;
    color: $text-color;
    margin-bottom: 0.75rem;
    letter-spacing: -0.025em;
  }

  p {
    font-size: 1.125rem;
    color: $muted-text;
    max-width: 700px;
    margin: 0 auto 1.5rem;
    line-height: 1.6;
  }

  .search-bar {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.75rem;
    max-width: 500px;
    width: 100%; // Ensure search bar spans available space
    position: relative;

    input {
      padding: 0.75rem 1rem;
      padding-left: 2.5rem;
      width: 100%;
      border: 1px solid #d1d5db;
      border-radius: 8px;
      font-size: 1rem;
      background-color: white;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:focus {
        border-color: $accent-color;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
        outline: none;
      }

      &::placeholder {
        color: $muted-text;
        opacity: 0.8;
      }
    }

    // Search icon
    &::before {
      content: '\1F50D'; // Unicode for magnifying glass
      position: absolute;
      left: 0.75rem;
      font-size: 1.25rem;
      color: $muted-text;
    }

    button {
      padding: 0.75rem 1.5rem;
      background: linear-gradient(to right, $accent-color, #60a5fa);
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 1rem;
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(to right, #2563eb, $accent-color);
        transform: translateY(-2px);
      }

      &:active {
        transform: translateY(0);
      }

      &:focus {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
        outline: none;
      }
    }
  }
}

.content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center; // Center content block

  .category {
    margin-bottom: 3rem;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center; // Center category content

    h2 {
      font-size: 2rem;
      font-weight: 600;
      color: $text-color;
      margin-bottom: 1.5rem;
      position: relative;
      padding-bottom: 0.75rem;
      text-align: center; // Center category title

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%); // Center the underline
        width: 80px;
        height: 4px;
        background: linear-gradient(to right, $accent-color, transparent);
        border-radius: 2px;
      }
    }

    .resource-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 320px)); // Fixed card width for consistency
      gap: 1.5rem;
      width: 100%;
      justify-content: center; // Center the grid items horizontally
      justify-items: center; // Ensure each card is centered within its grid cell
    }

    .resource-card {
      background: $card-background;
      padding: 1.5rem;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      border: 1px solid #e5e7eb;
      width: 100%;
      max-width: 320px; // Consistent card width
      display: flex;
      flex-direction: column;
      align-items: center; // Center card content
      text-align: center; // Center text within cards

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
        border-color: $accent-color;
      }

      h3 {
        font-size: 1.5rem;
        font-weight: 500;
        color: $text-color;
        margin-bottom: 0.75rem;
      }

      p {
        font-size: 1rem;
        color: $muted-text;
        margin-bottom: 1rem;
        line-height: 1.5;
      }

      .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center; // Center button text
        padding: 0.5rem 1.25rem;
        background: linear-gradient(to right, $accent-color, #60a5fa);
        color: white;
        text-decoration: none;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          background: linear-gradient(to right, #2563eb, $accent-color);
          transform: translateY(-1px);
        }

        &:focus {
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
          outline: none;
        }
      }
    }
  }

  .no-results {
    text-align: center;
    font-size: 1.25rem;
    color: $muted-text;
    padding: 2rem;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    width: 100%;
    max-width: 500px; // Limit width for centering
    margin: 0 auto;
  }
}

// Responsive design
@media (max-width: 1024px) {
  .header {
    h1 {
      font-size: 2.25rem;
    }

    .search-bar {
      max-width: 400px;
    }
  }

  .content .category .resource-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 320px)); // Consistent card width
    justify-content: center; // Maintain centering
  }
}

@media (max-width: 768px) {
  .info-hub-container {
    padding: 1.5rem;
  }

  .header {
    h1 {
      font-size: 1.75rem;
    }

    p {
      font-size: 1rem;
    }

    .search-bar {
      flex-direction: column;
      align-items: center; // Center search bar items
      max-width: 100%;
    }
  }

  .content .category {
    h2 {
      font-size: 1.5rem;
    }

    .resource-grid {
      grid-template-columns: 1fr; // Single column for mobile
      justify-content: center; // Center single-column cards
      justify-items: center; // Ensure cards are centered
    }
  }
}

@media (max-width: 480px) {
  .header .search-bar input {
    padding-left: 2.25rem;
    font-size: 0.9rem;
  }

  .content .category .resource-card {
    padding: 1rem;
    max-width: 100%; // Full width on small screens
  }
}