.contact-section {
  position: relative;
  padding: 6rem 1.5rem;
  background: #ffffff;
  font-family: 'Inter', -apple-system, sans-serif;
  color: #1e293b;
  overflow: hidden;
}

.contact-container {
  max-width: 1280px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.contact-header {
  text-align: center;
  max-width: 700px;
  margin: 0 auto 3.5rem;
}

.contact-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.contact-subtitle {
  font-size: 1.125rem;
  color: #64748b;
  line-height: 1.7;
}

/* Contact Grid */
.contact-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 768px) {
  .contact-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Contact Card */
.contact-card {
  background: white;
  border-radius: 1.25rem;
  padding: 2rem;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  transition: all 0.35s ease;
  position: relative;
  overflow: hidden;
}

.contact-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  border-color: #7c3aed20;
}

/* Icon Styling */
.contact-icon {
  width: 60px;
  height: 60px;
  background: #f3e8ff;
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.25rem;
  color: #7c3aed;
  font-size: 1.5rem;
}

.contact-icon svg {
  width: 28px;
  height: 28px;
  stroke: #7c3aed;
}

/* Content */
.contact-card h3 {
  font-size: 1.375rem;
  color: #1e293b;
  margin-bottom: 1rem;
  font-weight: 700;
}

.contact-detail {
  font-size: 1rem;
  color: #475569;
  line-height: 1.8;
}

.contact-detail a {
  color: #7c3aed;
  text-decoration: none;
  font-weight: 500;
}

.contact-detail a:hover {
  text-decoration: underline;
}

.contact-note {
  font-size: 0.875rem;
  color: #94a3b8;
  margin-top: 0.75rem;
}

/* Tags */
.tag {
  background: #fef3c7;
  color: #d97706;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.15rem 0.5rem;
  border-radius: 0.375rem;
  margin-left: 0.5rem;
}

/* Action Links */
.action-links {
  margin-top: 1rem;
  font-size: 0.95rem;
}

.link-primary, .link-secondary {
  color: #7c3aed;
  text-decoration: none;
  font-weight: 500;
}

.link-secondary {
  color: #64748b;
}

.link-primary:hover, .link-secondary:hover {
  text-decoration: underline;
}

/* Decorative SVG Background */
.contact-decoration {
  position: absolute;
  top: 20%;
  right: 10%;
  width: 120px;
  height: 120px;
  opacity: 0.6;
  pointer-events: none;
  z-index: 1;
  animation: float 8s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(5deg); }
}

/* Responsive Adjustments */
@media (max-width: 640px) {
  .contact-title {
    font-size: 2rem;
  }

  .contact-card {
    padding: 1.5rem;
  }
}