.dialog-content {
  width: 100%;
  max-width: 1000px;
  background: #fff;
  border-radius: 8px;
}

::ng-deep .custom-dialog-container .mat-dialog-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 2rem;
  border-radius: 12px;
  width: 1055px !important;
  max-width: 95vw !important;
  height: auto !important;
  max-height: 90vh !important;
}

.service-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.w-100 {
  width: 100%;
}

.custom-dialog-container {
  border-radius: 12px !important;
  padding: 0;
  animation: fadeIn 0.3s ease-in-out;
}

.heading {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

h2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e0e0e0;
}

.title {
  font-size: 1.2rem;
  font-weight: 600;
}

.dialog-header .title {
  font-size: 18px;
  font-weight: 600;
}

.service-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.full-width {
  width: 100%;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-row {
  display: flex;
  gap: 1rem;
}

.form-field {
  flex: 1;
}

.checkboxes {
  flex-wrap: wrap;
  gap: 1rem;
}

.w-100 {
  width: 100%;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
}

.dialog-content {
  width: 100%;
  max-width: 1100px;
  background: #fff;
  border-radius: 10px;
  padding: 1.5rem;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e0e0e0;

  .title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
  }
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-row {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.form-field {
  flex: 1;
}

.checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 0.5rem;
}

mat-form-field {
  width: 100%;
}

mat-checkbox {
  margin-right: 1rem;
}

mat-dialog-actions {
  padding: 1rem 0;
}

button[mat-raised-button] {
  min-width: 120px;
}

@media (max-width: 1024px) {
  .form-row {
    flex-direction: column;
  }
}