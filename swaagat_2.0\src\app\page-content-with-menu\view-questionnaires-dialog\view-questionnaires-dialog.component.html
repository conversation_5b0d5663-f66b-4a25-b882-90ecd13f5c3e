<div class="dialog-header" mat-dialog-title>
  <h2 class="dialog-title">
    Questionnaires for <b>{{ data.service.name }}</b>
  </h2>
  <button mat-icon-button mat-dialog-close class="close-btn" aria-label="Close dialog">
    <mat-icon>close</mat-icon>
  </button>
</div>

<div mat-dialog-content>
  <div *ngIf="loading" class="loading-container">
    <mat-spinner diameter="50"></mat-spinner>
  </div>

  <div *ngIf="!loading && questionnaires.length === 0" class="empty-state">
    <mat-icon class="empty-icon">info</mat-icon>
    <p>No rows (Questionnaires) to show</p>
  </div>

  <div class="table-wrapper" *ngIf="!loading && questionnaires.length > 0">
    <table mat-table [dataSource]="questionnaires" class="custom-table mat-elevation-z4" [@fadeInTable]>

      <ng-container matColumnDef="display_order">
        <th mat-header-cell *matHeaderCellDef class="heading"> Order </th>
        <td mat-cell *matCellDef="let q"> {{ q.display_order }} </td>
      </ng-container>

      <ng-container matColumnDef="question_label">
        <th mat-header-cell *matHeaderCellDef class="heading"> Label </th>
        <td mat-cell *matCellDef="let q"> {{ q.question_label }} </td>
      </ng-container>

      <ng-container matColumnDef="question_type">
        <th mat-header-cell *matHeaderCellDef class="heading"> Type </th>
        <td mat-cell *matCellDef="let q"> {{ q.question_type }} </td>
      </ng-container>

      <ng-container matColumnDef="is_required">
        <th mat-header-cell *matHeaderCellDef class="heading"> Required </th>
        <td mat-cell *matCellDef="let q"> {{ q.is_required ? 'Yes' : 'No' }} </td>
      </ng-container>

      <ng-container matColumnDef="options">
        <th mat-header-cell *matHeaderCellDef class="heading"> Options </th>
        <td mat-cell *matCellDef="let q"> {{ getOptionsAsString(q.options) || '---' }} </td>
      </ng-container>

      <ng-container matColumnDef="default_value">
        <th mat-header-cell *matHeaderCellDef class="heading"> Default </th>
        <td mat-cell *matCellDef="let q"> {{ q.default_value || '-' }} </td>
      </ng-container>

      <ng-container matColumnDef="group_label">
        <th mat-header-cell *matHeaderCellDef class="heading"> Group </th>
        <td mat-cell *matCellDef="let q"> {{ q.group_label || '-' }} </td>
      </ng-container>

      <ng-container matColumnDef="display_width">
        <th mat-header-cell *matHeaderCellDef class="heading"> Width </th>
        <td mat-cell *matCellDef="let q"> {{ q.display_width }} </td>
      </ng-container>

      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef class="heading text-center"> Actions </th>
        <td mat-cell *matCellDef="let q" class="action-buttons">
          <button mat-icon-button color="primary" matTooltip="Edit" (click)="editQuestionnaire(q)" [@fadeInRow]>
            <mat-icon>edit</mat-icon>
          </button>
          <button mat-icon-button color="warn" matTooltip="Delete" (click)="deleteQuestionnaire(q)" [@fadeInRow]>
            <mat-icon>delete</mat-icon>
          </button>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;" [@fadeInRow]></tr>
    </table>
  </div>
</div>

<div mat-dialog-actions align="end">
  <button mat-raised-button color="warn" (click)="closeDialog()">
    <mat-icon>close</mat-icon> Close
  </button>
</div>