<form [formGroup]="form" class="">

  <div class="form-grid">
    <app-ilogi-input fieldLabel="Name of the Unit" formControlName="unitName" [mandatory]="true"
      [placeholder]="'Name of the Unit'"></app-ilogi-input>
    <app-ilogi-input fieldLabel="Post Office" formControlName="postOffice"
      [placeholder]="'Post Office'"></app-ilogi-input>
    <app-ilogi-input fieldLabel="Unit Address" formControlName="unitAddress" [mandatory]="true"
      [placeholder]="'Unit Address'"></app-ilogi-input>
    <app-ilogi-input fieldLabel="Contact No" formControlName="contactNo" [placeholder]="'Contact No'"></app-ilogi-input>
    <app-ilogi-input fieldLabel="Email" formControlName="email" [placeholder]="'Email'"></app-ilogi-input>
    <app-ilogi-input fieldLabel="Website" formControlName="website" [placeholder]="'Website'"></app-ilogi-input>

   <app-ilogi-select
  formControlName="district"
  fieldLabel="District"
  [selectOptions]="districtOptions"
  placeholder="Select District"
  [mandatory]="true"
  (change)="onDistrictChange($event.value)"
></app-ilogi-select>
   <app-ilogi-select
  formControlName="subDivision"
  fieldLabel="Subdivision"
  [selectOptions]="subDivisionOptions"
  placeholder="Select Subdivision"
  [mandatory]="true"
  [disabled]="subDivisionOptions.length === 0"
></app-ilogi-select>
</div>
  <app-ilogi-radio fieldLabel="Area Type" formControlName="areaType" [radioOptions]="[
    {name:'Urban', value:'Urban'},
    {name:'Rural', value:'Rural'}
  ]">
  </app-ilogi-radio>
  <div class="form-grid mb-3">

    <app-ilogi-select fieldLabel="Land Type" formControlName="landType" [mandatory]="true" [placeholder]="'Land Type'"
      [selectOptions]="unit_location_land_type"></app-ilogi-select>

    <app-ilogi-input fieldLabel="Block" formControlName="block" [placeholder]="'Block'"
      *ngIf="visibility.showRuralFields"></app-ilogi-input>
    <app-ilogi-input fieldLabel="Gram Panchayat" formControlName="gramPanchayat" [placeholder]="'Gram Panchayat'"
      *ngIf="visibility.showRuralFields"></app-ilogi-input>

    <app-ilogi-input fieldLabel="Municipality" formControlName="municipality" [placeholder]="'Municipality'"
      *ngIf="visibility.showUrbanFields"></app-ilogi-input>
    <app-ilogi-input fieldLabel="Ward No" formControlName="wardNo" [placeholder]="'Ward No'"
      *ngIf="visibility.showUrbanFields"></app-ilogi-input>

    <app-ilogi-input fieldLabel="Estate Name" formControlName="estateName" [placeholder]="'Estate Name'"
      *ngIf="visibility.showEstateFields"></app-ilogi-input>
    <app-ilogi-input fieldLabel="Plot No (Estate)" formControlName="estatePlotNo" [placeholder]="'Plot No (Estate)'"
      *ngIf="visibility.showEstateFields"></app-ilogi-input>
  </div>


  <div class="form-grid mb-3" > 
    <app-ilogi-input fieldLabel="Pin Code" formControlName="pinNo" [mandatory]="true"
      [placeholder]="'Pin Code'"></app-ilogi-input>
    <app-ilogi-input fieldLabel="Fax" formControlName="fax" [placeholder]="'Fax'"></app-ilogi-input>
    <app-ilogi-input fieldLabel="Police Station" formControlName="policeStation" [mandatory]="true"
      [placeholder]="'Police Station'"></app-ilogi-input>

    <app-ilogi-input fieldLabel="Planning Area" formControlName="planningArea"
      [placeholder]="'Planning Area'"></app-ilogi-input>
  </div>

  <div class="form-grid mb-3">
    <app-ilogi-input fieldLabel="Tehasil" formControlName="tehasil" [placeholder]="'Tehasil'"></app-ilogi-input>
    <app-ilogi-input fieldLabel="Revenue Mouza" formControlName="mouza"
      [placeholder]="'Revenue Mouza'"></app-ilogi-input>
    <app-ilogi-input fieldLabel="Plot No (CS Sabek)" formControlName="plotNoCs"
      [placeholder]="'Plot No (CS Sabek)'"></app-ilogi-input>
    <app-ilogi-input fieldLabel="Load Bearing (Sq. Mtr)" formControlName="loadBearing"
      [placeholder]="'Load Bearing (Sq. Mtr)'"></app-ilogi-input>
    <app-ilogi-input fieldLabel="Other Construction (Sq. Mtr)" formControlName="otherConstruction"
      [placeholder]="'Other Construction (Sq. Mtr)'"></app-ilogi-input>
    <app-ilogi-input fieldLabel="Power Supply Agency" formControlName="powerSupply"
      [placeholder]="'Power Supply Agency'"></app-ilogi-input>
  </div>

  <div class="form-grid">
    <app-ilogi-input fieldLabel="Revenue Circle" formControlName="revenueCircle" [mandatory]="true"
      [placeholder]="'Revenue Circle'"></app-ilogi-input>
    <app-ilogi-input fieldLabel="Khatian No" formControlName="khatianNo" [mandatory]="true"
      [placeholder]="'Khatian No'"></app-ilogi-input>
    <app-ilogi-input fieldLabel="Plot No" formControlName="plotNo" [mandatory]="true"
      [placeholder]="'Plot No'"></app-ilogi-input>
    <app-ilogi-input fieldLabel="Land Area" formControlName="landArea" [mandatory]="true"
      [placeholder]="'Land Area'"></app-ilogi-input>
    <app-ilogi-select fieldLabel="Classification" formControlName="classification" [mandatory]="true"
      [placeholder]="'Classification'" [selectOptions]="land_record_details_classification_of_land"></app-ilogi-select>
    <app-ilogi-select fieldLabel="Land Record Details Unit" formControlName="details_unit" [mandatory]="true"
      [placeholder]="'Unit'" [selectOptions]="land_record_details_unit"></app-ilogi-select>

    <app-ilogi-input fieldLabel="Land Floor Building (Sq. Mtr)" formControlName="landFloor"
      [placeholder]="'Land Floor Building (Sq. Mtr)'"></app-ilogi-input>
    <app-ilogi-input fieldLabel="Sanitary Latrine Count" formControlName="sanitaryCount"
      [placeholder]="'Sanitary Latrine Count'"></app-ilogi-input>
    <app-ilogi-input fieldLabel="Boundary Wall (Mtr)" formControlName="boundaryWall"
      [placeholder]="'Boundary Wall (Mtr)'"></app-ilogi-input>
    <app-ilogi-input fieldLabel="Building Sq. Mtr" formControlName="buildingSqMtr"
      [placeholder]="'Building Sq. Mtr'"></app-ilogi-input>
  </div>

  <div class="mt-3">
    <app-dynamic-table [title1]="'Type'" [title2]="'Amount'" [rows]="projectCostRows" [formGroup]="form">
    </app-dynamic-table>
  </div>

  <app-dynamic-table [title1]="'Type'" [title2]="'Count'" [rows]="employmentRows" [formGroup]="form">
  </app-dynamic-table>

  <div class="form-grid">
    <app-ilogi-input fieldLabel="Annual Turnover" formControlName="annualTurnover" [mandatory]="true"
      [placeholder]="'Annual Turnover'"></app-ilogi-input>
    <app-ilogi-input fieldLabel="Working Session" formControlName="workingSession" [mandatory]="true"
      [placeholder]="'Working Session'"></app-ilogi-input>
    <app-ilogi-input fieldLabel="Product Manufacturing Process" formControlName="process" [mandatory]="true"
      [placeholder]="'Product Manufacturing Process'"></app-ilogi-input>

        <app-ilogi-select fieldLabel="Category Enterprise" formControlName="category" [mandatory]="true"
      [placeholder]="'Category Enterprise'" [selectOptions]="category_of_enterprise"></app-ilogi-select>
  </div>

  <div class="form-actions">
    <button type="button" class="btn btn-primary" (click)="onSubmit(true)">
      Save As Draft
    </button>

    <button type="submit" class="btn btn-success" (click)="onSubmit(false)">
      Submit
    </button>
  </div>

</form>