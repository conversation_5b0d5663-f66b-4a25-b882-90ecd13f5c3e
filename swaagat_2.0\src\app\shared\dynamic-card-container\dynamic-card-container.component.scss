// dynamic-card-container.component.scss

:host {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0;
  margin: 0;
  min-height: 100vh;
  gap: 5vh;
  align-items: center;
  justify-content: center;
  background-color: rgb(255, 252, 245);
}

.section-title h2 {
  text-align: center;
  font-size: 2.8rem;
  font-weight: 700;
  color: #1a1a2e;
  margin-bottom: 1.5rem;
  background: linear-gradient(90deg, #e563f1, #f65c9f);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.5px;
}

.section-title p.subtitle {
  text-align: center;
  font-size: 1.2rem;
  color: #4b5563;
  margin-bottom: 3rem;
  // max-width: 700px;
  line-height: 1.6;
  margin-left: auto;
  margin-right: auto;
}

.cards-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  justify-content: center;
  padding: 3rem 1.5rem;
  max-width: 1800px;
  margin: 0 auto;
  width: 100%;

  @media (max-width: 768px) {
    padding: 2rem 1rem;
    gap: 1.5rem;
  }
}

.card-container {
  position: relative;
  max-width: 300px;
  width: 100%;
  // Remove gradient background from main container
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: white;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  flex: 1 1 300px;

  display: flex;
  flex-direction: column;
  min-height: 400px;
}

.card-container:hover {
  transform: translateY(-10px) scale(1.01);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
}

.card-container:hover .card-glow {
  opacity: 0.8;
}

.card-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.12) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.card-header {
  padding: 2rem 2rem 1rem;
  position: relative;
  z-index: 2;
  // Add gradient background to header only
  background: var(--card-gradient);
  border-radius: 20px 20px 0 0; // Round top corners only
}

.icon-container {
  margin-bottom: 1.5rem;
}

.card-icon {
  font-size: 2.5rem;
  color: rgba(255, 255, 255, 0.95);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.header-content {
  animation: slideUp 0.6s ease-out;
}

.card-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0 0 0.75rem 0;
  letter-spacing: -0.025em;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.card-description {
  font-size: 1rem;
  margin: 0;
  opacity: 0.9;
  line-height: 1.5;
  font-weight: 400;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.card-body {
  padding: 0 2rem 2rem;
  position: relative;
  z-index: 2;
  // Add white background to body
  background: white;
  color: #333; // Dark text for better contrast on white background

  display: flex;
  flex-direction: column;
  flex-grow: 1;
  border-radius: 0 0 20px 20px; // Round bottom corners only
}

.features-list {
  margin-bottom: 2rem;
  flex-grow: 1;
  padding-top: 1.5rem; // Add some spacing from header
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  animation: slideUp 0.6s ease-out;
  animation-fill-mode: both;
}

.feature-item:nth-child(1) { animation-delay: 0.1s; }
.feature-item:nth-child(2) { animation-delay: 0.2s; }
.feature-item:nth-child(3) { animation-delay: 0.3s; }
.feature-item:nth-child(4) { animation-delay: 0.4s; }

.feature-icon {
  width: 20px;
  height: 20px;
  margin-right: 1rem;
  flex-shrink: 0;
  color: #666; // Darker color for white background
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.feature-icon svg {
  width: 100%;
  height: 100%;
}

.feature-text {
  font-size: 0.95rem;
  font-weight: 500;
  opacity: 0.85;
  line-height: 1.4;
  color: #555; // Dark text for white background
}

.card-button {
  width: 100%;
  padding: 1rem 1.5rem;
  background: rgba(0, 0, 0, 0.05); // Light background for white section
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  color: #333; // Dark text
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  text-shadow: none; // Remove text shadow for dark text

  margin-top: 0;
}

.card-button:hover {
  background: rgba(0, 0, 0, 0.1);
  border-color: rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-button:active {
  transform: translateY(0);
}

.button-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.card-button:active .button-ripple {
  width: 300px;
  height: 300px;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .card-container {
    max-width: 100%;
    border-radius: 16px;
    min-height: 350px;
  }

  .card-header {
    border-radius: 16px 16px 0 0;
  }

  .card-body {
    border-radius: 0 0 16px 16px;
  }

  .card-title {
    font-size: 1.5rem;
  }

  .card-description,
  .feature-text {
    font-size: 0.9rem;
  }

  .card-icon {
    font-size: 2.2rem;
  }

  .feature-icon {
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 480px) {
  .card-container {
    border-radius: 14px;
    min-height: 320px;
  }

  .card-header {
    border-radius: 14px 14px 0 0;
  }

  .card-body {
    border-radius: 0 0 14px 14px;
  }

  .card-title {
    font-size: 1.35rem;
  }

  .card-button {
    font-size: 0.95rem;
    padding: 0.9rem 1.25rem;
  }
}

.icon-container .card-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  margin-bottom: 1.5rem;

  svg {
    width: 100%;
    height: 100%;
    max-width: 48px;
    max-height: 48px;
    fill: rgba(255, 255, 255, 0.9);
    stroke: rgba(255, 255, 255, 0.9);
    stroke-width: 2;
  }
}