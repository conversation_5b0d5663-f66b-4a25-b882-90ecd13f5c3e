.clarification-required-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  overflow-x: auto;
}
.table-title {
  font-size: 18px;
  font-weight: 600;
  color: #212529;
  margin: 0 0 20px 0;
}

.table-responsive-wrapper {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}
table {
  width: 100%;
  min-width: 650px;
  border-collapse: collapse;
  margin-top: 0;
}
th,
td {
  text-align: left;
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  font-size: 14px;
  color: #495057;
  white-space: nowrap;
}
th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #212529;
  text-transform: uppercase;
  font-size: 12px;
}
tbody tr:last-child td {
  border-bottom: none;
}
.document-missing-tag {
  background-color: #dc3545;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  display: inline-block;
  white-space: nowrap;
}
.upload-button {
  background-color: #0d6efd;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}
.upload-button:hover {
  background-color: #0a58ca;
}

@media (max-width: 768px) {
  .clarification-required-container {
    padding: 16px;
  }
  .table-title {
    font-size: 16px;
    margin-bottom: 16px;
  }

  table {
    min-width: 550px;
  }
  th,
  td {
    padding: 10px 12px;
    font-size: 13px;
  }
  th {
    font-size: 11px;
  }
  .document-missing-tag {
    font-size: 10px;
    padding: 3px 6px;
  }
  .upload-button {
    padding: 6px 12px;
    font-size: 13px;
    gap: 6px;
  }
}

@media (max-width: 480px) {
  .clarification-required-container {
    padding: 12px;
  }
  .table-title {
    font-size: 14px;
    margin-bottom: 12px;
  }
  table {
    min-width: 450px;
  }
  th,
  td {
    padding: 8px 10px;
    font-size: 12px;
  }
  th {
    font-size: 10px;
  }
  .document-missing-tag {
    font-size: 9px;
    padding: 2px 5px;
  }
  .upload-button {
    padding: 5px 10px;
    font-size: 12px;
    gap: 5px;
  }
}
