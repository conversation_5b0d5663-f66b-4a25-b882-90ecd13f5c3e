// This file was generated by running 'ng generate @angular/material:theme-color'.
// Proceed with caution if making changes to this file.

@use 'sass:map';
@use '@angular/material' as mat;

// Note: Color palettes are generated from primary: b4001e, secondary: #d1273c, tertiary: #009c7b, neutral: #212121, neutral variant: #b3b3b3
$_palettes: (
  primary: (
    0: #000000,
    10: #410005,
    20: #68000d,
    25: #7d0011,
    30: #930016,
    35: #a9001b,
    40: #bd0d23,
    50: #e12f38,
    60: #ff5354,
    70: #ff8984,
    80: #ffb3af,
    90: #ffdad7,
    95: #ffedeb,
    98: #fff8f7,
    99: #fffbff,
    100: #ffffff,
  ),
  secondary: (
    0: #000000,
    10: #410008,
    20: #680013,
    25: #7d0019,
    30: #92001f,
    35: #a80026,
    40: #bb122f,
    50: #df3245,
    60: #ff525e,
    70: #ff888a,
    80: #ffb3b2,
    90: #ffdad9,
    95: #ffedec,
    98: #fff8f7,
    99: #fffbff,
    100: #ffffff,
  ),
  tertiary: (
    0: #000000,
    10: #002117,
    20: #00382a,
    25: #004434,
    30: #00513f,
    35: #005e49,
    40: #006b54,
    50: #00876a,
    60: #18a382,
    70: #43bf9c,
    80: #63dbb6,
    90: #81f8d2,
    95: #baffe5,
    98: #e6fff3,
    99: #f3fff8,
    100: #ffffff,
  ),
  neutral: (
    0: #000000,
    10: #1b1c1c,
    20: #303030,
    25: #3c3b3b,
    30: #474746,
    35: #535252,
    40: #5f5e5e,
    50: #787776,
    60: #929090,
    70: #adabaa,
    80: #c8c6c5,
    90: #e5e2e1,
    95: #f3f0ef,
    98: #fcf9f8,
    99: #fffbfb,
    100: #ffffff,
    4: #0e0e0e,
    6: #131313,
    12: #202020,
    17: #2a2a2a,
    22: #353535,
    24: #393939,
    87: #dcd9d9,
    92: #eae7e7,
    94: #f0eded,
    96: #f6f3f2,
  ),
  neutral-variant: (
    0: #000000,
    10: #1a1c1c,
    20: #2f3131,
    25: #3a3c3c,
    30: #464747,
    35: #515353,
    40: #5d5e5f,
    50: #767777,
    60: #909191,
    70: #ababab,
    80: #c7c6c6,
    90: #e3e2e2,
    95: #f1f0f0,
    98: #faf9f9,
    99: #fdfcfc,
    100: #ffffff,
  ),
  error: (
    0: #000000,
    10: #410002,
    20: #690005,
    25: #7e0007,
    30: #93000a,
    35: #a80710,
    40: #ba1a1a,
    50: #de3730,
    60: #ff5449,
    70: #ff897d,
    80: #ffb4ab,
    90: #ffdad6,
    95: #ffedea,
    98: #fff8f7,
    99: #fffbff,
    100: #ffffff,
  ),
);

$_rest: (
  secondary: map.get($_palettes, secondary),
  neutral: map.get($_palettes, neutral),
  neutral-variant: map.get($_palettes,  neutral-variant),
  error: map.get($_palettes, error),
);

$primary-palette: map.merge(map.get($_palettes, primary), $_rest);
$tertiary-palette: map.merge(map.get($_palettes, tertiary), $_rest);